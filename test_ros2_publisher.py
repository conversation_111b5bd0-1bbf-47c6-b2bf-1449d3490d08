#!/usr/bin/env python3

"""
ROS 2 Test Publisher
Publishes test messages that should be received by ROS 1 via the bridge
"""

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import time

class ROS2TestPublisher(Node):
    def __init__(self):
        super().__init__('ros2_test_publisher')
        self.publisher = self.create_publisher(String, 'to_ros1', 10)
        self.timer = self.create_timer(1.0, self.timer_callback)  # 1 Hz
        self.counter = 0
        self.get_logger().info('ROS 2 Test Publisher started')
        self.get_logger().info('Publishing to /to_ros1 topic')
    
    def timer_callback(self):
        msg = String()
        msg.data = f'Hello from ROS 2! Message #{self.counter}'
        self.publisher.publish(msg)
        self.get_logger().info(f'Published: {msg.data}')
        self.counter += 1

def main(args=None):
    rclpy.init(args=args)
    node = ROS2TestPublisher()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
