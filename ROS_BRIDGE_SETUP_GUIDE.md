# ROS 1 ↔ ROS 2 Bridge Setup Guide

## Network Configuration ✅

Your VMs are already properly configured:
- **Ubuntu 20.04 (ROS 1 Noetic)**: *************
- **Ubuntu 22.04 (ROS 2 Humble)**: *************
- **Network**: Both VMs can ping each other ✅

## Option A: ros1_bridge (Recommended)

### Setup on Ubuntu 22.04 VM

1. **Copy and run the setup script:**
   ```bash
   # Transfer setup_ros1_bridge_ubuntu22.sh to Ubuntu 22.04 VM
   chmod +x setup_ros1_bridge_ubuntu22.sh
   ./setup_ros1_bridge_ubuntu22.sh
   ```

2. **Usage:**
   ```bash
   # Terminal 1 - ROS 1 Master (on Ubuntu 20.04 VM)
   export ROS_MASTER_URI=http://*************:11311
   export ROS_IP=*************
   source /opt/ros/noetic/setup.bash
   roscore
   
   # Terminal 2 - Bridge (on Ubuntu 22.04 VM)
   export ROS_MASTER_URI=http://*************:11311
   export ROS_IP=*************
   source /opt/ros/noetic/setup.bash
   source /opt/ros/humble/setup.bash
   source ~/ros1_bridge_ws/install/setup.bash
   ros2 run ros1_bridge dynamic_bridge
   
   # Terminal 3 - ROS 2 nodes (on Ubuntu 22.04 VM)
   source /opt/ros/humble/setup.bash
   ros2 topic list
   ```

## Option B: Network Bridge (Separate VMs)

### Setup on Ubuntu 20.04 VM (Current)

```bash
chmod +x setup_network_bridge.sh
./setup_network_bridge.sh
```

### Setup on Ubuntu 22.04 VM

```bash
# Transfer and run the same script
chmod +x setup_network_bridge.sh
./setup_network_bridge.sh
```

### Usage:

```bash
# Ubuntu 20.04 VM - ROS 1 Bridge Server
source /opt/ros/noetic/setup.bash
roscore &
roslaunch ~/ros_bridge_ws/launch/ros1_bridge_server.launch

# Ubuntu 22.04 VM - ROS 2 Bridge Client
source /opt/ros/humble/setup.bash
python3 ~/ros_bridge_ws/scripts/ros2_bridge_client.py
```

## Testing the Bridge

### Test Publishers

**On Ubuntu 20.04 (ROS 1):**
```bash
source /opt/ros/noetic/setup.bash
python3 test_ros1_publisher.py
```

**On Ubuntu 22.04 (ROS 2):**
```bash
source /opt/ros/humble/setup.bash
python3 test_ros2_publisher.py
```

### Verify Communication

**ROS 1 side:**
```bash
source /opt/ros/noetic/setup.bash
rostopic list
rostopic echo /to_ros1
```

**ROS 2 side:**
```bash
source /opt/ros/humble/setup.bash
ros2 topic list
ros2 topic echo /from_ros1
```

## Troubleshooting

### Network Issues
```bash
# Test connectivity
ping *************  # from Ubuntu 20.04
ping *************  # from Ubuntu 22.04

# Check ports
netstat -tlnp | grep 11311  # ROS Master
netstat -tlnp | grep 9090   # ROSBridge
```

### ROS Environment
```bash
# Check ROS environment
echo $ROS_MASTER_URI
echo $ROS_IP
printenv | grep ROS
```

## Performance Comparison

| Method | Latency | Setup Complexity | Maintenance |
|--------|---------|------------------|-------------|
| ros1_bridge | Low | Medium | Low |
| Network Bridge | Medium | Low | Medium |

**Recommendation**: Use ros1_bridge for better performance and native ROS integration.
