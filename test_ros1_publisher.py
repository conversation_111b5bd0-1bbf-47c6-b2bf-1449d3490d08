#!/usr/bin/env python3

"""
ROS 1 Test Publisher
Publishes test messages that should be received by ROS 2 via the bridge
"""

import rospy
from std_msgs.msg import String
import time

def main():
    rospy.init_node('ros1_test_publisher', anonymous=True)
    pub = rospy.Publisher('/from_ros2', String, queue_size=10)
    
    rate = rospy.Rate(1)  # 1 Hz
    counter = 0
    
    rospy.loginfo("ROS 1 Test Publisher started")
    rospy.loginfo("Publishing to /from_ros2 topic")
    
    while not rospy.is_shutdown():
        msg = String()
        msg.data = f"Hello from ROS 1! Message #{counter}"
        pub.publish(msg)
        rospy.loginfo(f"Published: {msg.data}")
        counter += 1
        rate.sleep()

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        pass
