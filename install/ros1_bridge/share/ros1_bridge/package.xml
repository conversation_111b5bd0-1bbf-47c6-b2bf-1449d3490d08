<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>ros1_bridge</name>
  <version>0.9.7</version>
  <description>A simple bridge between ROS 1 and ROS 2</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>
  <license>Apache License 2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_index_python</buildtool_depend>
  <buildtool_depend>python3-catkin-pkg-modules</buildtool_depend>
  <buildtool_depend>rosidl_cmake</buildtool_depend>
  <buildtool_depend>rosidl_parser</buildtool_depend>

  <build_depend>builtin_interfaces</build_depend>
  <build_depend>pkg-config</build_depend>
  <build_depend>python3-yaml</build_depend>
  <build_depend>rclcpp</build_depend>
  <build_depend>rcutils</build_depend>
  <build_depend>rmw_implementation_cmake</build_depend>
  <build_depend>std_msgs</build_depend>

  <buildtool_export_depend>pkg-config</buildtool_export_depend>

  <exec_depend>builtin_interfaces</exec_depend>
  <exec_depend>python3-yaml</exec_depend>
  <exec_depend>rclcpp</exec_depend>
  <exec_depend>rcutils</exec_depend>
  <exec_depend>std_msgs</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>
  <test_depend>demo_nodes_cpp</test_depend>
  <test_depend>diagnostic_msgs</test_depend>
  <test_depend>launch</test_depend>
  <test_depend>launch_testing</test_depend>
  <test_depend>launch_testing_ament_cmake</test_depend>
  <test_depend>launch_testing_ros</test_depend>
  <test_depend>ros2run</test_depend>

  <group_depend>rosidl_interface_packages</group_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
