#!/bin/bash

# Network-based ROS 1 <-> ROS 2 Bridge Setup
# This approach uses rosbridge_suite for communication between separate VMs

set -e

echo "=== Network-based ROS Bridge Setup ==="
echo "This script sets up rosbridge_suite for ROS 1 <-> ROS 2 communication"
echo "across separate VMs."
echo ""

# Detect ROS version
if [ -f "/opt/ros/noetic/setup.bash" ]; then
    ROS_VERSION="noetic"
    ROS_DISTRO="ROS 1 Noetic"
elif [ -f "/opt/ros/humble/setup.bash" ]; then
    ROS_VERSION="humble"
    ROS_DISTRO="ROS 2 Humble"
else
    echo "Error: No ROS installation detected!"
    exit 1
fi

echo "Detected: $ROS_DISTRO"
echo ""

# Update system
sudo apt update

if [ "$ROS_VERSION" = "noetic" ]; then
    echo "Setting up ROS 1 Noetic side..."
    
    # Install rosbridge_suite for ROS 1
    sudo apt install -y ros-noetic-rosbridge-suite
    sudo apt install -y ros-noetic-tf2-web-republisher
    
    # Create launch file for ROS 1 bridge server
    mkdir -p ~/ros_bridge_ws/launch
    
    cat > ~/ros_bridge_ws/launch/ros1_bridge_server.launch << 'EOF'
<launch>
    <!-- ROSBridge WebSocket Server -->
    <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
        <arg name="port" value="9090"/>
        <arg name="address" value="0.0.0.0"/>
    </include>
    
    <!-- TF2 Web Republisher -->
    <node name="tf2_web_republisher" pkg="tf2_web_republisher" type="tf2_web_republisher" />
</launch>
EOF

    echo "ROS 1 setup complete!"
    echo ""
    echo "To run ROS 1 bridge server:"
    echo "source /opt/ros/noetic/setup.bash"
    echo "roslaunch ~/ros_bridge_ws/launch/ros1_bridge_server.launch"
    echo ""
    echo "The bridge will be available at ws://*************:9090"

elif [ "$ROS_VERSION" = "humble" ]; then
    echo "Setting up ROS 2 Humble side..."
    
    # Install rosbridge_suite for ROS 2
    sudo apt install -y ros-humble-rosbridge-suite
    sudo apt install -y python3-pip
    pip3 install roslibpy
    
    # Create Python script for ROS 2 client
    mkdir -p ~/ros_bridge_ws/scripts
    
    cat > ~/ros_bridge_ws/scripts/ros2_bridge_client.py << 'EOF'
#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import roslibpy
import json
import threading
import time

class ROS2BridgeClient(Node):
    def __init__(self):
        super().__init__('ros2_bridge_client')
        
        # ROS 1 connection via rosbridge
        self.ros1_client = roslibpy.Ros(host='*************', port=9090)
        self.ros1_client.run()
        
        # ROS 2 publishers and subscribers
        self.ros2_pub = self.create_publisher(String, 'from_ros1', 10)
        self.ros2_sub = self.create_subscription(String, 'to_ros1', self.ros2_callback, 10)
        
        # ROS 1 publisher and subscriber via rosbridge
        self.ros1_pub = roslibpy.Topic(self.ros1_client, '/to_ros1', 'std_msgs/String')
        self.ros1_sub = roslibpy.Topic(self.ros1_client, '/from_ros2', 'std_msgs/String')
        self.ros1_sub.subscribe(self.ros1_callback)
        
        self.get_logger().info('ROS 2 Bridge Client started')
        self.get_logger().info('Connected to ROS 1 at *************:9090')
    
    def ros2_callback(self, msg):
        """Forward ROS 2 messages to ROS 1"""
        ros1_msg = {'data': msg.data}
        self.ros1_pub.publish(roslibpy.Message(ros1_msg))
        self.get_logger().info(f'Forwarded to ROS 1: {msg.data}')
    
    def ros1_callback(self, message):
        """Forward ROS 1 messages to ROS 2"""
        ros2_msg = String()
        ros2_msg.data = message['data']
        self.ros2_pub.publish(ros2_msg)
        self.get_logger().info(f'Received from ROS 1: {message["data"]}')

def main(args=None):
    rclpy.init(args=args)
    node = ROS2BridgeClient()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
EOF

    chmod +x ~/ros_bridge_ws/scripts/ros2_bridge_client.py
    
    echo "ROS 2 setup complete!"
    echo ""
    echo "To run ROS 2 bridge client:"
    echo "source /opt/ros/humble/setup.bash"
    echo "python3 ~/ros_bridge_ws/scripts/ros2_bridge_client.py"

fi

echo ""
echo "=== Network Configuration ==="
echo "Make sure both VMs can communicate:"
echo "- Ubuntu 20.04 (ROS 1): *************"
echo "- Ubuntu 22.04 (ROS 2): *************"
echo ""
echo "Test with: ping *************  # from ROS 1 VM"
echo "Test with: ping *************  # from ROS 2 VM"
echo ""
