[0.000000] (-) TimerEvent: {}
[0.000282] (ros1_bridge) JobQueued: {'identifier': 'ros1_bridge', 'dependencies': OrderedDict()}
[0.000308] (ros1_bridge) JobStarted: {'identifier': 'ros1_bridge'}
[0.033343] (ros1_bridge) JobProgress: {'identifier': 'ros1_bridge', 'progress': 'cmake'}
[0.033499] (ros1_bridge) Command: {'cmd': ['/usr/bin/cmake', '/home/<USER>/bridge_ws/src/ros1_bridge', '-DCMAKE_INSTALL_PREFIX=/home/<USER>/bridge_ws/install/ros1_bridge'], 'cwd': '/home/<USER>/bridge_ws/build/ros1_bridge', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('SSH_AGENT_PID', '1593'), ('ROS_PACKAGE_PATH', '/opt/ros/noetic/share'), ('XDG_SESSION_TYPE', 'x11'), ('ROS_ETC_DIR', '/opt/ros/noetic/etc/ros'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/x86_64-linux-gnu:/opt/ros/foxy/lib:/opt/ros/noetic/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/bridge_ws/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('MANAGERPID', '1495'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'foxy'), ('LOGNAME', 'mehdi'), ('JOURNAL_STREAM', '8:55802'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/ros/noetic/lib/pkgconfig'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/foxy/bin:/opt/ros/noetic/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('SESSION_MANAGER', 'local/ubuntu:@/tmp/.ICE-unix/1628,unix/ubuntu:/tmp/.ICE-unix/1628'), ('INVOCATION_ID', '9c03585a47054d89b97b34bc48f12474'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/d8af12fa_0ba9_4dab_bb5a_fb890aa18bad'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('XMODIFIERS', '@im=ibus'), ('ROSLISP_PACKAGE_DIRECTORIES', ''), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.73'), ('ROS_ROOT', '/opt/ros/noetic/share/ros'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/foxy'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('ROS_MASTER_URI', 'http://localhost:11311'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/bridge_ws/build/ros1_bridge'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/foxy/lib/python3.8/site-packages:/opt/ros/noetic/lib/python3/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6003'), ('CMAKE_PREFIX_PATH', '/opt/ros/foxy:/opt/ros/noetic')]), 'shell': False}
[0.099317] (-) TimerEvent: {}
[0.199796] (-) TimerEvent: {}
[0.300105] (-) TimerEvent: {}
[0.357926] (ros1_bridge) StdoutLine: {'line': b'-- The C compiler identification is GNU 9.4.0\n'}
[0.400186] (-) TimerEvent: {}
[0.500479] (-) TimerEvent: {}
[0.542631] (ros1_bridge) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 9.4.0\n'}
[0.574986] (ros1_bridge) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc\n'}
[0.600551] (-) TimerEvent: {}
[0.655257] (ros1_bridge) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc -- works\n'}
[0.659862] (ros1_bridge) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[0.700713] (-) TimerEvent: {}
[0.770109] (ros1_bridge) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[0.778360] (ros1_bridge) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[0.779349] (ros1_bridge) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[0.787592] (ros1_bridge) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++\n'}
[0.801239] (-) TimerEvent: {}
[0.898178] (ros1_bridge) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ -- works\n'}
[0.899253] (ros1_bridge) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[0.902099] (-) TimerEvent: {}
[1.002373] (-) TimerEvent: {}
[1.047803] (ros1_bridge) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[1.063862] (ros1_bridge) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[1.064297] (ros1_bridge) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[1.071277] (ros1_bridge) StdoutLine: {'line': b'-- Found rmw: 1.0.4 (/opt/ros/foxy/share/rmw/cmake)\n'}
[1.102436] (-) TimerEvent: {}
[1.112286] (ros1_bridge) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3") \n'}
[1.112531] (ros1_bridge) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[1.203099] (-) TimerEvent: {}
[1.303659] (-) TimerEvent: {}
[1.404055] (-) TimerEvent: {}
[1.504409] (-) TimerEvent: {}
[1.506382] (ros1_bridge) StdoutLine: {'line': b'-- Found rclcpp: 2.4.3 (/opt/ros/foxy/share/rclcpp/cmake)\n'}
[1.604488] (-) TimerEvent: {}
[1.612349] (ros1_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[1.629914] (ros1_bridge) StdoutLine: {'line': b'-- Found rosidl_adapter: 1.3.1 (/opt/ros/foxy/share/rosidl_adapter/cmake)\n'}
[1.704747] (-) TimerEvent: {}
[1.805102] (-) TimerEvent: {}
[1.806559] (ros1_bridge) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "1.1.1f")  \n'}
[1.837190] (ros1_bridge) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/foxy/include  \n'}
[1.905725] (-) TimerEvent: {}
[1.953223] (ros1_bridge) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[2.006310] (-) TimerEvent: {}
[2.059811] (ros1_bridge) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 1.0.4 (/opt/ros/foxy/share/rmw_implementation_cmake/cmake)\n'}
[2.063784] (ros1_bridge) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[2.067761] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[2.106476] (-) TimerEvent: {}
[2.197229] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[2.197735] (ros1_bridge) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[2.206640] (-) TimerEvent: {}
[2.289727] (ros1_bridge) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed\n'}
[2.290394] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread_create in pthreads\n'}
[2.307014] (-) TimerEvent: {}
[2.396118] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread_create in pthreads - not found\n'}
[2.397530] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread_create in pthread\n'}
[2.407327] (-) TimerEvent: {}
[2.508902] (-) TimerEvent: {}
[2.546965] (ros1_bridge) StdoutLine: {'line': b'-- Looking for pthread_create in pthread - found\n'}
[2.547950] (ros1_bridge) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[2.609145] (-) TimerEvent: {}
[2.709664] (-) TimerEvent: {}
[2.781298] (ros1_bridge) StdoutLine: {'line': b'-- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") \n'}
[2.788241] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'roscpp'\n"}
[2.809716] (-) TimerEvent: {}
[2.819851] (ros1_bridge) StdoutLine: {'line': b'--   Found roscpp, version 1.17.4\n'}
[2.910195] (-) TimerEvent: {}
[2.914378] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'std_msgs'\n"}
[2.959076] (ros1_bridge) StdoutLine: {'line': b'--   Found std_msgs, version 0.5.14\n'}
[3.010289] (-) TimerEvent: {}
[3.110755] (-) TimerEvent: {}
[3.211111] (-) TimerEvent: {}
[3.312061] (-) TimerEvent: {}
[3.413060] (-) TimerEvent: {}
[3.514172] (-) TimerEvent: {}
[3.598183] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'actionlib'\n"}
[3.614282] (-) TimerEvent: {}
[3.623548] (ros1_bridge) StdoutLine: {'line': b'--   Found actionlib, version 1.14.3\n'}
[3.714642] (-) TimerEvent: {}
[3.725561] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'actionlib_msgs'\n"}
[3.752968] (ros1_bridge) StdoutLine: {'line': b'--   Found actionlib_msgs, version 1.13.2\n'}
[3.815074] (-) TimerEvent: {}
[3.834139] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'actionlib_tutorials'\n"}
[3.853866] (ros1_bridge) StdoutLine: {'line': b'--   Found actionlib_tutorials, version 0.2.0\n'}
[3.915286] (-) TimerEvent: {}
[3.922241] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'bond'\n"}
[3.945371] (ros1_bridge) StdoutLine: {'line': b'--   Found bond, version 1.8.7\n'}
[4.009949] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'control_msgs'\n"}
[4.015386] (-) TimerEvent: {}
[4.029623] (ros1_bridge) StdoutLine: {'line': b'--   Found control_msgs, version 1.5.2\n'}
[4.090238] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'controller_manager_msgs'\n"}
[4.113063] (ros1_bridge) StdoutLine: {'line': b'--   Found controller_manager_msgs, version 0.20.0\n'}
[4.115455] (-) TimerEvent: {}
[4.212280] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'diagnostic_msgs'\n"}
[4.215560] (-) TimerEvent: {}
[4.232752] (ros1_bridge) StdoutLine: {'line': b'--   Found diagnostic_msgs, version 1.13.2\n'}
[4.294421] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'dynamic_reconfigure'\n"}
[4.315651] (-) TimerEvent: {}
[4.336122] (ros1_bridge) StdoutLine: {'line': b'--   Found dynamic_reconfigure, version 1.7.6\n'}
[4.399341] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'gazebo_msgs'\n"}
[4.415751] (-) TimerEvent: {}
[4.418556] (ros1_bridge) StdoutLine: {'line': b'--   Found gazebo_msgs, version 2.9.3\n'}
[4.493417] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'geometry_msgs'\n"}
[4.515910] (-) TimerEvent: {}
[4.524771] (ros1_bridge) StdoutLine: {'line': b'--   Found geometry_msgs, version 1.13.2\n'}
[4.599541] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'map_msgs'\n"}
[4.616258] (-) TimerEvent: {}
[4.621005] (ros1_bridge) StdoutLine: {'line': b'--   Found map_msgs, version 1.14.2\n'}
[4.705173] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'nav_msgs'\n"}
[4.716626] (-) TimerEvent: {}
[4.729720] (ros1_bridge) StdoutLine: {'line': b'--   Found nav_msgs, version 1.13.2\n'}
[4.817633] (-) TimerEvent: {}
[4.840231] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'pcl_msgs'\n"}
[4.867732] (ros1_bridge) StdoutLine: {'line': b'--   Found pcl_msgs, version 0.3.0\n'}
[4.917696] (-) TimerEvent: {}
[4.933669] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'rosgraph_msgs'\n"}
[4.960430] (ros1_bridge) StdoutLine: {'line': b'--   Found rosgraph_msgs, version 1.11.4\n'}
[5.016943] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'rospy_tutorials'\n"}
[5.018321] (-) TimerEvent: {}
[5.035575] (ros1_bridge) StdoutLine: {'line': b'--   Found rospy_tutorials, version 0.10.3\n'}
[5.097787] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'sensor_msgs'\n"}
[5.118530] (-) TimerEvent: {}
[5.118908] (ros1_bridge) StdoutLine: {'line': b'--   Found sensor_msgs, version 1.13.2\n'}
[5.190388] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'shape_msgs'\n"}
[5.210668] (ros1_bridge) StdoutLine: {'line': b'--   Found shape_msgs, version 1.13.2\n'}
[5.218802] (-) TimerEvent: {}
[5.285921] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'smach_msgs'\n"}
[5.312611] (ros1_bridge) StdoutLine: {'line': b'--   Found smach_msgs, version 2.5.3\n'}
[5.318871] (-) TimerEvent: {}
[5.397173] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'stereo_msgs'\n"}
[5.418944] (-) TimerEvent: {}
[5.428208] (ros1_bridge) StdoutLine: {'line': b'--   Found stereo_msgs, version 1.13.2\n'}
[5.502696] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'tf'\n"}
[5.519245] (-) TimerEvent: {}
[5.538093] (ros1_bridge) StdoutLine: {'line': b'--   Found tf, version 1.13.4\n'}
[5.619345] (-) TimerEvent: {}
[5.636058] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'tf2_msgs'\n"}
[5.660687] (ros1_bridge) StdoutLine: {'line': b'--   Found tf2_msgs, version 0.7.10\n'}
[5.719635] (-) TimerEvent: {}
[5.743801] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'theora_image_transport'\n"}
[5.767716] (ros1_bridge) StdoutLine: {'line': b'--   Found theora_image_transport, version 1.15.0\n'}
[5.819807] (-) TimerEvent: {}
[5.828079] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'trajectory_msgs'\n"}
[5.848094] (ros1_bridge) StdoutLine: {'line': b'--   Found trajectory_msgs, version 1.13.2\n'}
[5.920352] (-) TimerEvent: {}
[5.929602] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'turtle_actionlib'\n"}
[5.956053] (ros1_bridge) StdoutLine: {'line': b'--   Found turtle_actionlib, version 0.2.0\n'}
[6.009654] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'turtlesim'\n"}
[6.020825] (-) TimerEvent: {}
[6.033989] (ros1_bridge) StdoutLine: {'line': b'--   Found turtlesim, version 0.10.3\n'}
[6.114244] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'visualization_msgs'\n"}
[6.121140] (-) TimerEvent: {}
[6.138901] (ros1_bridge) StdoutLine: {'line': b'--   Found visualization_msgs, version 1.13.2\n'}
[6.219431] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'control_toolbox'\n"}
[6.221217] (-) TimerEvent: {}
[6.243023] (ros1_bridge) StdoutLine: {'line': b'--   Found control_toolbox, version 1.19.0\n'}
[6.317002] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'laser_assembler'\n"}
[6.321708] (-) TimerEvent: {}
[6.353072] (ros1_bridge) StdoutLine: {'line': b'--   Found laser_assembler, version 1.7.8\n'}
[6.422428] (-) TimerEvent: {}
[6.455542] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'polled_camera'\n"}
[6.480013] (ros1_bridge) StdoutLine: {'line': b'--   Found polled_camera, version 1.12.1\n'}
[6.522524] (-) TimerEvent: {}
[6.598625] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'roscpp_tutorials'\n"}
[6.622221] (ros1_bridge) StdoutLine: {'line': b'--   Found roscpp_tutorials, version 0.10.3\n'}
[6.622574] (-) TimerEvent: {}
[6.704994] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'rviz'\n"}
[6.722648] (-) TimerEvent: {}
[6.735958] (ros1_bridge) StdoutLine: {'line': b'--   Found rviz, version 1.14.26\n'}
[6.822876] (-) TimerEvent: {}
[6.842827] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'std_srvs'\n"}
[6.873750] (ros1_bridge) StdoutLine: {'line': b'--   Found std_srvs, version 1.11.4\n'}
[6.922979] (-) TimerEvent: {}
[6.976238] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'topic_tools'\n"}
[6.996774] (ros1_bridge) StdoutLine: {'line': b'--   Found topic_tools, version 1.17.4\n'}
[7.023292] (-) TimerEvent: {}
[7.070476] (ros1_bridge) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.9.8 (/opt/ros/foxy/share/ament_lint_auto/cmake)\n'}
[7.075330] (ros1_bridge) StdoutLine: {'line': b'-- Found diagnostic_msgs: 2.0.5 (/opt/ros/foxy/share/diagnostic_msgs/cmake)\n'}
[7.123373] (-) TimerEvent: {}
[7.137671] (ros1_bridge) StdoutLine: {'line': b"-- Checking for module 'roslaunch'\n"}
[7.175645] (ros1_bridge) StdoutLine: {'line': b'--   Found roslaunch, version 1.17.4\n'}
[7.223665] (-) TimerEvent: {}
[7.324025] (-) TimerEvent: {}
[7.387849] (ros1_bridge) StdoutLine: {'line': b'-- Found python_cmake_module: 0.8.1 (/opt/ros/foxy/share/python_cmake_module/cmake)\n'}
[7.407331] (ros1_bridge) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3.5") \n'}
[7.425045] (-) TimerEvent: {}
[7.431786] (ros1_bridge) StdoutLine: {'line': b'-- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.8.so (found suitable version "3.8.10", minimum required is "3.5") \n'}
[7.433116] (ros1_bridge) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[7.433293] (ros1_bridge) StdoutLine: {'line': b'-- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.8\n'}
[7.433370] (ros1_bridge) StdoutLine: {'line': b'-- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.8.so\n'}
[7.472653] (ros1_bridge) StdoutLine: {'line': b'-- Found PythonExtra: .so  \n'}
[7.523209] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[7.525173] (-) TimerEvent: {}
[7.527833] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[7.528114] (ros1_bridge) StdoutLine: {'line': b'-- Configured cppcheck include dirs: \n'}
[7.528201] (ros1_bridge) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[7.532727] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[7.532818] (ros1_bridge) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[7.534984] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'flake8' to check Python code syntax and style conventions\n"}
[7.537410] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[7.539281] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'pep257' to check Python code against some of the style conventions in PEP 257\n"}
[7.543729] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[7.543826] (ros1_bridge) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[7.545704] (ros1_bridge) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[7.625453] (-) TimerEvent: {}
[7.637685] (ros1_bridge) StdoutLine: {'line': b'-- Found action_msgs: 1.0.0 (/opt/ros/foxy/share/action_msgs/cmake)\n'}
[7.685134] (ros1_bridge) StdoutLine: {'line': b'-- Found action_tutorials_interfaces: 0.9.4 (/opt/ros/foxy/share/action_tutorials_interfaces/cmake)\n'}
[7.726111] (-) TimerEvent: {}
[7.752129] (ros1_bridge) StdoutLine: {'line': b'-- Found actionlib_msgs: 2.0.5 (/opt/ros/foxy/share/actionlib_msgs/cmake)\n'}
[7.752450] (ros1_bridge) StdoutLine: {'line': b'-- Found builtin_interfaces: 1.0.0 (/opt/ros/foxy/share/builtin_interfaces/cmake)\n'}
[7.796873] (ros1_bridge) StdoutLine: {'line': b'-- Found composition_interfaces: 1.0.0 (/opt/ros/foxy/share/composition_interfaces/cmake)\n'}
[7.797215] (ros1_bridge) StdoutLine: {'line': b'-- Found diagnostic_msgs: 2.0.5 (/opt/ros/foxy/share/diagnostic_msgs/cmake)\n'}
[7.826750] (-) TimerEvent: {}
[7.855032] (ros1_bridge) StdoutLine: {'line': b'-- Found example_interfaces: 0.9.1 (/opt/ros/foxy/share/example_interfaces/cmake)\n'}
[7.901239] (ros1_bridge) StdoutLine: {'line': b'-- Found geometry_msgs: 2.0.5 (/opt/ros/foxy/share/geometry_msgs/cmake)\n'}
[7.901846] (ros1_bridge) StdoutLine: {'line': b'-- Found libstatistics_collector: 1.0.2 (/opt/ros/foxy/share/libstatistics_collector/cmake)\n'}
[7.927078] (-) TimerEvent: {}
[7.932591] (ros1_bridge) StdoutLine: {'line': b'-- Found lifecycle_msgs: 1.0.0 (/opt/ros/foxy/share/lifecycle_msgs/cmake)\n'}
[7.941032] (ros1_bridge) StdoutLine: {'line': b'-- Found logging_demo: 0.9.4 (/opt/ros/foxy/share/logging_demo/cmake)\n'}
[8.027174] (-) TimerEvent: {}
[8.127553] (-) TimerEvent: {}
[8.228111] (-) TimerEvent: {}
[8.257328] (ros1_bridge) StdoutLine: {'line': b'-- Found map_msgs: 2.0.2 (/opt/ros/foxy/share/map_msgs/cmake)\n'}
[8.257606] (ros1_bridge) StdoutLine: {'line': b'-- Found nav_msgs: 2.0.5 (/opt/ros/foxy/share/nav_msgs/cmake)\n'}
[8.328180] (-) TimerEvent: {}
[8.355467] (ros1_bridge) StdoutLine: {'line': b'-- Found pcl_msgs: 1.0.0 (/opt/ros/foxy/share/pcl_msgs/cmake)\n'}
[8.381115] (ros1_bridge) StdoutLine: {'line': b'-- Found pendulum_msgs: 0.9.4 (/opt/ros/foxy/share/pendulum_msgs/cmake)\n'}
[8.381336] (ros1_bridge) StdoutLine: {'line': b'-- Found rcl_interfaces: 1.0.0 (/opt/ros/foxy/share/rcl_interfaces/cmake)\n'}
[8.415316] (ros1_bridge) StdoutLine: {'line': b'-- Found rmw_dds_common: 1.0.3 (/opt/ros/foxy/share/rmw_dds_common/cmake)\n'}
[8.415649] (ros1_bridge) StdoutLine: {'line': b'-- Found rosgraph_msgs: 1.0.0 (/opt/ros/foxy/share/rosgraph_msgs/cmake)\n'}
[8.415956] (ros1_bridge) StdoutLine: {'line': b'-- Found sensor_msgs: 2.0.5 (/opt/ros/foxy/share/sensor_msgs/cmake)\n'}
[8.428235] (-) TimerEvent: {}
[8.497480] (ros1_bridge) StdoutLine: {'line': b'-- Found shape_msgs: 2.0.5 (/opt/ros/foxy/share/shape_msgs/cmake)\n'}
[8.498525] (ros1_bridge) StdoutLine: {'line': b'-- Found statistics_msgs: 1.0.0 (/opt/ros/foxy/share/statistics_msgs/cmake)\n'}
[8.499554] (ros1_bridge) StdoutLine: {'line': b'-- Found std_msgs: 2.0.5 (/opt/ros/foxy/share/std_msgs/cmake)\n'}
[8.528359] (-) TimerEvent: {}
[8.531398] (ros1_bridge) StdoutLine: {'line': b'-- Found std_srvs: 2.0.5 (/opt/ros/foxy/share/std_srvs/cmake)\n'}
[8.628878] (-) TimerEvent: {}
[8.668585] (ros1_bridge) StdoutLine: {'line': b'-- Found stereo_msgs: 2.0.5 (/opt/ros/foxy/share/stereo_msgs/cmake)\n'}
[8.729315] (-) TimerEvent: {}
[8.830190] (-) TimerEvent: {}
[8.834302] (ros1_bridge) StdoutLine: {'line': b'-- Found tf2_msgs: 0.13.14 (/opt/ros/foxy/share/tf2_msgs/cmake)\n'}
[8.930341] (-) TimerEvent: {}
[8.934924] (ros1_bridge) StdoutLine: {'line': b'-- Found trajectory_msgs: 2.0.5 (/opt/ros/foxy/share/trajectory_msgs/cmake)\n'}
[8.978951] (ros1_bridge) StdoutLine: {'line': b'-- Found turtlesim: 1.2.6 (/opt/ros/foxy/share/turtlesim/cmake)\n'}
[8.979097] (ros1_bridge) StdoutLine: {'line': b'-- Found unique_identifier_msgs: 2.1.3 (/opt/ros/foxy/share/unique_identifier_msgs/cmake)\n'}
[9.030459] (-) TimerEvent: {}
[9.060098] (ros1_bridge) StdoutLine: {'line': b'-- Found visualization_msgs: 2.0.5 (/opt/ros/foxy/share/visualization_msgs/cmake)\n'}
[9.130552] (-) TimerEvent: {}
[9.231161] (-) TimerEvent: {}
[9.326142] (ros1_bridge) StdoutLine: {'line': b'-- Configuring done\n'}
[9.331224] (-) TimerEvent: {}
[9.431562] (-) TimerEvent: {}
[9.531944] (-) TimerEvent: {}
[9.632990] (-) TimerEvent: {}
[9.657284] (ros1_bridge) StdoutLine: {'line': b'-- Generating done\n'}
[9.660929] (ros1_bridge) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/bridge_ws/build/ros1_bridge\n'}
[9.683186] (ros1_bridge) CommandEnded: {'returncode': 0}
[9.685752] (ros1_bridge) JobProgress: {'identifier': 'ros1_bridge', 'progress': 'build'}
[9.690072] (ros1_bridge) Command: {'cmd': ['/usr/bin/cmake', '--build', '/home/<USER>/bridge_ws/build/ros1_bridge', '--', '-j2', '-l2'], 'cwd': '/home/<USER>/bridge_ws/build/ros1_bridge', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('SSH_AGENT_PID', '1593'), ('ROS_PACKAGE_PATH', '/opt/ros/noetic/share'), ('XDG_SESSION_TYPE', 'x11'), ('ROS_ETC_DIR', '/opt/ros/noetic/etc/ros'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/x86_64-linux-gnu:/opt/ros/foxy/lib:/opt/ros/noetic/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/bridge_ws/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('MANAGERPID', '1495'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'foxy'), ('LOGNAME', 'mehdi'), ('JOURNAL_STREAM', '8:55802'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/ros/noetic/lib/pkgconfig'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/foxy/bin:/opt/ros/noetic/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('SESSION_MANAGER', 'local/ubuntu:@/tmp/.ICE-unix/1628,unix/ubuntu:/tmp/.ICE-unix/1628'), ('INVOCATION_ID', '9c03585a47054d89b97b34bc48f12474'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/d8af12fa_0ba9_4dab_bb5a_fb890aa18bad'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('XMODIFIERS', '@im=ibus'), ('ROSLISP_PACKAGE_DIRECTORIES', ''), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.73'), ('ROS_ROOT', '/opt/ros/noetic/share/ros'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/foxy'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('ROS_MASTER_URI', 'http://localhost:11311'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/bridge_ws/build/ros1_bridge'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/foxy/lib/python3.8/site-packages:/opt/ros/noetic/lib/python3/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6003'), ('CMAKE_PREFIX_PATH', '/opt/ros/foxy:/opt/ros/noetic')]), 'shell': False}
[9.733295] (-) TimerEvent: {}
[9.752303] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target test_ros1_client\x1b[0m\n'}
[9.753165] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target test_ros1_server\x1b[0m\n'}
[9.793702] (ros1_bridge) StdoutLine: {'line': b'[  0%] \x1b[32mBuilding CXX object CMakeFiles/test_ros1_client.dir/test/test_ros1_client.cpp.o\x1b[0m\n'}
[9.805113] (ros1_bridge) StdoutLine: {'line': b'[  1%] \x1b[32mBuilding CXX object CMakeFiles/test_ros1_server.dir/test/test_ros1_server.cpp.o\x1b[0m\n'}
[9.833425] (-) TimerEvent: {}
[9.934159] (-) TimerEvent: {}
[10.034711] (-) TimerEvent: {}
[10.136407] (-) TimerEvent: {}
[10.236987] (-) TimerEvent: {}
[10.341725] (-) TimerEvent: {}
[10.444552] (-) TimerEvent: {}
[10.544947] (-) TimerEvent: {}
[10.646180] (-) TimerEvent: {}
[10.747200] (-) TimerEvent: {}
[10.848783] (-) TimerEvent: {}
[10.949174] (-) TimerEvent: {}
[11.050510] (-) TimerEvent: {}
[11.150912] (-) TimerEvent: {}
[11.251273] (-) TimerEvent: {}
[11.352170] (-) TimerEvent: {}
[11.452561] (-) TimerEvent: {}
[11.552966] (-) TimerEvent: {}
[11.654177] (-) TimerEvent: {}
[11.754581] (-) TimerEvent: {}
[11.854974] (-) TimerEvent: {}
[11.955564] (-) TimerEvent: {}
[12.056126] (-) TimerEvent: {}
[12.157133] (-) TimerEvent: {}
[12.258165] (-) TimerEvent: {}
[12.358568] (-) TimerEvent: {}
[12.459329] (-) TimerEvent: {}
[12.559749] (-) TimerEvent: {}
[12.660108] (-) TimerEvent: {}
[12.713228] (ros1_bridge) StdoutLine: {'line': b'[  1%] \x1b[32m\x1b[1mLinking CXX executable test_ros1_client\x1b[0m\n'}
[12.760206] (-) TimerEvent: {}
[12.861174] (-) TimerEvent: {}
[12.913349] (ros1_bridge) StdoutLine: {'line': b'[  1%] Built target test_ros1_client\n'}
[12.939328] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target simple_bridge_1_to_2\x1b[0m\n'}
[12.961320] (-) TimerEvent: {}
[12.974668] (ros1_bridge) StdoutLine: {'line': b'[  1%] \x1b[32m\x1b[1mLinking CXX executable test_ros1_server\x1b[0m\n'}
[13.061461] (-) TimerEvent: {}
[13.162236] (-) TimerEvent: {}
[13.167981] (ros1_bridge) StdoutLine: {'line': b'[  1%] Built target test_ros1_server\n'}
[13.182836] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target simple_bridge_2_to_1\x1b[0m\n'}
[13.262458] (-) TimerEvent: {}
[13.280323] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[32mBuilding CXX object CMakeFiles/simple_bridge_2_to_1.dir/src/simple_bridge_2_to_1.cpp.o\x1b[0m\n'}
[13.280675] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[32mBuilding CXX object CMakeFiles/simple_bridge_1_to_2.dir/src/simple_bridge_1_to_2.cpp.o\x1b[0m\n'}
[13.362735] (-) TimerEvent: {}
[13.463142] (-) TimerEvent: {}
[13.563732] (-) TimerEvent: {}
[13.664205] (-) TimerEvent: {}
[13.764921] (-) TimerEvent: {}
[13.865262] (-) TimerEvent: {}
[13.966150] (-) TimerEvent: {}
[14.066562] (-) TimerEvent: {}
[14.166969] (-) TimerEvent: {}
[14.267657] (-) TimerEvent: {}
[14.368027] (-) TimerEvent: {}
[14.468624] (-) TimerEvent: {}
[14.569007] (-) TimerEvent: {}
[14.670167] (-) TimerEvent: {}
[14.770797] (-) TimerEvent: {}
[14.871156] (-) TimerEvent: {}
[14.971876] (-) TimerEvent: {}
[15.072240] (-) TimerEvent: {}
[15.173119] (-) TimerEvent: {}
[15.274248] (-) TimerEvent: {}
[15.374633] (-) TimerEvent: {}
[15.475225] (-) TimerEvent: {}
[15.575816] (-) TimerEvent: {}
[15.676879] (-) TimerEvent: {}
[15.777761] (-) TimerEvent: {}
[15.878160] (-) TimerEvent: {}
[15.978579] (-) TimerEvent: {}
[16.078973] (-) TimerEvent: {}
[16.179410] (-) TimerEvent: {}
[16.280417] (-) TimerEvent: {}
[16.381091] (-) TimerEvent: {}
[16.481495] (-) TimerEvent: {}
[16.582326] (-) TimerEvent: {}
[16.683118] (-) TimerEvent: {}
[16.783636] (-) TimerEvent: {}
[16.884031] (-) TimerEvent: {}
[16.984425] (-) TimerEvent: {}
[17.084800] (-) TimerEvent: {}
[17.185206] (-) TimerEvent: {}
[17.286160] (-) TimerEvent: {}
[17.390190] (-) TimerEvent: {}
[17.490760] (-) TimerEvent: {}
[17.591190] (-) TimerEvent: {}
[17.691564] (-) TimerEvent: {}
[17.791949] (-) TimerEvent: {}
[17.892307] (-) TimerEvent: {}
[17.993038] (-) TimerEvent: {}
[18.093466] (-) TimerEvent: {}
[18.194529] (-) TimerEvent: {}
[18.294880] (-) TimerEvent: {}
[18.395377] (-) TimerEvent: {}
[18.495896] (-) TimerEvent: {}
[18.560555] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[32m\x1b[1mLinking CXX executable simple_bridge_1_to_2\x1b[0m\n'}
[18.596024] (-) TimerEvent: {}
[18.696504] (-) TimerEvent: {}
[18.796971] (-) TimerEvent: {}
[18.898152] (-) TimerEvent: {}
[18.942806] (ros1_bridge) StdoutLine: {'line': b'[  2%] Built target simple_bridge_1_to_2\n'}
[18.966422] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[34m\x1b[1mGenerating factories for interface types\x1b[0m\n'}
[18.998753] (-) TimerEvent: {}
[19.099125] (-) TimerEvent: {}
[19.199852] (-) TimerEvent: {}
[19.300340] (-) TimerEvent: {}
[19.400931] (-) TimerEvent: {}
[19.501540] (-) TimerEvent: {}
[19.602147] (-) TimerEvent: {}
[19.703079] (-) TimerEvent: {}
[19.803728] (-) TimerEvent: {}
[19.904111] (-) TimerEvent: {}
[20.004999] (-) TimerEvent: {}
[20.106239] (-) TimerEvent: {}
[20.207174] (-) TimerEvent: {}
[20.307562] (-) TimerEvent: {}
[20.407936] (-) TimerEvent: {}
[20.466001] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[32m\x1b[1mLinking CXX executable simple_bridge_2_to_1\x1b[0m\n'}
[20.508113] (-) TimerEvent: {}
[20.608487] (-) TimerEvent: {}
[20.709124] (-) TimerEvent: {}
[20.810188] (-) TimerEvent: {}
[20.825803] (ros1_bridge) StdoutLine: {'line': b'[  2%] Built target simple_bridge_2_to_1\n'}
[20.840239] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target test_ros2_server_cpp\x1b[0m\n'}
[20.909557] (ros1_bridge) StdoutLine: {'line': b'[  2%] \x1b[32mBuilding CXX object CMakeFiles/test_ros2_server_cpp.dir/test/test_ros2_server.cpp.o\x1b[0m\n'}
[20.910309] (-) TimerEvent: {}
[21.010661] (-) TimerEvent: {}
[21.111282] (-) TimerEvent: {}
[21.212660] (-) TimerEvent: {}
[21.313350] (-) TimerEvent: {}
[21.414511] (-) TimerEvent: {}
[21.515527] (-) TimerEvent: {}
[21.615934] (-) TimerEvent: {}
[21.716775] (-) TimerEvent: {}
[21.818148] (-) TimerEvent: {}
[21.918589] (-) TimerEvent: {}
[22.019076] (-) TimerEvent: {}
[22.119826] (-) TimerEvent: {}
[22.220364] (-) TimerEvent: {}
[22.321357] (-) TimerEvent: {}
[22.422331] (-) TimerEvent: {}
[22.522769] (-) TimerEvent: {}
[22.623255] (-) TimerEvent: {}
[22.723830] (-) TimerEvent: {}
[22.824256] (-) TimerEvent: {}
[22.925396] (-) TimerEvent: {}
[23.026156] (-) TimerEvent: {}
[23.126666] (-) TimerEvent: {}
[23.188171] (ros1_bridge) StdoutLine: {'line': b'[  3%] \x1b[32m\x1b[1mLinking CXX executable test_ros2_server_cpp\x1b[0m\n'}
[23.226782] (-) TimerEvent: {}
[23.327204] (-) TimerEvent: {}
[23.385564] (ros1_bridge) StdoutLine: {'line': b'[  3%] Built target test_ros2_server_cpp\n'}
[23.399331] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target simple_bridge\x1b[0m\n'}
[23.427286] (-) TimerEvent: {}
[23.465097] (ros1_bridge) StdoutLine: {'line': b'[  3%] \x1b[32mBuilding CXX object CMakeFiles/simple_bridge.dir/src/simple_bridge.cpp.o\x1b[0m\n'}
[23.527570] (-) TimerEvent: {}
[23.627953] (-) TimerEvent: {}
[23.728938] (-) TimerEvent: {}
[23.830157] (-) TimerEvent: {}
[23.930556] (-) TimerEvent: {}
[24.031186] (-) TimerEvent: {}
[24.131527] (-) TimerEvent: {}
[24.232021] (-) TimerEvent: {}
[24.332412] (-) TimerEvent: {}
[24.433134] (-) TimerEvent: {}
[24.534461] (-) TimerEvent: {}
[24.634779] (-) TimerEvent: {}
[24.735439] (-) TimerEvent: {}
[24.836125] (-) TimerEvent: {}
[24.937303] (-) TimerEvent: {}
[25.038134] (-) TimerEvent: {}
[25.138580] (-) TimerEvent: {}
[25.239505] (-) TimerEvent: {}
[25.340201] (-) TimerEvent: {}
[25.441027] (-) TimerEvent: {}
[25.542232] (-) TimerEvent: {}
[25.642718] (-) TimerEvent: {}
[25.743106] (-) TimerEvent: {}
[25.843627] (-) TimerEvent: {}
[25.944244] (-) TimerEvent: {}
[26.044673] (-) TimerEvent: {}
[26.145233] (-) TimerEvent: {}
[26.246145] (-) TimerEvent: {}
[26.346573] (-) TimerEvent: {}
[26.447610] (-) TimerEvent: {}
[26.548027] (-) TimerEvent: {}
[26.649111] (-) TimerEvent: {}
[26.749635] (-) TimerEvent: {}
[26.850178] (-) TimerEvent: {}
[26.950519] (-) TimerEvent: {}
[27.051006] (-) TimerEvent: {}
[27.151532] (-) TimerEvent: {}
[27.251945] (-) TimerEvent: {}
[27.352475] (-) TimerEvent: {}
[27.453480] (-) TimerEvent: {}
[27.554217] (-) TimerEvent: {}
[27.654788] (-) TimerEvent: {}
[27.755203] (-) TimerEvent: {}
[27.855977] (-) TimerEvent: {}
[27.957060] (-) TimerEvent: {}
[28.058132] (-) TimerEvent: {}
[28.158556] (-) TimerEvent: {}
[28.258879] (-) TimerEvent: {}
[28.359210] (-) TimerEvent: {}
[28.459591] (-) TimerEvent: {}
[28.560254] (-) TimerEvent: {}
[28.660781] (-) TimerEvent: {}
[28.761282] (-) TimerEvent: {}
[28.862370] (-) TimerEvent: {}
[28.963332] (-) TimerEvent: {}
[29.063953] (-) TimerEvent: {}
[29.164577] (-) TimerEvent: {}
[29.264985] (-) TimerEvent: {}
[29.365507] (-) TimerEvent: {}
[29.466150] (-) TimerEvent: {}
[29.566536] (-) TimerEvent: {}
[29.667216] (-) TimerEvent: {}
[29.767731] (-) TimerEvent: {}
[29.868173] (-) TimerEvent: {}
[29.968534] (-) TimerEvent: {}
[30.068915] (-) TimerEvent: {}
[30.169502] (-) TimerEvent: {}
[30.270231] (-) TimerEvent: {}
[30.370642] (-) TimerEvent: {}
[30.471152] (-) TimerEvent: {}
[30.572214] (-) TimerEvent: {}
[30.672580] (-) TimerEvent: {}
[30.773279] (-) TimerEvent: {}
[30.873663] (-) TimerEvent: {}
[30.974256] (-) TimerEvent: {}
[31.011036] (ros1_bridge) StdoutLine: {'line': b'[  4%] \x1b[32m\x1b[1mLinking CXX executable simple_bridge\x1b[0m\n'}
[31.074394] (-) TimerEvent: {}
[31.174761] (-) TimerEvent: {}
[31.275221] (-) TimerEvent: {}
[31.343811] (ros1_bridge) StdoutLine: {'line': b'[  4%] Built target simple_bridge\n'}
[31.354317] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target test_ros2_client_cpp\x1b[0m\n'}
[31.375493] (-) TimerEvent: {}
[31.398806] (ros1_bridge) StdoutLine: {'line': b'[  4%] \x1b[32mBuilding CXX object CMakeFiles/test_ros2_client_cpp.dir/test/test_ros2_client.cpp.o\x1b[0m\n'}
[31.475735] (-) TimerEvent: {}
[31.576211] (-) TimerEvent: {}
[31.677313] (-) TimerEvent: {}
[31.778208] (-) TimerEvent: {}
[31.878672] (-) TimerEvent: {}
[31.979108] (-) TimerEvent: {}
[32.079486] (-) TimerEvent: {}
[32.179927] (-) TimerEvent: {}
[32.280326] (-) TimerEvent: {}
[32.381221] (-) TimerEvent: {}
[32.481601] (-) TimerEvent: {}
[32.582201] (-) TimerEvent: {}
[32.682877] (-) TimerEvent: {}
[32.783295] (-) TimerEvent: {}
[32.883718] (-) TimerEvent: {}
[32.984344] (-) TimerEvent: {}
[33.084917] (-) TimerEvent: {}
[33.185435] (-) TimerEvent: {}
[33.286322] (-) TimerEvent: {}
[33.387296] (-) TimerEvent: {}
[33.487737] (-) TimerEvent: {}
[33.588222] (-) TimerEvent: {}
[33.688698] (-) TimerEvent: {}
[33.789167] (-) TimerEvent: {}
[33.889671] (-) TimerEvent: {}
[33.938438] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target ros1_bridge\x1b[0m\n'}
[33.990222] (-) TimerEvent: {}
[34.091263] (-) TimerEvent: {}
[34.191847] (-) TimerEvent: {}
[34.209256] (ros1_bridge) StdoutLine: {'line': b'[  5%] \x1b[32m\x1b[1mLinking CXX executable test_ros2_client_cpp\x1b[0m\n'}
[34.291965] (-) TimerEvent: {}
[34.392478] (-) TimerEvent: {}
[34.394453] (ros1_bridge) StdoutLine: {'line': b'[  5%] Built target test_ros2_client_cpp\n'}
[34.492555] (-) TimerEvent: {}
[34.592975] (-) TimerEvent: {}
[34.693437] (-) TimerEvent: {}
[34.794258] (-) TimerEvent: {}
[34.894818] (-) TimerEvent: {}
[34.995289] (-) TimerEvent: {}
[35.095739] (-) TimerEvent: {}
[35.196167] (-) TimerEvent: {}
[35.297218] (-) TimerEvent: {}
[35.397616] (-) TimerEvent: {}
[35.498536] (-) TimerEvent: {}
[35.599422] (-) TimerEvent: {}
[35.700031] (-) TimerEvent: {}
[35.800523] (-) TimerEvent: {}
[35.901578] (-) TimerEvent: {}
[36.002518] (-) TimerEvent: {}
[36.103005] (-) TimerEvent: {}
[36.203476] (-) TimerEvent: {}
[36.303862] (-) TimerEvent: {}
[36.390983] (ros1_bridge) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/builtin_interfaces_factories.cpp.o\x1b[0m\n'}
[36.391299] (ros1_bridge) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/convert_builtin_interfaces.cpp.o\x1b[0m\n'}
[36.403935] (-) TimerEvent: {}
[36.504222] (-) TimerEvent: {}
[36.604622] (-) TimerEvent: {}
[36.704990] (-) TimerEvent: {}
[36.805395] (-) TimerEvent: {}
[36.906266] (-) TimerEvent: {}
[37.007213] (-) TimerEvent: {}
[37.107613] (-) TimerEvent: {}
[37.130743] (ros1_bridge) StdoutLine: {'line': b'[  6%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/bridge.cpp.o\x1b[0m\n'}
[37.208274] (-) TimerEvent: {}
[37.308686] (-) TimerEvent: {}
[37.409059] (-) TimerEvent: {}
[37.509533] (-) TimerEvent: {}
[37.610670] (-) TimerEvent: {}
[37.711199] (-) TimerEvent: {}
[37.811595] (-) TimerEvent: {}
[37.912347] (-) TimerEvent: {}
[38.012848] (-) TimerEvent: {}
[38.113291] (-) TimerEvent: {}
[38.213714] (-) TimerEvent: {}
[38.314309] (-) TimerEvent: {}
[38.415433] (-) TimerEvent: {}
[38.516172] (-) TimerEvent: {}
[38.616635] (-) TimerEvent: {}
[38.717109] (-) TimerEvent: {}
[38.817579] (-) TimerEvent: {}
[38.918155] (-) TimerEvent: {}
[39.018558] (-) TimerEvent: {}
[39.119179] (-) TimerEvent: {}
[39.219844] (-) TimerEvent: {}
[39.320212] (-) TimerEvent: {}
[39.421121] (-) TimerEvent: {}
[39.521491] (-) TimerEvent: {}
[39.622162] (-) TimerEvent: {}
[39.722575] (-) TimerEvent: {}
[39.822953] (-) TimerEvent: {}
[39.923326] (-) TimerEvent: {}
[40.024210] (-) TimerEvent: {}
[40.124646] (-) TimerEvent: {}
[40.177705] (ros1_bridge) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/get_factory.cpp.o\x1b[0m\n'}
[40.224770] (-) TimerEvent: {}
[40.325183] (-) TimerEvent: {}
[40.425768] (-) TimerEvent: {}
[40.526324] (-) TimerEvent: {}
[40.627000] (-) TimerEvent: {}
[40.727527] (-) TimerEvent: {}
[40.828071] (-) TimerEvent: {}
[40.928598] (-) TimerEvent: {}
[41.029012] (-) TimerEvent: {}
[41.129584] (-) TimerEvent: {}
[41.230431] (-) TimerEvent: {}
[41.331121] (-) TimerEvent: {}
[41.431762] (-) TimerEvent: {}
[41.532237] (-) TimerEvent: {}
[41.632744] (-) TimerEvent: {}
[41.733326] (-) TimerEvent: {}
[41.834297] (-) TimerEvent: {}
[41.935196] (-) TimerEvent: {}
[42.035939] (-) TimerEvent: {}
[42.136679] (-) TimerEvent: {}
[42.237193] (-) TimerEvent: {}
[42.338151] (-) TimerEvent: {}
[42.438788] (-) TimerEvent: {}
[42.539181] (-) TimerEvent: {}
[42.639865] (-) TimerEvent: {}
[42.741035] (-) TimerEvent: {}
[42.841480] (-) TimerEvent: {}
[42.942214] (-) TimerEvent: {}
[43.042879] (-) TimerEvent: {}
[43.143475] (-) TimerEvent: {}
[43.243982] (-) TimerEvent: {}
[43.344679] (-) TimerEvent: {}
[43.445173] (-) TimerEvent: {}
[43.545629] (-) TimerEvent: {}
[43.646232] (-) TimerEvent: {}
[43.747261] (-) TimerEvent: {}
[43.851381] (-) TimerEvent: {}
[43.952352] (-) TimerEvent: {}
[44.053056] (-) TimerEvent: {}
[44.154236] (-) TimerEvent: {}
[44.255124] (-) TimerEvent: {}
[44.358477] (-) TimerEvent: {}
[44.459257] (-) TimerEvent: {}
[44.560110] (-) TimerEvent: {}
[44.664289] (-) TimerEvent: {}
[44.765126] (-) TimerEvent: {}
[44.865487] (-) TimerEvent: {}
[44.966229] (-) TimerEvent: {}
[45.066754] (-) TimerEvent: {}
[45.167287] (-) TimerEvent: {}
[45.268037] (-) TimerEvent: {}
[45.368763] (-) TimerEvent: {}
[45.469732] (-) TimerEvent: {}
[45.570354] (-) TimerEvent: {}
[45.671256] (-) TimerEvent: {}
[45.771875] (-) TimerEvent: {}
[45.872440] (-) TimerEvent: {}
[45.972975] (-) TimerEvent: {}
[46.074191] (-) TimerEvent: {}
[46.175152] (-) TimerEvent: {}
[46.275687] (-) TimerEvent: {}
[46.376108] (-) TimerEvent: {}
[46.477031] (-) TimerEvent: {}
[46.577470] (-) TimerEvent: {}
[46.637813] (ros1_bridge) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/get_mappings.cpp.o\x1b[0m\n'}
[46.677593] (-) TimerEvent: {}
[46.779226] (-) TimerEvent: {}
[46.881013] (-) TimerEvent: {}
[46.981446] (-) TimerEvent: {}
[47.082151] (-) TimerEvent: {}
[47.182914] (-) TimerEvent: {}
[47.218352] (ros1_bridge) StdoutLine: {'line': b'[  7%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs_factories.cpp.o\x1b[0m\n'}
[47.283115] (-) TimerEvent: {}
[47.383632] (-) TimerEvent: {}
[47.484162] (-) TimerEvent: {}
[47.490737] (ros1_bridge) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalInfo__factories.cpp.o\x1b[0m\n'}
[47.584270] (-) TimerEvent: {}
[47.684943] (-) TimerEvent: {}
[47.785591] (-) TimerEvent: {}
[47.886213] (-) TimerEvent: {}
[47.986875] (-) TimerEvent: {}
[48.087976] (-) TimerEvent: {}
[48.188685] (-) TimerEvent: {}
[48.289266] (-) TimerEvent: {}
[48.390169] (-) TimerEvent: {}
[48.491086] (-) TimerEvent: {}
[48.591596] (-) TimerEvent: {}
[48.692365] (-) TimerEvent: {}
[48.793135] (-) TimerEvent: {}
[48.894197] (-) TimerEvent: {}
[48.994722] (-) TimerEvent: {}
[49.095273] (-) TimerEvent: {}
[49.195799] (-) TimerEvent: {}
[49.296513] (-) TimerEvent: {}
[49.397022] (-) TimerEvent: {}
[49.498223] (-) TimerEvent: {}
[49.599076] (-) TimerEvent: {}
[49.700216] (-) TimerEvent: {}
[49.800889] (-) TimerEvent: {}
[49.901476] (-) TimerEvent: {}
[50.002213] (-) TimerEvent: {}
[50.103050] (-) TimerEvent: {}
[50.204058] (-) TimerEvent: {}
[50.304964] (-) TimerEvent: {}
[50.406199] (-) TimerEvent: {}
[50.507023] (-) TimerEvent: {}
[50.607526] (-) TimerEvent: {}
[50.707970] (-) TimerEvent: {}
[50.808625] (-) TimerEvent: {}
[50.909127] (-) TimerEvent: {}
[51.009606] (-) TimerEvent: {}
[51.110407] (-) TimerEvent: {}
[51.210931] (-) TimerEvent: {}
[51.311372] (-) TimerEvent: {}
[51.411867] (-) TimerEvent: {}
[51.512584] (-) TimerEvent: {}
[51.613259] (-) TimerEvent: {}
[51.714176] (-) TimerEvent: {}
[51.815180] (-) TimerEvent: {}
[51.915748] (-) TimerEvent: {}
[51.954796] (ros1_bridge) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalStatus__factories.cpp.o\x1b[0m\n'}
[52.016141] (-) TimerEvent: {}
[52.116720] (-) TimerEvent: {}
[52.217239] (-) TimerEvent: {}
[52.235929] (ros1_bridge) StdoutLine: {'line': b'[  8%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalStatusArray__factories.cpp.o\x1b[0m\n'}
[52.317392] (-) TimerEvent: {}
[52.418167] (-) TimerEvent: {}
[52.518525] (-) TimerEvent: {}
[52.619088] (-) TimerEvent: {}
[52.719692] (-) TimerEvent: {}
[52.820187] (-) TimerEvent: {}
[52.920945] (-) TimerEvent: {}
[53.021435] (-) TimerEvent: {}
[53.122273] (-) TimerEvent: {}
[53.222843] (-) TimerEvent: {}
[53.323352] (-) TimerEvent: {}
[53.423980] (-) TimerEvent: {}
[53.524530] (-) TimerEvent: {}
[53.625044] (-) TimerEvent: {}
[53.725504] (-) TimerEvent: {}
[53.826150] (-) TimerEvent: {}
[53.927010] (-) TimerEvent: {}
[54.027950] (-) TimerEvent: {}
[54.128331] (-) TimerEvent: {}
[54.228800] (-) TimerEvent: {}
[54.330203] (-) TimerEvent: {}
[54.431026] (-) TimerEvent: {}
[54.531449] (-) TimerEvent: {}
[54.632090] (-) TimerEvent: {}
[54.733142] (-) TimerEvent: {}
[54.834153] (-) TimerEvent: {}
[54.934538] (-) TimerEvent: {}
[55.035237] (-) TimerEvent: {}
[55.135699] (-) TimerEvent: {}
[55.236747] (-) TimerEvent: {}
[55.337261] (-) TimerEvent: {}
[55.438150] (-) TimerEvent: {}
[55.538590] (-) TimerEvent: {}
[55.639072] (-) TimerEvent: {}
[55.739441] (-) TimerEvent: {}
[55.839858] (-) TimerEvent: {}
[55.940287] (-) TimerEvent: {}
[56.040971] (-) TimerEvent: {}
[56.141429] (-) TimerEvent: {}
[56.242171] (-) TimerEvent: {}
[56.342705] (-) TimerEvent: {}
[56.379150] (ros1_bridge) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__srv__CancelGoal__factories.cpp.o\x1b[0m\n'}
[56.443097] (-) TimerEvent: {}
[56.543795] (-) TimerEvent: {}
[56.644154] (-) TimerEvent: {}
[56.744854] (-) TimerEvent: {}
[56.845273] (-) TimerEvent: {}
[56.867725] (ros1_bridge) StdoutLine: {'line': b'[  9%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_tutorials_interfaces_factories.cpp.o\x1b[0m\n'}
[56.945417] (-) TimerEvent: {}
[57.046187] (-) TimerEvent: {}
[57.146731] (-) TimerEvent: {}
[57.247141] (-) TimerEvent: {}
[57.347585] (-) TimerEvent: {}
[57.448295] (-) TimerEvent: {}
[57.548903] (-) TimerEvent: {}
[57.649362] (-) TimerEvent: {}
[57.750192] (-) TimerEvent: {}
[57.850664] (-) TimerEvent: {}
[57.951174] (-) TimerEvent: {}
[58.052256] (-) TimerEvent: {}
[58.152819] (-) TimerEvent: {}
[58.253236] (-) TimerEvent: {}
[58.353690] (-) TimerEvent: {}
[58.454169] (-) TimerEvent: {}
[58.554742] (-) TimerEvent: {}
[58.655288] (-) TimerEvent: {}
[58.755827] (-) TimerEvent: {}
[58.856992] (-) TimerEvent: {}
[58.958160] (-) TimerEvent: {}
[59.058559] (-) TimerEvent: {}
[59.159301] (-) TimerEvent: {}
[59.259878] (-) TimerEvent: {}
[59.360345] (-) TimerEvent: {}
[59.460851] (-) TimerEvent: {}
[59.561322] (-) TimerEvent: {}
[59.662399] (-) TimerEvent: {}
[59.762930] (-) TimerEvent: {}
[59.863286] (-) TimerEvent: {}
[59.963947] (-) TimerEvent: {}
[60.064503] (-) TimerEvent: {}
[60.165312] (-) TimerEvent: {}
[60.266179] (-) TimerEvent: {}
[60.366726] (-) TimerEvent: {}
[60.467451] (-) TimerEvent: {}
[60.565897] (ros1_bridge) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs_factories.cpp.o\x1b[0m\n'}
[60.567524] (-) TimerEvent: {}
[60.667982] (-) TimerEvent: {}
[60.768737] (-) TimerEvent: {}
[60.869298] (-) TimerEvent: {}
[60.970203] (-) TimerEvent: {}
[61.070670] (-) TimerEvent: {}
[61.171664] (-) TimerEvent: {}
[61.244324] (ros1_bridge) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalID__factories.cpp.o\x1b[0m\n'}
[61.271798] (-) TimerEvent: {}
[61.372263] (-) TimerEvent: {}
[61.473244] (-) TimerEvent: {}
[61.574173] (-) TimerEvent: {}
[61.674864] (-) TimerEvent: {}
[61.775510] (-) TimerEvent: {}
[61.876851] (-) TimerEvent: {}
[61.977438] (-) TimerEvent: {}
[62.078600] (-) TimerEvent: {}
[62.179312] (-) TimerEvent: {}
[62.279959] (-) TimerEvent: {}
[62.380664] (-) TimerEvent: {}
[62.482209] (-) TimerEvent: {}
[62.582976] (-) TimerEvent: {}
[62.683597] (-) TimerEvent: {}
[62.784197] (-) TimerEvent: {}
[62.884783] (-) TimerEvent: {}
[62.985326] (-) TimerEvent: {}
[63.086220] (-) TimerEvent: {}
[63.186907] (-) TimerEvent: {}
[63.287604] (-) TimerEvent: {}
[63.388861] (-) TimerEvent: {}
[63.489597] (-) TimerEvent: {}
[63.590185] (-) TimerEvent: {}
[63.690750] (-) TimerEvent: {}
[63.791763] (-) TimerEvent: {}
[63.892929] (-) TimerEvent: {}
[63.993695] (-) TimerEvent: {}
[64.094273] (-) TimerEvent: {}
[64.194808] (-) TimerEvent: {}
[64.295447] (-) TimerEvent: {}
[64.396210] (-) TimerEvent: {}
[64.496792] (-) TimerEvent: {}
[64.597592] (-) TimerEvent: {}
[64.698357] (-) TimerEvent: {}
[64.798915] (-) TimerEvent: {}
[64.899699] (-) TimerEvent: {}
[65.000157] (-) TimerEvent: {}
[65.100783] (-) TimerEvent: {}
[65.201644] (-) TimerEvent: {}
[65.302231] (-) TimerEvent: {}
[65.392557] (ros1_bridge) StdoutLine: {'line': b'[ 10%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalStatus__factories.cpp.o\x1b[0m\n'}
[65.402561] (-) TimerEvent: {}
[65.503048] (-) TimerEvent: {}
[65.603632] (-) TimerEvent: {}
[65.704257] (-) TimerEvent: {}
[65.804828] (-) TimerEvent: {}
[65.905639] (-) TimerEvent: {}
[66.006623] (-) TimerEvent: {}
[66.107115] (-) TimerEvent: {}
[66.207947] (-) TimerEvent: {}
[66.308984] (-) TimerEvent: {}
[66.409563] (-) TimerEvent: {}
[66.510562] (-) TimerEvent: {}
[66.611154] (-) TimerEvent: {}
[66.711821] (-) TimerEvent: {}
[66.812591] (-) TimerEvent: {}
[66.913390] (-) TimerEvent: {}
[67.014782] (-) TimerEvent: {}
[67.115312] (-) TimerEvent: {}
[67.215831] (-) TimerEvent: {}
[67.316372] (-) TimerEvent: {}
[67.416764] (-) TimerEvent: {}
[67.518161] (-) TimerEvent: {}
[67.618719] (-) TimerEvent: {}
[67.719639] (-) TimerEvent: {}
[67.820666] (-) TimerEvent: {}
[67.921202] (-) TimerEvent: {}
[68.021669] (-) TimerEvent: {}
[68.122853] (-) TimerEvent: {}
[68.223536] (-) TimerEvent: {}
[68.324426] (-) TimerEvent: {}
[68.425240] (-) TimerEvent: {}
[68.526523] (-) TimerEvent: {}
[68.627135] (-) TimerEvent: {}
[68.727697] (-) TimerEvent: {}
[68.828694] (-) TimerEvent: {}
[68.929082] (-) TimerEvent: {}
[69.030131] (-) TimerEvent: {}
[69.130861] (-) TimerEvent: {}
[69.231800] (-) TimerEvent: {}
[69.332486] (-) TimerEvent: {}
[69.433018] (-) TimerEvent: {}
[69.533611] (-) TimerEvent: {}
[69.634261] (-) TimerEvent: {}
[69.734745] (-) TimerEvent: {}
[69.835548] (-) TimerEvent: {}
[69.936391] (-) TimerEvent: {}
[70.036774] (-) TimerEvent: {}
[70.137271] (-) TimerEvent: {}
[70.238275] (-) TimerEvent: {}
[70.339066] (-) TimerEvent: {}
[70.439575] (-) TimerEvent: {}
[70.540018] (-) TimerEvent: {}
[70.640849] (-) TimerEvent: {}
[70.741632] (-) TimerEvent: {}
[70.842560] (-) TimerEvent: {}
[70.942977] (-) TimerEvent: {}
[71.043465] (-) TimerEvent: {}
[71.144187] (-) TimerEvent: {}
[71.244629] (-) TimerEvent: {}
[71.345101] (-) TimerEvent: {}
[71.446167] (-) TimerEvent: {}
[71.546620] (-) TimerEvent: {}
[71.647310] (-) TimerEvent: {}
[71.747836] (-) TimerEvent: {}
[71.848422] (-) TimerEvent: {}
[71.923553] (ros1_bridge) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalStatusArray__factories.cpp.o\x1b[0m\n'}
[71.948568] (-) TimerEvent: {}
[72.049435] (-) TimerEvent: {}
[72.150497] (-) TimerEvent: {}
[72.250887] (-) TimerEvent: {}
[72.351533] (-) TimerEvent: {}
[72.452045] (-) TimerEvent: {}
[72.552540] (-) TimerEvent: {}
[72.653012] (-) TimerEvent: {}
[72.753496] (-) TimerEvent: {}
[72.854220] (-) TimerEvent: {}
[72.954705] (-) TimerEvent: {}
[73.055205] (-) TimerEvent: {}
[73.155562] (-) TimerEvent: {}
[73.256029] (-) TimerEvent: {}
[73.356490] (-) TimerEvent: {}
[73.457074] (-) TimerEvent: {}
[73.558158] (-) TimerEvent: {}
[73.658574] (-) TimerEvent: {}
[73.759045] (-) TimerEvent: {}
[73.859658] (-) TimerEvent: {}
[73.960626] (-) TimerEvent: {}
[74.061204] (-) TimerEvent: {}
[74.161681] (-) TimerEvent: {}
[74.262592] (-) TimerEvent: {}
[74.363364] (-) TimerEvent: {}
[74.463858] (-) TimerEvent: {}
[74.564363] (-) TimerEvent: {}
[74.665728] (-) TimerEvent: {}
[74.766243] (-) TimerEvent: {}
[74.867425] (-) TimerEvent: {}
[74.968142] (-) TimerEvent: {}
[75.068672] (-) TimerEvent: {}
[75.169364] (-) TimerEvent: {}
[75.270362] (-) TimerEvent: {}
[75.371337] (-) TimerEvent: {}
[75.472348] (-) TimerEvent: {}
[75.488508] (ros1_bridge) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces_factories.cpp.o\x1b[0m\n'}
[75.572468] (-) TimerEvent: {}
[75.673108] (-) TimerEvent: {}
[75.774374] (-) TimerEvent: {}
[75.875183] (-) TimerEvent: {}
[75.975776] (-) TimerEvent: {}
[76.076267] (-) TimerEvent: {}
[76.177307] (-) TimerEvent: {}
[76.278266] (-) TimerEvent: {}
[76.378938] (-) TimerEvent: {}
[76.479496] (-) TimerEvent: {}
[76.580011] (-) TimerEvent: {}
[76.680539] (-) TimerEvent: {}
[76.781457] (-) TimerEvent: {}
[76.882310] (-) TimerEvent: {}
[76.982820] (-) TimerEvent: {}
[77.083354] (-) TimerEvent: {}
[77.183803] (-) TimerEvent: {}
[77.284363] (-) TimerEvent: {}
[77.384836] (-) TimerEvent: {}
[77.485345] (-) TimerEvent: {}
[77.586187] (-) TimerEvent: {}
[77.686669] (-) TimerEvent: {}
[77.787270] (-) TimerEvent: {}
[77.887749] (-) TimerEvent: {}
[77.988533] (-) TimerEvent: {}
[78.089515] (-) TimerEvent: {}
[78.190154] (-) TimerEvent: {}
[78.290557] (-) TimerEvent: {}
[78.391024] (-) TimerEvent: {}
[78.491649] (-) TimerEvent: {}
[78.592709] (-) TimerEvent: {}
[78.693333] (-) TimerEvent: {}
[78.794181] (-) TimerEvent: {}
[78.894784] (-) TimerEvent: {}
[78.995362] (-) TimerEvent: {}
[79.096033] (-) TimerEvent: {}
[79.197414] (-) TimerEvent: {}
[79.298377] (-) TimerEvent: {}
[79.399441] (-) TimerEvent: {}
[79.500315] (-) TimerEvent: {}
[79.600915] (-) TimerEvent: {}
[79.701323] (-) TimerEvent: {}
[79.802263] (-) TimerEvent: {}
[79.903191] (-) TimerEvent: {}
[80.004520] (-) TimerEvent: {}
[80.105203] (-) TimerEvent: {}
[80.206300] (-) TimerEvent: {}
[80.307284] (-) TimerEvent: {}
[80.408325] (-) TimerEvent: {}
[80.508833] (-) TimerEvent: {}
[80.609865] (-) TimerEvent: {}
[80.710508] (-) TimerEvent: {}
[80.811045] (-) TimerEvent: {}
[80.911748] (-) TimerEvent: {}
[80.946041] (ros1_bridge) StdoutLine: {'line': b'[ 11%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__LoadNode__factories.cpp.o\x1b[0m\n'}
[81.011868] (-) TimerEvent: {}
[81.112496] (-) TimerEvent: {}
[81.212971] (-) TimerEvent: {}
[81.314449] (-) TimerEvent: {}
[81.415231] (-) TimerEvent: {}
[81.515785] (-) TimerEvent: {}
[81.616182] (-) TimerEvent: {}
[81.716624] (-) TimerEvent: {}
[81.817633] (-) TimerEvent: {}
[81.919046] (-) TimerEvent: {}
[82.019670] (-) TimerEvent: {}
[82.120213] (-) TimerEvent: {}
[82.220910] (-) TimerEvent: {}
[82.321513] (-) TimerEvent: {}
[82.423892] (-) TimerEvent: {}
[82.524703] (-) TimerEvent: {}
[82.625269] (-) TimerEvent: {}
[82.726324] (-) TimerEvent: {}
[82.826953] (-) TimerEvent: {}
[82.927573] (-) TimerEvent: {}
[82.967224] (ros1_bridge) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__ListNodes__factories.cpp.o\x1b[0m\n'}
[83.027681] (-) TimerEvent: {}
[83.128227] (-) TimerEvent: {}
[83.228857] (-) TimerEvent: {}
[83.329501] (-) TimerEvent: {}
[83.430361] (-) TimerEvent: {}
[83.530926] (-) TimerEvent: {}
[83.631403] (-) TimerEvent: {}
[83.732306] (-) TimerEvent: {}
[83.833506] (-) TimerEvent: {}
[83.934379] (-) TimerEvent: {}
[84.035080] (-) TimerEvent: {}
[84.136292] (-) TimerEvent: {}
[84.237506] (-) TimerEvent: {}
[84.338540] (-) TimerEvent: {}
[84.439448] (-) TimerEvent: {}
[84.540291] (-) TimerEvent: {}
[84.640853] (-) TimerEvent: {}
[84.742950] (-) TimerEvent: {}
[84.843716] (-) TimerEvent: {}
[84.944263] (-) TimerEvent: {}
[85.045277] (-) TimerEvent: {}
[85.146167] (-) TimerEvent: {}
[85.246661] (-) TimerEvent: {}
[85.347065] (-) TimerEvent: {}
[85.447796] (-) TimerEvent: {}
[85.548299] (-) TimerEvent: {}
[85.591356] (ros1_bridge) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__UnloadNode__factories.cpp.o\x1b[0m\n'}
[85.649096] (-) TimerEvent: {}
[85.750172] (-) TimerEvent: {}
[85.850575] (-) TimerEvent: {}
[85.950921] (-) TimerEvent: {}
[86.051784] (-) TimerEvent: {}
[86.152354] (-) TimerEvent: {}
[86.253151] (-) TimerEvent: {}
[86.353522] (-) TimerEvent: {}
[86.454267] (-) TimerEvent: {}
[86.555155] (-) TimerEvent: {}
[86.655593] (-) TimerEvent: {}
[86.756166] (-) TimerEvent: {}
[86.856872] (-) TimerEvent: {}
[86.957269] (-) TimerEvent: {}
[87.058150] (-) TimerEvent: {}
[87.159079] (-) TimerEvent: {}
[87.259444] (-) TimerEvent: {}
[87.264848] (ros1_bridge) StdoutLine: {'line': b'[ 12%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs_factories.cpp.o\x1b[0m\n'}
[87.359653] (-) TimerEvent: {}
[87.460245] (-) TimerEvent: {}
[87.561512] (-) TimerEvent: {}
[87.662475] (-) TimerEvent: {}
[87.763160] (-) TimerEvent: {}
[87.863593] (-) TimerEvent: {}
[87.964455] (-) TimerEvent: {}
[88.065280] (-) TimerEvent: {}
[88.166439] (-) TimerEvent: {}
[88.267365] (-) TimerEvent: {}
[88.367830] (-) TimerEvent: {}
[88.468313] (-) TimerEvent: {}
[88.568837] (-) TimerEvent: {}
[88.669485] (-) TimerEvent: {}
[88.770154] (-) TimerEvent: {}
[88.871150] (-) TimerEvent: {}
[88.971621] (-) TimerEvent: {}
[89.072060] (-) TimerEvent: {}
[89.172543] (-) TimerEvent: {}
[89.273073] (-) TimerEvent: {}
[89.323326] (ros1_bridge) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__DiagnosticArray__factories.cpp.o\x1b[0m\n'}
[89.373213] (-) TimerEvent: {}
[89.474464] (-) TimerEvent: {}
[89.574901] (-) TimerEvent: {}
[89.675278] (-) TimerEvent: {}
[89.775968] (-) TimerEvent: {}
[89.876464] (-) TimerEvent: {}
[89.977048] (-) TimerEvent: {}
[90.078256] (-) TimerEvent: {}
[90.178599] (-) TimerEvent: {}
[90.279867] (-) TimerEvent: {}
[90.381192] (-) TimerEvent: {}
[90.482165] (-) TimerEvent: {}
[90.582650] (-) TimerEvent: {}
[90.683248] (-) TimerEvent: {}
[90.783779] (-) TimerEvent: {}
[90.884130] (-) TimerEvent: {}
[90.984487] (-) TimerEvent: {}
[91.009419] (ros1_bridge) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__DiagnosticStatus__factories.cpp.o\x1b[0m\n'}
[91.084581] (-) TimerEvent: {}
[91.184938] (-) TimerEvent: {}
[91.286155] (-) TimerEvent: {}
[91.386543] (-) TimerEvent: {}
[91.487364] (-) TimerEvent: {}
[91.587771] (-) TimerEvent: {}
[91.688875] (-) TimerEvent: {}
[91.789241] (-) TimerEvent: {}
[91.889612] (-) TimerEvent: {}
[91.990256] (-) TimerEvent: {}
[92.090747] (-) TimerEvent: {}
[92.191136] (-) TimerEvent: {}
[92.291674] (-) TimerEvent: {}
[92.392125] (-) TimerEvent: {}
[92.492895] (-) TimerEvent: {}
[92.593310] (-) TimerEvent: {}
[92.694151] (-) TimerEvent: {}
[92.794530] (-) TimerEvent: {}
[92.895207] (-) TimerEvent: {}
[92.995860] (-) TimerEvent: {}
[93.096185] (-) TimerEvent: {}
[93.197125] (-) TimerEvent: {}
[93.298167] (-) TimerEvent: {}
[93.398549] (-) TimerEvent: {}
[93.499240] (-) TimerEvent: {}
[93.599694] (-) TimerEvent: {}
[93.700096] (-) TimerEvent: {}
[93.800428] (-) TimerEvent: {}
[93.901074] (-) TimerEvent: {}
[94.002193] (-) TimerEvent: {}
[94.102762] (-) TimerEvent: {}
[94.203158] (-) TimerEvent: {}
[94.303566] (-) TimerEvent: {}
[94.403945] (-) TimerEvent: {}
[94.505031] (-) TimerEvent: {}
[94.605377] (-) TimerEvent: {}
[94.706519] (-) TimerEvent: {}
[94.807019] (-) TimerEvent: {}
[94.907559] (-) TimerEvent: {}
[95.008094] (-) TimerEvent: {}
[95.109216] (-) TimerEvent: {}
[95.210143] (-) TimerEvent: {}
[95.310511] (-) TimerEvent: {}
[95.411202] (-) TimerEvent: {}
[95.511610] (-) TimerEvent: {}
[95.612530] (-) TimerEvent: {}
[95.713152] (-) TimerEvent: {}
[95.814262] (-) TimerEvent: {}
[95.914998] (-) TimerEvent: {}
[96.015626] (-) TimerEvent: {}
[96.116142] (-) TimerEvent: {}
[96.216722] (-) TimerEvent: {}
[96.317179] (-) TimerEvent: {}
[96.418151] (-) TimerEvent: {}
[96.518528] (-) TimerEvent: {}
[96.619319] (-) TimerEvent: {}
[96.719851] (-) TimerEvent: {}
[96.820224] (-) TimerEvent: {}
[96.920905] (-) TimerEvent: {}
[97.022232] (-) TimerEvent: {}
[97.122631] (-) TimerEvent: {}
[97.223257] (-) TimerEvent: {}
[97.324132] (-) TimerEvent: {}
[97.426190] (-) TimerEvent: {}
[97.527244] (-) TimerEvent: {}
[97.575683] (ros1_bridge) StdoutLine: {'line': b'[ 13%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__KeyValue__factories.cpp.o\x1b[0m\n'}
[97.627359] (-) TimerEvent: {}
[97.727718] (-) TimerEvent: {}
[97.828091] (-) TimerEvent: {}
[97.928840] (-) TimerEvent: {}
[98.029523] (-) TimerEvent: {}
[98.130219] (-) TimerEvent: {}
[98.230795] (-) TimerEvent: {}
[98.331166] (-) TimerEvent: {}
[98.431749] (-) TimerEvent: {}
[98.532189] (-) TimerEvent: {}
[98.633125] (-) TimerEvent: {}
[98.733662] (-) TimerEvent: {}
[98.834238] (-) TimerEvent: {}
[98.934759] (-) TimerEvent: {}
[99.035578] (-) TimerEvent: {}
[99.136042] (-) TimerEvent: {}
[99.236448] (-) TimerEvent: {}
[99.337443] (-) TimerEvent: {}
[99.438153] (-) TimerEvent: {}
[99.489737] (ros1_bridge) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__srv__AddDiagnostics__factories.cpp.o\x1b[0m\n'}
[99.538276] (-) TimerEvent: {}
[99.639194] (-) TimerEvent: {}
[99.740259] (-) TimerEvent: {}
[99.840903] (-) TimerEvent: {}
[99.941322] (-) TimerEvent: {}
[100.042173] (-) TimerEvent: {}
[100.143232] (-) TimerEvent: {}
[100.243985] (-) TimerEvent: {}
[100.344498] (-) TimerEvent: {}
[100.445545] (-) TimerEvent: {}
[100.546169] (-) TimerEvent: {}
[100.646590] (-) TimerEvent: {}
[100.747149] (-) TimerEvent: {}
[100.847986] (-) TimerEvent: {}
[100.948556] (-) TimerEvent: {}
[101.048957] (-) TimerEvent: {}
[101.150164] (-) TimerEvent: {}
[101.250821] (-) TimerEvent: {}
[101.351791] (-) TimerEvent: {}
[101.452201] (-) TimerEvent: {}
[101.552693] (-) TimerEvent: {}
[101.653746] (-) TimerEvent: {}
[101.754763] (-) TimerEvent: {}
[101.855162] (-) TimerEvent: {}
[101.955973] (-) TimerEvent: {}
[102.057094] (-) TimerEvent: {}
[102.157506] (-) TimerEvent: {}
[102.258218] (-) TimerEvent: {}
[102.358825] (-) TimerEvent: {}
[102.459205] (-) TimerEvent: {}
[102.560386] (-) TimerEvent: {}
[102.660987] (-) TimerEvent: {}
[102.761607] (-) TimerEvent: {}
[102.862141] (-) TimerEvent: {}
[102.962489] (-) TimerEvent: {}
[103.063046] (-) TimerEvent: {}
[103.163839] (-) TimerEvent: {}
[103.264513] (-) TimerEvent: {}
[103.365334] (-) TimerEvent: {}
[103.466151] (-) TimerEvent: {}
[103.567083] (-) TimerEvent: {}
[103.667733] (-) TimerEvent: {}
[103.768129] (-) TimerEvent: {}
[103.868818] (-) TimerEvent: {}
[103.969443] (-) TimerEvent: {}
[104.070820] (-) TimerEvent: {}
[104.171271] (-) TimerEvent: {}
[104.271636] (-) TimerEvent: {}
[104.372127] (-) TimerEvent: {}
[104.473283] (-) TimerEvent: {}
[104.574446] (-) TimerEvent: {}
[104.649974] (ros1_bridge) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__srv__SelfTest__factories.cpp.o\x1b[0m\n'}
[104.675249] (-) TimerEvent: {}
[104.775669] (-) TimerEvent: {}
[104.876204] (-) TimerEvent: {}
[104.976829] (-) TimerEvent: {}
[105.078174] (-) TimerEvent: {}
[105.179005] (-) TimerEvent: {}
[105.279766] (-) TimerEvent: {}
[105.380191] (-) TimerEvent: {}
[105.480655] (-) TimerEvent: {}
[105.581098] (-) TimerEvent: {}
[105.682155] (-) TimerEvent: {}
[105.783039] (-) TimerEvent: {}
[105.883575] (-) TimerEvent: {}
[105.986639] (-) TimerEvent: {}
[106.087162] (-) TimerEvent: {}
[106.094911] (ros1_bridge) StdoutLine: {'line': b'[ 14%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces_factories.cpp.o\x1b[0m\n'}
[106.187238] (-) TimerEvent: {}
[106.287753] (-) TimerEvent: {}
[106.388108] (-) TimerEvent: {}
[106.488758] (-) TimerEvent: {}
[106.589666] (-) TimerEvent: {}
[106.690716] (-) TimerEvent: {}
[106.791189] (-) TimerEvent: {}
[106.891600] (-) TimerEvent: {}
[106.991988] (-) TimerEvent: {}
[107.092381] (-) TimerEvent: {}
[107.193487] (-) TimerEvent: {}
[107.294152] (-) TimerEvent: {}
[107.395141] (-) TimerEvent: {}
[107.495912] (-) TimerEvent: {}
[107.597014] (-) TimerEvent: {}
[107.697713] (-) TimerEvent: {}
[107.798184] (-) TimerEvent: {}
[107.899256] (-) TimerEvent: {}
[107.999889] (-) TimerEvent: {}
[108.100917] (-) TimerEvent: {}
[108.201346] (-) TimerEvent: {}
[108.302149] (-) TimerEvent: {}
[108.402667] (-) TimerEvent: {}
[108.503640] (-) TimerEvent: {}
[108.604060] (-) TimerEvent: {}
[108.705161] (-) TimerEvent: {}
[108.806223] (-) TimerEvent: {}
[108.906647] (-) TimerEvent: {}
[109.007111] (-) TimerEvent: {}
[109.107702] (-) TimerEvent: {}
[109.208260] (-) TimerEvent: {}
[109.308714] (-) TimerEvent: {}
[109.409180] (-) TimerEvent: {}
[109.509721] (-) TimerEvent: {}
[109.610154] (-) TimerEvent: {}
[109.710544] (-) TimerEvent: {}
[109.811376] (-) TimerEvent: {}
[109.819529] (ros1_bridge) StdoutLine: {'line': b'[ 15%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Bool__factories.cpp.o\x1b[0m\n'}
[109.911739] (-) TimerEvent: {}
[110.012860] (-) TimerEvent: {}
[110.114286] (-) TimerEvent: {}
[110.214784] (-) TimerEvent: {}
[110.315677] (-) TimerEvent: {}
[110.416074] (-) TimerEvent: {}
[110.517059] (-) TimerEvent: {}
[110.617695] (-) TimerEvent: {}
[110.683037] (ros1_bridge) StdoutLine: {'line': b'[ 15%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Byte__factories.cpp.o\x1b[0m\n'}
[110.718446] (-) TimerEvent: {}
[110.818877] (-) TimerEvent: {}
[110.919304] (-) TimerEvent: {}
[111.019904] (-) TimerEvent: {}
[111.120474] (-) TimerEvent: {}
[111.220887] (-) TimerEvent: {}
[111.322163] (-) TimerEvent: {}
[111.422611] (-) TimerEvent: {}
[111.523239] (-) TimerEvent: {}
[111.623875] (-) TimerEvent: {}
[111.724880] (-) TimerEvent: {}
[111.825343] (-) TimerEvent: {}
[111.926179] (-) TimerEvent: {}
[112.027104] (-) TimerEvent: {}
[112.127891] (-) TimerEvent: {}
[112.228366] (-) TimerEvent: {}
[112.329064] (-) TimerEvent: {}
[112.430150] (-) TimerEvent: {}
[112.530840] (-) TimerEvent: {}
[112.631298] (-) TimerEvent: {}
[112.731735] (-) TimerEvent: {}
[112.832104] (-) TimerEvent: {}
[112.932540] (-) TimerEvent: {}
[113.033017] (-) TimerEvent: {}
[113.133499] (-) TimerEvent: {}
[113.234172] (-) TimerEvent: {}
[113.334881] (-) TimerEvent: {}
[113.435352] (-) TimerEvent: {}
[113.535909] (-) TimerEvent: {}
[113.572870] (ros1_bridge) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__ByteMultiArray__factories.cpp.o\x1b[0m\n'}
[113.635980] (-) TimerEvent: {}
[113.736308] (-) TimerEvent: {}
[113.836715] (-) TimerEvent: {}
[113.937493] (-) TimerEvent: {}
[114.038508] (-) TimerEvent: {}
[114.138965] (-) TimerEvent: {}
[114.239621] (-) TimerEvent: {}
[114.340791] (-) TimerEvent: {}
[114.441685] (-) TimerEvent: {}
[114.542854] (-) TimerEvent: {}
[114.643231] (-) TimerEvent: {}
[114.744036] (-) TimerEvent: {}
[114.844390] (-) TimerEvent: {}
[114.944829] (-) TimerEvent: {}
[115.046355] (-) TimerEvent: {}
[115.147671] (-) TimerEvent: {}
[115.204881] (ros1_bridge) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Char__factories.cpp.o\x1b[0m\n'}
[115.250166] (-) TimerEvent: {}
[115.354727] (-) TimerEvent: {}
[115.460572] (-) TimerEvent: {}
[115.560927] (-) TimerEvent: {}
[115.661427] (-) TimerEvent: {}
[115.762383] (-) TimerEvent: {}
[115.862828] (-) TimerEvent: {}
[115.963282] (-) TimerEvent: {}
[116.063811] (-) TimerEvent: {}
[116.164407] (-) TimerEvent: {}
[116.272357] (-) TimerEvent: {}
[116.372839] (-) TimerEvent: {}
[116.474182] (-) TimerEvent: {}
[116.574798] (-) TimerEvent: {}
[116.675375] (-) TimerEvent: {}
[116.775868] (-) TimerEvent: {}
[116.876422] (-) TimerEvent: {}
[116.977358] (-) TimerEvent: {}
[117.078174] (-) TimerEvent: {}
[117.182590] (-) TimerEvent: {}
[117.283574] (-) TimerEvent: {}
[117.384448] (-) TimerEvent: {}
[117.502175] (-) TimerEvent: {}
[117.602554] (-) TimerEvent: {}
[117.703616] (-) TimerEvent: {}
[117.804024] (-) TimerEvent: {}
[117.906668] (-) TimerEvent: {}
[118.011246] (-) TimerEvent: {}
[118.112116] (-) TimerEvent: {}
[118.212593] (-) TimerEvent: {}
[118.313186] (-) TimerEvent: {}
[118.414124] (-) TimerEvent: {}
[118.514772] (-) TimerEvent: {}
[118.615245] (-) TimerEvent: {}
[118.715728] (-) TimerEvent: {}
[118.816426] (-) TimerEvent: {}
[118.917407] (-) TimerEvent: {}
[119.018385] (-) TimerEvent: {}
[119.119011] (-) TimerEvent: {}
[119.219916] (-) TimerEvent: {}
[119.230758] (ros1_bridge) StdoutLine: {'line': b'[ 16%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Empty__factories.cpp.o\x1b[0m\n'}
[119.320861] (-) TimerEvent: {}
[119.421294] (-) TimerEvent: {}
[119.522173] (-) TimerEvent: {}
[119.622605] (-) TimerEvent: {}
[119.723309] (-) TimerEvent: {}
[119.823723] (-) TimerEvent: {}
[119.924141] (-) TimerEvent: {}
[119.963866] (ros1_bridge) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float32__factories.cpp.o\x1b[0m\n'}
[120.024254] (-) TimerEvent: {}
[120.125029] (-) TimerEvent: {}
[120.226192] (-) TimerEvent: {}
[120.326676] (-) TimerEvent: {}
[120.427372] (-) TimerEvent: {}
[120.527856] (-) TimerEvent: {}
[120.629079] (-) TimerEvent: {}
[120.729577] (-) TimerEvent: {}
[120.830232] (-) TimerEvent: {}
[120.930750] (-) TimerEvent: {}
[121.031484] (-) TimerEvent: {}
[121.132053] (-) TimerEvent: {}
[121.232732] (-) TimerEvent: {}
[121.333159] (-) TimerEvent: {}
[121.433581] (-) TimerEvent: {}
[121.534220] (-) TimerEvent: {}
[121.634790] (-) TimerEvent: {}
[121.735652] (-) TimerEvent: {}
[121.836230] (-) TimerEvent: {}
[121.936761] (-) TimerEvent: {}
[122.037278] (-) TimerEvent: {}
[122.137693] (-) TimerEvent: {}
[122.238871] (-) TimerEvent: {}
[122.339318] (-) TimerEvent: {}
[122.439839] (-) TimerEvent: {}
[122.540262] (-) TimerEvent: {}
[122.640965] (-) TimerEvent: {}
[122.741613] (-) TimerEvent: {}
[122.842339] (-) TimerEvent: {}
[122.942830] (-) TimerEvent: {}
[122.954595] (ros1_bridge) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float32MultiArray__factories.cpp.o\x1b[0m\n'}
[123.043300] (-) TimerEvent: {}
[123.143766] (-) TimerEvent: {}
[123.244443] (-) TimerEvent: {}
[123.344901] (-) TimerEvent: {}
[123.445721] (-) TimerEvent: {}
[123.546182] (-) TimerEvent: {}
[123.632039] (ros1_bridge) StdoutLine: {'line': b'[ 17%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float64__factories.cpp.o\x1b[0m\n'}
[123.646619] (-) TimerEvent: {}
[123.747305] (-) TimerEvent: {}
[123.847786] (-) TimerEvent: {}
[123.948299] (-) TimerEvent: {}
[124.048785] (-) TimerEvent: {}
[124.149445] (-) TimerEvent: {}
[124.250239] (-) TimerEvent: {}
[124.350958] (-) TimerEvent: {}
[124.451438] (-) TimerEvent: {}
[124.551904] (-) TimerEvent: {}
[124.652332] (-) TimerEvent: {}
[124.753259] (-) TimerEvent: {}
[124.854149] (-) TimerEvent: {}
[124.954855] (-) TimerEvent: {}
[125.055572] (-) TimerEvent: {}
[125.155986] (-) TimerEvent: {}
[125.256885] (-) TimerEvent: {}
[125.357426] (-) TimerEvent: {}
[125.458168] (-) TimerEvent: {}
[125.558642] (-) TimerEvent: {}
[125.659820] (-) TimerEvent: {}
[125.760372] (-) TimerEvent: {}
[125.860796] (-) TimerEvent: {}
[125.961718] (-) TimerEvent: {}
[126.062523] (-) TimerEvent: {}
[126.163395] (-) TimerEvent: {}
[126.264566] (-) TimerEvent: {}
[126.365304] (-) TimerEvent: {}
[126.466189] (-) TimerEvent: {}
[126.566624] (-) TimerEvent: {}
[126.667328] (-) TimerEvent: {}
[126.767816] (-) TimerEvent: {}
[126.868402] (-) TimerEvent: {}
[126.927970] (ros1_bridge) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float64MultiArray__factories.cpp.o\x1b[0m\n'}
[126.968955] (-) TimerEvent: {}
[127.069537] (-) TimerEvent: {}
[127.170171] (-) TimerEvent: {}
[127.270617] (-) TimerEvent: {}
[127.371910] (-) TimerEvent: {}
[127.407656] (ros1_bridge) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int16__factories.cpp.o\x1b[0m\n'}
[127.471995] (-) TimerEvent: {}
[127.573076] (-) TimerEvent: {}
[127.674273] (-) TimerEvent: {}
[127.774674] (-) TimerEvent: {}
[127.875031] (-) TimerEvent: {}
[127.975469] (-) TimerEvent: {}
[128.076404] (-) TimerEvent: {}
[128.176921] (-) TimerEvent: {}
[128.277314] (-) TimerEvent: {}
[128.378247] (-) TimerEvent: {}
[128.479319] (-) TimerEvent: {}
[128.579796] (-) TimerEvent: {}
[128.680441] (-) TimerEvent: {}
[128.781255] (-) TimerEvent: {}
[128.882252] (-) TimerEvent: {}
[128.983335] (-) TimerEvent: {}
[129.083910] (-) TimerEvent: {}
[129.184484] (-) TimerEvent: {}
[129.285184] (-) TimerEvent: {}
[129.386335] (-) TimerEvent: {}
[129.487459] (-) TimerEvent: {}
[129.588148] (-) TimerEvent: {}
[129.688853] (-) TimerEvent: {}
[129.789528] (-) TimerEvent: {}
[129.890302] (-) TimerEvent: {}
[129.991106] (-) TimerEvent: {}
[130.092184] (-) TimerEvent: {}
[130.192881] (-) TimerEvent: {}
[130.294200] (-) TimerEvent: {}
[130.394764] (-) TimerEvent: {}
[130.495412] (-) TimerEvent: {}
[130.596139] (-) TimerEvent: {}
[130.696883] (-) TimerEvent: {}
[130.798160] (-) TimerEvent: {}
[130.899214] (-) TimerEvent: {}
[130.999694] (-) TimerEvent: {}
[131.100671] (-) TimerEvent: {}
[131.201220] (-) TimerEvent: {}
[131.301736] (-) TimerEvent: {}
[131.402260] (-) TimerEvent: {}
[131.502733] (-) TimerEvent: {}
[131.506893] (ros1_bridge) StdoutLine: {'line': b'[ 18%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int16MultiArray__factories.cpp.o\x1b[0m\n'}
[131.602890] (-) TimerEvent: {}
[131.703287] (-) TimerEvent: {}
[131.803686] (-) TimerEvent: {}
[131.864799] (ros1_bridge) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int32__factories.cpp.o\x1b[0m\n'}
[131.903790] (-) TimerEvent: {}
[132.004434] (-) TimerEvent: {}
[132.105072] (-) TimerEvent: {}
[132.206554] (-) TimerEvent: {}
[132.307143] (-) TimerEvent: {}
[132.407696] (-) TimerEvent: {}
[132.508298] (-) TimerEvent: {}
[132.609325] (-) TimerEvent: {}
[132.710246] (-) TimerEvent: {}
[132.810815] (-) TimerEvent: {}
[132.911462] (-) TimerEvent: {}
[133.012499] (-) TimerEvent: {}
[133.113087] (-) TimerEvent: {}
[133.214181] (-) TimerEvent: {}
[133.314789] (-) TimerEvent: {}
[133.415305] (-) TimerEvent: {}
[133.515799] (-) TimerEvent: {}
[133.616270] (-) TimerEvent: {}
[133.716951] (-) TimerEvent: {}
[133.817609] (-) TimerEvent: {}
[133.918166] (-) TimerEvent: {}
[134.018871] (-) TimerEvent: {}
[134.119735] (-) TimerEvent: {}
[134.220346] (-) TimerEvent: {}
[134.321404] (-) TimerEvent: {}
[134.422235] (-) TimerEvent: {}
[134.522668] (-) TimerEvent: {}
[134.623355] (-) TimerEvent: {}
[134.724214] (-) TimerEvent: {}
[134.824780] (-) TimerEvent: {}
[134.925451] (-) TimerEvent: {}
[135.026316] (-) TimerEvent: {}
[135.126868] (-) TimerEvent: {}
[135.227341] (-) TimerEvent: {}
[135.327777] (-) TimerEvent: {}
[135.428335] (-) TimerEvent: {}
[135.529345] (-) TimerEvent: {}
[135.630188] (-) TimerEvent: {}
[135.730672] (-) TimerEvent: {}
[135.831498] (-) TimerEvent: {}
[135.932057] (-) TimerEvent: {}
[136.032511] (-) TimerEvent: {}
[136.133064] (-) TimerEvent: {}
[136.177562] (ros1_bridge) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int32MultiArray__factories.cpp.o\x1b[0m\n'}
[136.233283] (-) TimerEvent: {}
[136.334220] (-) TimerEvent: {}
[136.434951] (-) TimerEvent: {}
[136.535664] (-) TimerEvent: {}
[136.636163] (-) TimerEvent: {}
[136.657531] (ros1_bridge) StdoutLine: {'line': b'[ 19%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int64__factories.cpp.o\x1b[0m\n'}
[136.736302] (-) TimerEvent: {}
[136.836950] (-) TimerEvent: {}
[136.937596] (-) TimerEvent: {}
[137.038210] (-) TimerEvent: {}
[137.139057] (-) TimerEvent: {}
[137.239597] (-) TimerEvent: {}
[137.340119] (-) TimerEvent: {}
[137.440808] (-) TimerEvent: {}
[137.541377] (-) TimerEvent: {}
[137.642423] (-) TimerEvent: {}
[137.743243] (-) TimerEvent: {}
[137.843592] (-) TimerEvent: {}
[137.944022] (-) TimerEvent: {}
[138.044988] (-) TimerEvent: {}
[138.145719] (-) TimerEvent: {}
[138.246271] (-) TimerEvent: {}
[138.346741] (-) TimerEvent: {}
[138.447416] (-) TimerEvent: {}
[138.547963] (-) TimerEvent: {}
[138.648368] (-) TimerEvent: {}
[138.748836] (-) TimerEvent: {}
[138.849431] (-) TimerEvent: {}
[138.950213] (-) TimerEvent: {}
[139.050932] (-) TimerEvent: {}
[139.151596] (-) TimerEvent: {}
[139.252215] (-) TimerEvent: {}
[139.353161] (-) TimerEvent: {}
[139.453724] (-) TimerEvent: {}
[139.554697] (-) TimerEvent: {}
[139.655307] (-) TimerEvent: {}
[139.756036] (-) TimerEvent: {}
[139.856832] (-) TimerEvent: {}
[139.957367] (-) TimerEvent: {}
[140.058471] (-) TimerEvent: {}
[140.159013] (-) TimerEvent: {}
[140.259605] (-) TimerEvent: {}
[140.360208] (-) TimerEvent: {}
[140.460750] (-) TimerEvent: {}
[140.561320] (-) TimerEvent: {}
[140.662175] (-) TimerEvent: {}
[140.700349] (ros1_bridge) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int64MultiArray__factories.cpp.o\x1b[0m\n'}
[140.762270] (-) TimerEvent: {}
[140.862754] (-) TimerEvent: {}
[140.963350] (-) TimerEvent: {}
[141.063773] (-) TimerEvent: {}
[141.144149] (ros1_bridge) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int8__factories.cpp.o\x1b[0m\n'}
[141.164075] (-) TimerEvent: {}
[141.264529] (-) TimerEvent: {}
[141.365412] (-) TimerEvent: {}
[141.466294] (-) TimerEvent: {}
[141.566660] (-) TimerEvent: {}
[141.667040] (-) TimerEvent: {}
[141.768041] (-) TimerEvent: {}
[141.869160] (-) TimerEvent: {}
[141.970191] (-) TimerEvent: {}
[142.070584] (-) TimerEvent: {}
[142.170981] (-) TimerEvent: {}
[142.271385] (-) TimerEvent: {}
[142.371896] (-) TimerEvent: {}
[142.472311] (-) TimerEvent: {}
[142.572979] (-) TimerEvent: {}
[142.673549] (-) TimerEvent: {}
[142.774226] (-) TimerEvent: {}
[142.875006] (-) TimerEvent: {}
[142.975623] (-) TimerEvent: {}
[143.076119] (-) TimerEvent: {}
[143.177206] (-) TimerEvent: {}
[143.278355] (-) TimerEvent: {}
[143.380060] (-) TimerEvent: {}
[143.481197] (-) TimerEvent: {}
[143.582361] (-) TimerEvent: {}
[143.682991] (-) TimerEvent: {}
[143.783621] (-) TimerEvent: {}
[143.884003] (-) TimerEvent: {}
[143.985097] (-) TimerEvent: {}
[144.086182] (-) TimerEvent: {}
[144.186992] (-) TimerEvent: {}
[144.287638] (-) TimerEvent: {}
[144.388038] (-) TimerEvent: {}
[144.488861] (-) TimerEvent: {}
[144.589687] (-) TimerEvent: {}
[144.638021] (ros1_bridge) StdoutLine: {'line': b'[ 20%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int8MultiArray__factories.cpp.o\x1b[0m\n'}
[144.690481] (-) TimerEvent: {}
[144.791074] (-) TimerEvent: {}
[144.891544] (-) TimerEvent: {}
[144.907567] (ros1_bridge) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__MultiArrayDimension__factories.cpp.o\x1b[0m\n'}
[144.991835] (-) TimerEvent: {}
[145.092292] (-) TimerEvent: {}
[145.192772] (-) TimerEvent: {}
[145.293206] (-) TimerEvent: {}
[145.394230] (-) TimerEvent: {}
[145.494720] (-) TimerEvent: {}
[145.595222] (-) TimerEvent: {}
[145.695681] (-) TimerEvent: {}
[145.796119] (-) TimerEvent: {}
[145.896587] (-) TimerEvent: {}
[145.997216] (-) TimerEvent: {}
[146.098212] (-) TimerEvent: {}
[146.198693] (-) TimerEvent: {}
[146.299235] (-) TimerEvent: {}
[146.399714] (-) TimerEvent: {}
[146.500200] (-) TimerEvent: {}
[146.600680] (-) TimerEvent: {}
[146.701168] (-) TimerEvent: {}
[146.802232] (-) TimerEvent: {}
[146.902692] (-) TimerEvent: {}
[147.003160] (-) TimerEvent: {}
[147.103624] (-) TimerEvent: {}
[147.204092] (-) TimerEvent: {}
[147.304536] (-) TimerEvent: {}
[147.405076] (-) TimerEvent: {}
[147.506264] (-) TimerEvent: {}
[147.606710] (-) TimerEvent: {}
[147.707801] (-) TimerEvent: {}
[147.808127] (-) TimerEvent: {}
[147.908428] (-) TimerEvent: {}
[148.008824] (-) TimerEvent: {}
[148.109237] (-) TimerEvent: {}
[148.210154] (-) TimerEvent: {}
[148.310523] (-) TimerEvent: {}
[148.411114] (-) TimerEvent: {}
[148.495620] (ros1_bridge) StdoutLine: {'line': b'[ 21%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__MultiArrayLayout__factories.cpp.o\x1b[0m\n'}
[148.511180] (-) TimerEvent: {}
[148.584935] (ros1_bridge) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__String__factories.cpp.o\x1b[0m\n'}
[148.612023] (-) TimerEvent: {}
[148.712408] (-) TimerEvent: {}
[148.812838] (-) TimerEvent: {}
[148.913224] (-) TimerEvent: {}
[149.014214] (-) TimerEvent: {}
[149.114780] (-) TimerEvent: {}
[149.215221] (-) TimerEvent: {}
[149.315593] (-) TimerEvent: {}
[149.415963] (-) TimerEvent: {}
[149.516526] (-) TimerEvent: {}
[149.616933] (-) TimerEvent: {}
[149.717266] (-) TimerEvent: {}
[149.818160] (-) TimerEvent: {}
[149.918726] (-) TimerEvent: {}
[150.019727] (-) TimerEvent: {}
[150.120187] (-) TimerEvent: {}
[150.221031] (-) TimerEvent: {}
[150.321352] (-) TimerEvent: {}
[150.422170] (-) TimerEvent: {}
[150.523081] (-) TimerEvent: {}
[150.623622] (-) TimerEvent: {}
[150.724112] (-) TimerEvent: {}
[150.824719] (-) TimerEvent: {}
[150.925556] (-) TimerEvent: {}
[151.026739] (-) TimerEvent: {}
[151.127210] (-) TimerEvent: {}
[151.227617] (-) TimerEvent: {}
[151.328444] (-) TimerEvent: {}
[151.428835] (-) TimerEvent: {}
[151.529670] (-) TimerEvent: {}
[151.630280] (-) TimerEvent: {}
[151.730863] (-) TimerEvent: {}
[151.831802] (-) TimerEvent: {}
[151.932192] (-) TimerEvent: {}
[152.032496] (-) TimerEvent: {}
[152.132908] (-) TimerEvent: {}
[152.234174] (-) TimerEvent: {}
[152.265443] (ros1_bridge) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt16__factories.cpp.o\x1b[0m\n'}
[152.267286] (ros1_bridge) StdoutLine: {'line': b'[ 22%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt16MultiArray__factories.cpp.o\x1b[0m\n'}
[152.334391] (-) TimerEvent: {}
[152.435100] (-) TimerEvent: {}
[152.535498] (-) TimerEvent: {}
[152.636263] (-) TimerEvent: {}
[152.736668] (-) TimerEvent: {}
[152.837030] (-) TimerEvent: {}
[152.938142] (-) TimerEvent: {}
[153.038703] (-) TimerEvent: {}
[153.139222] (-) TimerEvent: {}
[153.239627] (-) TimerEvent: {}
[153.340014] (-) TimerEvent: {}
[153.440340] (-) TimerEvent: {}
[153.540759] (-) TimerEvent: {}
[153.641210] (-) TimerEvent: {}
[153.742153] (-) TimerEvent: {}
[153.842604] (-) TimerEvent: {}
[153.943298] (-) TimerEvent: {}
[154.044926] (-) TimerEvent: {}
[154.145699] (-) TimerEvent: {}
[154.246157] (-) TimerEvent: {}
[154.347044] (-) TimerEvent: {}
[154.447664] (-) TimerEvent: {}
[154.548232] (-) TimerEvent: {}
[154.648732] (-) TimerEvent: {}
[154.749382] (-) TimerEvent: {}
[154.850155] (-) TimerEvent: {}
[154.950938] (-) TimerEvent: {}
[155.051506] (-) TimerEvent: {}
[155.152026] (-) TimerEvent: {}
[155.252625] (-) TimerEvent: {}
[155.353217] (-) TimerEvent: {}
[155.453652] (-) TimerEvent: {}
[155.554243] (-) TimerEvent: {}
[155.654706] (-) TimerEvent: {}
[155.755206] (-) TimerEvent: {}
[155.855755] (-) TimerEvent: {}
[155.956277] (-) TimerEvent: {}
[155.983748] (ros1_bridge) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt32__factories.cpp.o\x1b[0m\n'}
[156.057590] (-) TimerEvent: {}
[156.130955] (ros1_bridge) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt32MultiArray__factories.cpp.o\x1b[0m\n'}
[156.157761] (-) TimerEvent: {}
[156.258242] (-) TimerEvent: {}
[156.359125] (-) TimerEvent: {}
[156.459700] (-) TimerEvent: {}
[156.560111] (-) TimerEvent: {}
[156.661190] (-) TimerEvent: {}
[156.762158] (-) TimerEvent: {}
[156.862572] (-) TimerEvent: {}
[156.963393] (-) TimerEvent: {}
[157.064119] (-) TimerEvent: {}
[157.165353] (-) TimerEvent: {}
[157.266237] (-) TimerEvent: {}
[157.366656] (-) TimerEvent: {}
[157.467090] (-) TimerEvent: {}
[157.567627] (-) TimerEvent: {}
[157.668067] (-) TimerEvent: {}
[157.769065] (-) TimerEvent: {}
[157.869588] (-) TimerEvent: {}
[157.970233] (-) TimerEvent: {}
[158.071045] (-) TimerEvent: {}
[158.171781] (-) TimerEvent: {}
[158.272174] (-) TimerEvent: {}
[158.372987] (-) TimerEvent: {}
[158.474162] (-) TimerEvent: {}
[158.574551] (-) TimerEvent: {}
[158.675491] (-) TimerEvent: {}
[158.775812] (-) TimerEvent: {}
[158.876845] (-) TimerEvent: {}
[158.977421] (-) TimerEvent: {}
[159.078487] (-) TimerEvent: {}
[159.178883] (-) TimerEvent: {}
[159.279323] (-) TimerEvent: {}
[159.379691] (-) TimerEvent: {}
[159.480236] (-) TimerEvent: {}
[159.581216] (-) TimerEvent: {}
[159.681555] (-) TimerEvent: {}
[159.704030] (ros1_bridge) StdoutLine: {'line': b'[ 23%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt64__factories.cpp.o\x1b[0m\n'}
[159.781652] (-) TimerEvent: {}
[159.882220] (-) TimerEvent: {}
[159.935401] (ros1_bridge) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt64MultiArray__factories.cpp.o\x1b[0m\n'}
[159.982454] (-) TimerEvent: {}
[160.082946] (-) TimerEvent: {}
[160.183389] (-) TimerEvent: {}
[160.283957] (-) TimerEvent: {}
[160.385428] (-) TimerEvent: {}
[160.486173] (-) TimerEvent: {}
[160.586848] (-) TimerEvent: {}
[160.687881] (-) TimerEvent: {}
[160.788486] (-) TimerEvent: {}
[160.888896] (-) TimerEvent: {}
[160.989285] (-) TimerEvent: {}
[161.090167] (-) TimerEvent: {}
[161.190590] (-) TimerEvent: {}
[161.291389] (-) TimerEvent: {}
[161.391783] (-) TimerEvent: {}
[161.492737] (-) TimerEvent: {}
[161.593186] (-) TimerEvent: {}
[161.694173] (-) TimerEvent: {}
[161.794556] (-) TimerEvent: {}
[161.894953] (-) TimerEvent: {}
[161.995475] (-) TimerEvent: {}
[162.095880] (-) TimerEvent: {}
[162.196932] (-) TimerEvent: {}
[162.300523] (-) TimerEvent: {}
[162.401289] (-) TimerEvent: {}
[162.502209] (-) TimerEvent: {}
[162.602591] (-) TimerEvent: {}
[162.703185] (-) TimerEvent: {}
[162.803695] (-) TimerEvent: {}
[162.904093] (-) TimerEvent: {}
[163.005000] (-) TimerEvent: {}
[163.105588] (-) TimerEvent: {}
[163.206150] (-) TimerEvent: {}
[163.306570] (-) TimerEvent: {}
[163.407621] (-) TimerEvent: {}
[163.454201] (ros1_bridge) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt8__factories.cpp.o\x1b[0m\n'}
[163.507874] (-) TimerEvent: {}
[163.608971] (-) TimerEvent: {}
[163.704006] (ros1_bridge) StdoutLine: {'line': b'[ 24%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt8MultiArray__factories.cpp.o\x1b[0m\n'}
[163.709039] (-) TimerEvent: {}
[163.809682] (-) TimerEvent: {}
[163.910358] (-) TimerEvent: {}
[164.010894] (-) TimerEvent: {}
[164.111444] (-) TimerEvent: {}
[164.211834] (-) TimerEvent: {}
[164.312207] (-) TimerEvent: {}
[164.412865] (-) TimerEvent: {}
[164.513299] (-) TimerEvent: {}
[164.613616] (-) TimerEvent: {}
[164.714240] (-) TimerEvent: {}
[164.814823] (-) TimerEvent: {}
[164.915247] (-) TimerEvent: {}
[165.015844] (-) TimerEvent: {}
[165.116814] (-) TimerEvent: {}
[165.217593] (-) TimerEvent: {}
[165.318456] (-) TimerEvent: {}
[165.419016] (-) TimerEvent: {}
[165.519715] (-) TimerEvent: {}
[165.620010] (-) TimerEvent: {}
[165.720713] (-) TimerEvent: {}
[165.821290] (-) TimerEvent: {}
[165.922153] (-) TimerEvent: {}
[166.022993] (-) TimerEvent: {}
[166.123464] (-) TimerEvent: {}
[166.223874] (-) TimerEvent: {}
[166.324323] (-) TimerEvent: {}
[166.424917] (-) TimerEvent: {}
[166.526323] (-) TimerEvent: {}
[166.627530] (-) TimerEvent: {}
[166.727929] (-) TimerEvent: {}
[166.828379] (-) TimerEvent: {}
[166.928911] (-) TimerEvent: {}
[166.968181] (ros1_bridge) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__WString__factories.cpp.o\x1b[0m\n'}
[167.029673] (-) TimerEvent: {}
[167.130690] (-) TimerEvent: {}
[167.231580] (-) TimerEvent: {}
[167.332153] (-) TimerEvent: {}
[167.351129] (ros1_bridge) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__AddTwoInts__factories.cpp.o\x1b[0m\n'}
[167.432277] (-) TimerEvent: {}
[167.532643] (-) TimerEvent: {}
[167.633132] (-) TimerEvent: {}
[167.733719] (-) TimerEvent: {}
[167.834242] (-) TimerEvent: {}
[167.934888] (-) TimerEvent: {}
[168.035774] (-) TimerEvent: {}
[168.136158] (-) TimerEvent: {}
[168.236534] (-) TimerEvent: {}
[168.337107] (-) TimerEvent: {}
[168.437476] (-) TimerEvent: {}
[168.538341] (-) TimerEvent: {}
[168.638772] (-) TimerEvent: {}
[168.739669] (-) TimerEvent: {}
[168.840071] (-) TimerEvent: {}
[168.940439] (-) TimerEvent: {}
[169.041405] (-) TimerEvent: {}
[169.142173] (-) TimerEvent: {}
[169.242694] (-) TimerEvent: {}
[169.343246] (-) TimerEvent: {}
[169.443687] (-) TimerEvent: {}
[169.544105] (-) TimerEvent: {}
[169.644939] (-) TimerEvent: {}
[169.745380] (-) TimerEvent: {}
[169.846140] (-) TimerEvent: {}
[169.946951] (-) TimerEvent: {}
[170.047601] (-) TimerEvent: {}
[170.148267] (-) TimerEvent: {}
[170.249032] (-) TimerEvent: {}
[170.350210] (-) TimerEvent: {}
[170.450610] (-) TimerEvent: {}
[170.551031] (-) TimerEvent: {}
[170.651410] (-) TimerEvent: {}
[170.752198] (-) TimerEvent: {}
[170.853291] (-) TimerEvent: {}
[170.954131] (-) TimerEvent: {}
[171.054788] (-) TimerEvent: {}
[171.155276] (-) TimerEvent: {}
[171.255626] (-) TimerEvent: {}
[171.355995] (-) TimerEvent: {}
[171.456371] (-) TimerEvent: {}
[171.557178] (-) TimerEvent: {}
[171.657525] (-) TimerEvent: {}
[171.758782] (-) TimerEvent: {}
[171.859636] (-) TimerEvent: {}
[171.960011] (-) TimerEvent: {}
[172.060618] (-) TimerEvent: {}
[172.162160] (-) TimerEvent: {}
[172.262778] (-) TimerEvent: {}
[172.364148] (-) TimerEvent: {}
[172.465184] (-) TimerEvent: {}
[172.566698] (-) TimerEvent: {}
[172.667147] (-) TimerEvent: {}
[172.767542] (-) TimerEvent: {}
[172.840268] (ros1_bridge) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__SetBool__factories.cpp.o\x1b[0m\n'}
[172.867634] (-) TimerEvent: {}
[172.968360] (-) TimerEvent: {}
[173.069563] (-) TimerEvent: {}
[173.170160] (-) TimerEvent: {}
[173.270487] (-) TimerEvent: {}
[173.370808] (-) TimerEvent: {}
[173.471587] (-) TimerEvent: {}
[173.572093] (-) TimerEvent: {}
[173.672624] (-) TimerEvent: {}
[173.773323] (-) TimerEvent: {}
[173.874499] (-) TimerEvent: {}
[173.975744] (-) TimerEvent: {}
[174.076068] (-) TimerEvent: {}
[174.176389] (-) TimerEvent: {}
[174.277305] (-) TimerEvent: {}
[174.378312] (-) TimerEvent: {}
[174.478883] (-) TimerEvent: {}
[174.579666] (-) TimerEvent: {}
[174.680197] (-) TimerEvent: {}
[174.780513] (-) TimerEvent: {}
[174.861123] (ros1_bridge) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__Trigger__factories.cpp.o\x1b[0m\n'}
[174.880613] (-) TimerEvent: {}
[174.981255] (-) TimerEvent: {}
[175.081655] (-) TimerEvent: {}
[175.183207] (-) TimerEvent: {}
[175.283667] (-) TimerEvent: {}
[175.384640] (-) TimerEvent: {}
[175.485221] (-) TimerEvent: {}
[175.586395] (-) TimerEvent: {}
[175.687256] (-) TimerEvent: {}
[175.787718] (-) TimerEvent: {}
[175.888086] (-) TimerEvent: {}
[175.988569] (-) TimerEvent: {}
[176.072177] (ros1_bridge) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs_factories.cpp.o\x1b[0m\n'}
[176.088709] (-) TimerEvent: {}
[176.190266] (-) TimerEvent: {}
[176.291352] (-) TimerEvent: {}
[176.392136] (-) TimerEvent: {}
[176.493171] (-) TimerEvent: {}
[176.594404] (-) TimerEvent: {}
[176.695529] (-) TimerEvent: {}
[176.795944] (-) TimerEvent: {}
[176.896342] (-) TimerEvent: {}
[176.996716] (-) TimerEvent: {}
[177.097555] (-) TimerEvent: {}
[177.198156] (-) TimerEvent: {}
[177.298557] (-) TimerEvent: {}
[177.399129] (-) TimerEvent: {}
[177.499688] (-) TimerEvent: {}
[177.600107] (-) TimerEvent: {}
[177.701023] (-) TimerEvent: {}
[177.801445] (-) TimerEvent: {}
[177.902354] (-) TimerEvent: {}
[178.003136] (-) TimerEvent: {}
[178.103526] (-) TimerEvent: {}
[178.204203] (-) TimerEvent: {}
[178.304604] (-) TimerEvent: {}
[178.405398] (-) TimerEvent: {}
[178.490558] (ros1_bridge) StdoutLine: {'line': b'[ 26%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Accel__factories.cpp.o\x1b[0m\n'}
[178.505559] (-) TimerEvent: {}
[178.606305] (-) TimerEvent: {}
[178.706744] (-) TimerEvent: {}
[178.807629] (-) TimerEvent: {}
[178.908130] (-) TimerEvent: {}
[179.009063] (-) TimerEvent: {}
[179.110146] (-) TimerEvent: {}
[179.210516] (-) TimerEvent: {}
[179.310887] (-) TimerEvent: {}
[179.411261] (-) TimerEvent: {}
[179.511703] (-) TimerEvent: {}
[179.612133] (-) TimerEvent: {}
[179.713493] (-) TimerEvent: {}
[179.750089] (ros1_bridge) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelStamped__factories.cpp.o\x1b[0m\n'}
[179.814213] (-) TimerEvent: {}
[179.914926] (-) TimerEvent: {}
[180.015393] (-) TimerEvent: {}
[180.116270] (-) TimerEvent: {}
[180.216714] (-) TimerEvent: {}
[180.317726] (-) TimerEvent: {}
[180.418409] (-) TimerEvent: {}
[180.518754] (-) TimerEvent: {}
[180.619153] (-) TimerEvent: {}
[180.719556] (-) TimerEvent: {}
[180.820093] (-) TimerEvent: {}
[180.920582] (-) TimerEvent: {}
[181.021119] (-) TimerEvent: {}
[181.121631] (-) TimerEvent: {}
[181.222325] (-) TimerEvent: {}
[181.322822] (-) TimerEvent: {}
[181.423788] (-) TimerEvent: {}
[181.524178] (-) TimerEvent: {}
[181.624592] (-) TimerEvent: {}
[181.725252] (-) TimerEvent: {}
[181.825727] (-) TimerEvent: {}
[181.926152] (-) TimerEvent: {}
[182.027033] (-) TimerEvent: {}
[182.127599] (-) TimerEvent: {}
[182.228049] (-) TimerEvent: {}
[182.329155] (-) TimerEvent: {}
[182.430173] (-) TimerEvent: {}
[182.531202] (-) TimerEvent: {}
[182.631914] (-) TimerEvent: {}
[182.733163] (-) TimerEvent: {}
[182.834157] (-) TimerEvent: {}
[182.934584] (-) TimerEvent: {}
[183.035328] (-) TimerEvent: {}
[183.135941] (-) TimerEvent: {}
[183.237143] (-) TimerEvent: {}
[183.337538] (-) TimerEvent: {}
[183.438173] (-) TimerEvent: {}
[183.538572] (-) TimerEvent: {}
[183.638968] (-) TimerEvent: {}
[183.739436] (-) TimerEvent: {}
[183.840026] (-) TimerEvent: {}
[183.940997] (-) TimerEvent: {}
[184.042143] (-) TimerEvent: {}
[184.142473] (-) TimerEvent: {}
[184.243063] (-) TimerEvent: {}
[184.344113] (-) TimerEvent: {}
[184.444557] (-) TimerEvent: {}
[184.544989] (-) TimerEvent: {}
[184.645403] (-) TimerEvent: {}
[184.746338] (-) TimerEvent: {}
[184.847016] (-) TimerEvent: {}
[184.947582] (-) TimerEvent: {}
[185.048169] (-) TimerEvent: {}
[185.149158] (-) TimerEvent: {}
[185.249588] (-) TimerEvent: {}
[185.350583] (-) TimerEvent: {}
[185.451008] (-) TimerEvent: {}
[185.551476] (-) TimerEvent: {}
[185.652043] (-) TimerEvent: {}
[185.752451] (-) TimerEvent: {}
[185.852823] (-) TimerEvent: {}
[185.953227] (-) TimerEvent: {}
[186.054160] (-) TimerEvent: {}
[186.154994] (-) TimerEvent: {}
[186.255358] (-) TimerEvent: {}
[186.355740] (-) TimerEvent: {}
[186.456281] (-) TimerEvent: {}
[186.535040] (ros1_bridge) StdoutLine: {'line': b'[ 27%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelWithCovariance__factories.cpp.o\x1b[0m\n'}
[186.556910] (-) TimerEvent: {}
[186.658338] (-) TimerEvent: {}
[186.759105] (-) TimerEvent: {}
[186.860063] (-) TimerEvent: {}
[186.961130] (-) TimerEvent: {}
[187.062228] (-) TimerEvent: {}
[187.162740] (-) TimerEvent: {}
[187.263219] (-) TimerEvent: {}
[187.363652] (-) TimerEvent: {}
[187.464130] (-) TimerEvent: {}
[187.564911] (-) TimerEvent: {}
[187.666158] (-) TimerEvent: {}
[187.766536] (-) TimerEvent: {}
[187.866992] (-) TimerEvent: {}
[187.893035] (ros1_bridge) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelWithCovarianceStamped__factories.cpp.o\x1b[0m\n'}
[187.967361] (-) TimerEvent: {}
[188.067986] (-) TimerEvent: {}
[188.168606] (-) TimerEvent: {}
[188.269150] (-) TimerEvent: {}
[188.370171] (-) TimerEvent: {}
[188.470905] (-) TimerEvent: {}
[188.571512] (-) TimerEvent: {}
[188.671907] (-) TimerEvent: {}
[188.772488] (-) TimerEvent: {}
[188.872889] (-) TimerEvent: {}
[188.973434] (-) TimerEvent: {}
[189.074276] (-) TimerEvent: {}
[189.174860] (-) TimerEvent: {}
[189.276306] (-) TimerEvent: {}
[189.378161] (-) TimerEvent: {}
[189.478577] (-) TimerEvent: {}
[189.579881] (-) TimerEvent: {}
[189.680482] (-) TimerEvent: {}
[189.780921] (-) TimerEvent: {}
[189.882166] (-) TimerEvent: {}
[189.983126] (-) TimerEvent: {}
[190.083541] (-) TimerEvent: {}
[190.184019] (-) TimerEvent: {}
[190.284549] (-) TimerEvent: {}
[190.384933] (-) TimerEvent: {}
[190.486147] (-) TimerEvent: {}
[190.586588] (-) TimerEvent: {}
[190.687144] (-) TimerEvent: {}
[190.787616] (-) TimerEvent: {}
[190.888102] (-) TimerEvent: {}
[190.988610] (-) TimerEvent: {}
[191.089140] (-) TimerEvent: {}
[191.190245] (-) TimerEvent: {}
[191.290957] (-) TimerEvent: {}
[191.391559] (-) TimerEvent: {}
[191.491939] (-) TimerEvent: {}
[191.592288] (-) TimerEvent: {}
[191.692634] (-) TimerEvent: {}
[191.793113] (-) TimerEvent: {}
[191.893622] (-) TimerEvent: {}
[191.994311] (-) TimerEvent: {}
[192.094827] (-) TimerEvent: {}
[192.195424] (-) TimerEvent: {}
[192.295880] (-) TimerEvent: {}
[192.396322] (-) TimerEvent: {}
[192.496816] (-) TimerEvent: {}
[192.597356] (-) TimerEvent: {}
[192.698220] (-) TimerEvent: {}
[192.798677] (-) TimerEvent: {}
[192.899186] (-) TimerEvent: {}
[192.999652] (-) TimerEvent: {}
[193.100100] (-) TimerEvent: {}
[193.200599] (-) TimerEvent: {}
[193.301460] (-) TimerEvent: {}
[193.402252] (-) TimerEvent: {}
[193.502747] (-) TimerEvent: {}
[193.603216] (-) TimerEvent: {}
[193.703703] (-) TimerEvent: {}
[193.804166] (-) TimerEvent: {}
[193.904970] (-) TimerEvent: {}
[194.005764] (-) TimerEvent: {}
[194.106876] (-) TimerEvent: {}
[194.209681] (-) TimerEvent: {}
[194.310164] (-) TimerEvent: {}
[194.410852] (-) TimerEvent: {}
[194.511226] (-) TimerEvent: {}
[194.611639] (-) TimerEvent: {}
[194.627267] (ros1_bridge) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Inertia__factories.cpp.o\x1b[0m\n'}
[194.711972] (-) TimerEvent: {}
[194.813094] (-) TimerEvent: {}
[194.913563] (-) TimerEvent: {}
[195.014210] (-) TimerEvent: {}
[195.114841] (-) TimerEvent: {}
[195.215510] (-) TimerEvent: {}
[195.315902] (-) TimerEvent: {}
[195.416661] (-) TimerEvent: {}
[195.517242] (-) TimerEvent: {}
[195.618348] (-) TimerEvent: {}
[195.718797] (-) TimerEvent: {}
[195.819403] (-) TimerEvent: {}
[195.919789] (-) TimerEvent: {}
[196.020748] (-) TimerEvent: {}
[196.121195] (-) TimerEvent: {}
[196.222167] (-) TimerEvent: {}
[196.223135] (ros1_bridge) StdoutLine: {'line': b'[ 28%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__InertiaStamped__factories.cpp.o\x1b[0m\n'}
[196.322290] (-) TimerEvent: {}
[196.422757] (-) TimerEvent: {}
[196.523222] (-) TimerEvent: {}
[196.623709] (-) TimerEvent: {}
[196.724187] (-) TimerEvent: {}
[196.825278] (-) TimerEvent: {}
[196.926178] (-) TimerEvent: {}
[197.026821] (-) TimerEvent: {}
[197.127447] (-) TimerEvent: {}
[197.228159] (-) TimerEvent: {}
[197.328626] (-) TimerEvent: {}
[197.429506] (-) TimerEvent: {}
[197.530541] (-) TimerEvent: {}
[197.631341] (-) TimerEvent: {}
[197.731849] (-) TimerEvent: {}
[197.832453] (-) TimerEvent: {}
[197.933132] (-) TimerEvent: {}
[198.034262] (-) TimerEvent: {}
[198.134804] (-) TimerEvent: {}
[198.235283] (-) TimerEvent: {}
[198.335907] (-) TimerEvent: {}
[198.436438] (-) TimerEvent: {}
[198.536893] (-) TimerEvent: {}
[198.638159] (-) TimerEvent: {}
[198.738784] (-) TimerEvent: {}
[198.839226] (-) TimerEvent: {}
[198.940242] (-) TimerEvent: {}
[199.040669] (-) TimerEvent: {}
[199.141019] (-) TimerEvent: {}
[199.242158] (-) TimerEvent: {}
[199.342879] (-) TimerEvent: {}
[199.443828] (-) TimerEvent: {}
[199.544345] (-) TimerEvent: {}
[199.645128] (-) TimerEvent: {}
[199.745642] (-) TimerEvent: {}
[199.846212] (-) TimerEvent: {}
[199.946761] (-) TimerEvent: {}
[200.047310] (-) TimerEvent: {}
[200.147930] (-) TimerEvent: {}
[200.248850] (-) TimerEvent: {}
[200.349384] (-) TimerEvent: {}
[200.450185] (-) TimerEvent: {}
[200.550692] (-) TimerEvent: {}
[200.651486] (-) TimerEvent: {}
[200.752502] (-) TimerEvent: {}
[200.853306] (-) TimerEvent: {}
[200.954164] (-) TimerEvent: {}
[201.054701] (-) TimerEvent: {}
[201.155121] (-) TimerEvent: {}
[201.255558] (-) TimerEvent: {}
[201.355940] (-) TimerEvent: {}
[201.456959] (-) TimerEvent: {}
[201.558174] (-) TimerEvent: {}
[201.658564] (-) TimerEvent: {}
[201.758986] (-) TimerEvent: {}
[201.859711] (-) TimerEvent: {}
[201.960132] (-) TimerEvent: {}
[202.061008] (-) TimerEvent: {}
[202.161452] (-) TimerEvent: {}
[202.262158] (-) TimerEvent: {}
[202.362552] (-) TimerEvent: {}
[202.463281] (-) TimerEvent: {}
[202.563688] (-) TimerEvent: {}
[202.663982] (-) TimerEvent: {}
[202.764351] (-) TimerEvent: {}
[202.865371] (-) TimerEvent: {}
[202.966131] (-) TimerEvent: {}
[203.021268] (ros1_bridge) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Point__factories.cpp.o\x1b[0m\n'}
[203.066681] (-) TimerEvent: {}
[203.167467] (-) TimerEvent: {}
[203.267935] (-) TimerEvent: {}
[203.368223] (-) TimerEvent: {}
[203.469012] (-) TimerEvent: {}
[203.570613] (-) TimerEvent: {}
[203.671667] (-) TimerEvent: {}
[203.772055] (-) TimerEvent: {}
[203.872420] (-) TimerEvent: {}
[203.972818] (-) TimerEvent: {}
[204.073179] (-) TimerEvent: {}
[204.173513] (-) TimerEvent: {}
[204.274274] (-) TimerEvent: {}
[204.374721] (-) TimerEvent: {}
[204.475617] (-) TimerEvent: {}
[204.576044] (-) TimerEvent: {}
[204.615131] (ros1_bridge) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Point32__factories.cpp.o\x1b[0m\n'}
[204.676269] (-) TimerEvent: {}
[204.776988] (-) TimerEvent: {}
[204.877440] (-) TimerEvent: {}
[204.978723] (-) TimerEvent: {}
[205.079175] (-) TimerEvent: {}
[205.180265] (-) TimerEvent: {}
[205.280715] (-) TimerEvent: {}
[205.381585] (-) TimerEvent: {}
[205.482448] (-) TimerEvent: {}
[205.583118] (-) TimerEvent: {}
[205.683628] (-) TimerEvent: {}
[205.784222] (-) TimerEvent: {}
[205.884721] (-) TimerEvent: {}
[205.985448] (-) TimerEvent: {}
[206.086171] (-) TimerEvent: {}
[206.186985] (-) TimerEvent: {}
[206.287399] (-) TimerEvent: {}
[206.387833] (-) TimerEvent: {}
[206.488231] (-) TimerEvent: {}
[206.588547] (-) TimerEvent: {}
[206.689203] (-) TimerEvent: {}
[206.789653] (-) TimerEvent: {}
[206.890528] (-) TimerEvent: {}
[206.990937] (-) TimerEvent: {}
[207.091748] (-) TimerEvent: {}
[207.192136] (-) TimerEvent: {}
[207.292580] (-) TimerEvent: {}
[207.393501] (-) TimerEvent: {}
[207.494179] (-) TimerEvent: {}
[207.594615] (-) TimerEvent: {}
[207.694998] (-) TimerEvent: {}
[207.795434] (-) TimerEvent: {}
[207.896246] (-) TimerEvent: {}
[207.997050] (-) TimerEvent: {}
[208.097546] (-) TimerEvent: {}
[208.198326] (-) TimerEvent: {}
[208.298828] (-) TimerEvent: {}
[208.399323] (-) TimerEvent: {}
[208.499787] (-) TimerEvent: {}
[208.600272] (-) TimerEvent: {}
[208.700785] (-) TimerEvent: {}
[208.801257] (-) TimerEvent: {}
[208.901705] (-) TimerEvent: {}
[209.002246] (-) TimerEvent: {}
[209.102693] (-) TimerEvent: {}
[209.203335] (-) TimerEvent: {}
[209.303796] (-) TimerEvent: {}
[209.404283] (-) TimerEvent: {}
[209.505343] (-) TimerEvent: {}
[209.606203] (-) TimerEvent: {}
[209.706653] (-) TimerEvent: {}
[209.807149] (-) TimerEvent: {}
[209.907596] (-) TimerEvent: {}
[210.008237] (-) TimerEvent: {}
[210.108617] (-) TimerEvent: {}
[210.210483] (-) TimerEvent: {}
[210.311383] (-) TimerEvent: {}
[210.411855] (-) TimerEvent: {}
[210.512900] (-) TimerEvent: {}
[210.614196] (-) TimerEvent: {}
[210.714767] (-) TimerEvent: {}
[210.815657] (-) TimerEvent: {}
[210.916111] (-) TimerEvent: {}
[211.016579] (-) TimerEvent: {}
[211.117008] (-) TimerEvent: {}
[211.217592] (-) TimerEvent: {}
[211.318282] (-) TimerEvent: {}
[211.419143] (-) TimerEvent: {}
[211.519848] (-) TimerEvent: {}
[211.620975] (-) TimerEvent: {}
[211.722190] (-) TimerEvent: {}
[211.822574] (-) TimerEvent: {}
[211.923441] (-) TimerEvent: {}
[212.023974] (-) TimerEvent: {}
[212.125250] (-) TimerEvent: {}
[212.226741] (-) TimerEvent: {}
[212.327357] (-) TimerEvent: {}
[212.428327] (-) TimerEvent: {}
[212.528928] (-) TimerEvent: {}
[212.629310] (-) TimerEvent: {}
[212.729691] (-) TimerEvent: {}
[212.830330] (-) TimerEvent: {}
[212.894225] (ros1_bridge) StdoutLine: {'line': b'[ 29%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PointStamped__factories.cpp.o\x1b[0m\n'}
[212.930402] (-) TimerEvent: {}
[213.031010] (-) TimerEvent: {}
[213.132094] (-) TimerEvent: {}
[213.233194] (-) TimerEvent: {}
[213.334520] (-) TimerEvent: {}
[213.435162] (-) TimerEvent: {}
[213.535655] (-) TimerEvent: {}
[213.636267] (-) TimerEvent: {}
[213.737741] (-) TimerEvent: {}
[213.839111] (-) TimerEvent: {}
[213.939491] (-) TimerEvent: {}
[214.040243] (-) TimerEvent: {}
[214.141425] (-) TimerEvent: {}
[214.242557] (-) TimerEvent: {}
[214.342939] (-) TimerEvent: {}
[214.443727] (-) TimerEvent: {}
[214.544278] (-) TimerEvent: {}
[214.645294] (-) TimerEvent: {}
[214.746200] (-) TimerEvent: {}
[214.846516] (-) TimerEvent: {}
[214.947285] (-) TimerEvent: {}
[215.047625] (-) TimerEvent: {}
[215.148018] (-) TimerEvent: {}
[215.248314] (-) TimerEvent: {}
[215.348730] (-) TimerEvent: {}
[215.449561] (-) TimerEvent: {}
[215.550549] (-) TimerEvent: {}
[215.650862] (-) TimerEvent: {}
[215.751488] (-) TimerEvent: {}
[215.851812] (-) TimerEvent: {}
[215.952721] (-) TimerEvent: {}
[216.053531] (-) TimerEvent: {}
[216.154580] (-) TimerEvent: {}
[216.255495] (-) TimerEvent: {}
[216.355805] (-) TimerEvent: {}
[216.456201] (-) TimerEvent: {}
[216.556772] (-) TimerEvent: {}
[216.657546] (-) TimerEvent: {}
[216.758893] (-) TimerEvent: {}
[216.859590] (-) TimerEvent: {}
[216.962146] (-) TimerEvent: {}
[217.063292] (-) TimerEvent: {}
[217.163660] (-) TimerEvent: {}
[217.263994] (-) TimerEvent: {}
[217.365032] (-) TimerEvent: {}
[217.466266] (-) TimerEvent: {}
[217.567111] (-) TimerEvent: {}
[217.667778] (-) TimerEvent: {}
[217.768031] (-) TimerEvent: {}
[217.868303] (-) TimerEvent: {}
[217.968573] (-) TimerEvent: {}
[218.068879] (-) TimerEvent: {}
[218.169453] (-) TimerEvent: {}
[218.270174] (-) TimerEvent: {}
[218.370943] (-) TimerEvent: {}
[218.471625] (-) TimerEvent: {}
[218.572060] (-) TimerEvent: {}
[218.672317] (-) TimerEvent: {}
[218.773134] (-) TimerEvent: {}
[218.874141] (-) TimerEvent: {}
[218.974436] (-) TimerEvent: {}
[219.075259] (-) TimerEvent: {}
[219.176204] (-) TimerEvent: {}
[219.277413] (-) TimerEvent: {}
[219.379101] (-) TimerEvent: {}
[219.479753] (-) TimerEvent: {}
[219.580693] (-) TimerEvent: {}
[219.681327] (-) TimerEvent: {}
[219.685409] (ros1_bridge) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Polygon__factories.cpp.o\x1b[0m\n'}
[219.782189] (-) TimerEvent: {}
[219.882654] (-) TimerEvent: {}
[219.983515] (-) TimerEvent: {}
[220.083817] (-) TimerEvent: {}
[220.184292] (-) TimerEvent: {}
[220.285001] (-) TimerEvent: {}
[220.385601] (-) TimerEvent: {}
[220.487070] (-) TimerEvent: {}
[220.587468] (-) TimerEvent: {}
[220.687768] (-) TimerEvent: {}
[220.788061] (-) TimerEvent: {}
[220.889252] (-) TimerEvent: {}
[220.989560] (-) TimerEvent: {}
[221.090170] (-) TimerEvent: {}
[221.190533] (-) TimerEvent: {}
[221.291143] (-) TimerEvent: {}
[221.392319] (-) TimerEvent: {}
[221.493144] (-) TimerEvent: {}
[221.594290] (-) TimerEvent: {}
[221.695255] (-) TimerEvent: {}
[221.795685] (-) TimerEvent: {}
[221.896417] (-) TimerEvent: {}
[221.996859] (-) TimerEvent: {}
[222.097321] (-) TimerEvent: {}
[222.198190] (-) TimerEvent: {}
[222.298644] (-) TimerEvent: {}
[222.399030] (-) TimerEvent: {}
[222.499511] (-) TimerEvent: {}
[222.600601] (-) TimerEvent: {}
[222.701556] (-) TimerEvent: {}
[222.802166] (-) TimerEvent: {}
[222.902549] (-) TimerEvent: {}
[223.003264] (-) TimerEvent: {}
[223.103621] (-) TimerEvent: {}
[223.204074] (-) TimerEvent: {}
[223.304522] (-) TimerEvent: {}
[223.404997] (-) TimerEvent: {}
[223.505313] (-) TimerEvent: {}
[223.605784] (-) TimerEvent: {}
[223.706313] (-) TimerEvent: {}
[223.807273] (-) TimerEvent: {}
[223.908802] (-) TimerEvent: {}
[224.011147] (-) TimerEvent: {}
[224.111507] (-) TimerEvent: {}
[224.212137] (-) TimerEvent: {}
[224.312424] (-) TimerEvent: {}
[224.412972] (-) TimerEvent: {}
[224.513271] (-) TimerEvent: {}
[224.614173] (-) TimerEvent: {}
[224.714601] (-) TimerEvent: {}
[224.815747] (-) TimerEvent: {}
[224.917131] (-) TimerEvent: {}
[225.017633] (-) TimerEvent: {}
[225.118233] (-) TimerEvent: {}
[225.218524] (-) TimerEvent: {}
[225.318795] (-) TimerEvent: {}
[225.419117] (-) TimerEvent: {}
[225.519455] (-) TimerEvent: {}
[225.619762] (-) TimerEvent: {}
[225.707661] (ros1_bridge) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PolygonStamped__factories.cpp.o\x1b[0m\n'}
[225.719845] (-) TimerEvent: {}
[225.820228] (-) TimerEvent: {}
[225.921313] (-) TimerEvent: {}
[226.022153] (-) TimerEvent: {}
[226.122539] (-) TimerEvent: {}
[226.223163] (-) TimerEvent: {}
[226.323633] (-) TimerEvent: {}
[226.424164] (-) TimerEvent: {}
[226.524513] (-) TimerEvent: {}
[226.625095] (-) TimerEvent: {}
[226.726156] (-) TimerEvent: {}
[226.774233] (ros1_bridge) StdoutLine: {'line': b'[ 30%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Pose__factories.cpp.o\x1b[0m\n'}
[226.826252] (-) TimerEvent: {}
[226.926629] (-) TimerEvent: {}
[227.027000] (-) TimerEvent: {}
[227.127564] (-) TimerEvent: {}
[227.228097] (-) TimerEvent: {}
[227.329017] (-) TimerEvent: {}
[227.430221] (-) TimerEvent: {}
[227.530610] (-) TimerEvent: {}
[227.631007] (-) TimerEvent: {}
[227.731878] (-) TimerEvent: {}
[227.832281] (-) TimerEvent: {}
[227.933211] (-) TimerEvent: {}
[228.033607] (-) TimerEvent: {}
[228.134264] (-) TimerEvent: {}
[228.234896] (-) TimerEvent: {}
[228.335311] (-) TimerEvent: {}
[228.435704] (-) TimerEvent: {}
[228.536484] (-) TimerEvent: {}
[228.638150] (-) TimerEvent: {}
[228.738546] (-) TimerEvent: {}
[228.839059] (-) TimerEvent: {}
[228.939536] (-) TimerEvent: {}
[229.040177] (-) TimerEvent: {}
[229.141214] (-) TimerEvent: {}
[229.242265] (-) TimerEvent: {}
[229.343163] (-) TimerEvent: {}
[229.443551] (-) TimerEvent: {}
[229.544106] (-) TimerEvent: {}
[229.645318] (-) TimerEvent: {}
[229.746176] (-) TimerEvent: {}
[229.846574] (-) TimerEvent: {}
[229.946932] (-) TimerEvent: {}
[230.047357] (-) TimerEvent: {}
[230.147748] (-) TimerEvent: {}
[230.248185] (-) TimerEvent: {}
[230.349002] (-) TimerEvent: {}
[230.449532] (-) TimerEvent: {}
[230.550280] (-) TimerEvent: {}
[230.650712] (-) TimerEvent: {}
[230.751110] (-) TimerEvent: {}
[230.851535] (-) TimerEvent: {}
[230.951949] (-) TimerEvent: {}
[231.052581] (-) TimerEvent: {}
[231.153212] (-) TimerEvent: {}
[231.254172] (-) TimerEvent: {}
[231.354600] (-) TimerEvent: {}
[231.454997] (-) TimerEvent: {}
[231.555537] (-) TimerEvent: {}
[231.656368] (-) TimerEvent: {}
[231.756927] (-) TimerEvent: {}
[231.857341] (-) TimerEvent: {}
[231.958353] (-) TimerEvent: {}
[232.058986] (-) TimerEvent: {}
[232.159729] (-) TimerEvent: {}
[232.260052] (-) TimerEvent: {}
[232.361116] (-) TimerEvent: {}
[232.462157] (-) TimerEvent: {}
[232.562585] (-) TimerEvent: {}
[232.663262] (-) TimerEvent: {}
[232.763788] (-) TimerEvent: {}
[232.864179] (-) TimerEvent: {}
[232.964548] (-) TimerEvent: {}
[233.064929] (-) TimerEvent: {}
[233.165528] (-) TimerEvent: {}
[233.267946] (-) TimerEvent: {}
[233.370246] (-) TimerEvent: {}
[233.470638] (-) TimerEvent: {}
[233.571055] (-) TimerEvent: {}
[233.671512] (-) TimerEvent: {}
[233.771935] (-) TimerEvent: {}
[233.825126] (ros1_bridge) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Pose2D__factories.cpp.o\x1b[0m\n'}
[233.872137] (-) TimerEvent: {}
[233.973047] (-) TimerEvent: {}
[234.074160] (-) TimerEvent: {}
[234.174708] (-) TimerEvent: {}
[234.275193] (-) TimerEvent: {}
[234.375566] (-) TimerEvent: {}
[234.476223] (-) TimerEvent: {}
[234.576599] (-) TimerEvent: {}
[234.678896] (-) TimerEvent: {}
[234.694901] (ros1_bridge) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseArray__factories.cpp.o\x1b[0m\n'}
[234.779012] (-) TimerEvent: {}
[234.879487] (-) TimerEvent: {}
[234.980288] (-) TimerEvent: {}
[235.081101] (-) TimerEvent: {}
[235.182144] (-) TimerEvent: {}
[235.282520] (-) TimerEvent: {}
[235.382935] (-) TimerEvent: {}
[235.483315] (-) TimerEvent: {}
[235.583899] (-) TimerEvent: {}
[235.684376] (-) TimerEvent: {}
[235.785211] (-) TimerEvent: {}
[235.886179] (-) TimerEvent: {}
[235.986924] (-) TimerEvent: {}
[236.087531] (-) TimerEvent: {}
[236.188027] (-) TimerEvent: {}
[236.289262] (-) TimerEvent: {}
[236.390198] (-) TimerEvent: {}
[236.490600] (-) TimerEvent: {}
[236.590991] (-) TimerEvent: {}
[236.691622] (-) TimerEvent: {}
[236.792058] (-) TimerEvent: {}
[236.892497] (-) TimerEvent: {}
[236.993004] (-) TimerEvent: {}
[237.093415] (-) TimerEvent: {}
[237.194192] (-) TimerEvent: {}
[237.295104] (-) TimerEvent: {}
[237.395507] (-) TimerEvent: {}
[237.495942] (-) TimerEvent: {}
[237.596355] (-) TimerEvent: {}
[237.697137] (-) TimerEvent: {}
[237.798158] (-) TimerEvent: {}
[237.898566] (-) TimerEvent: {}
[237.998954] (-) TimerEvent: {}
[238.099912] (-) TimerEvent: {}
[238.200405] (-) TimerEvent: {}
[238.300900] (-) TimerEvent: {}
[238.402147] (-) TimerEvent: {}
[238.502770] (-) TimerEvent: {}
[238.603371] (-) TimerEvent: {}
[238.703804] (-) TimerEvent: {}
[238.804311] (-) TimerEvent: {}
[238.904796] (-) TimerEvent: {}
[239.005223] (-) TimerEvent: {}
[239.105795] (-) TimerEvent: {}
[239.206145] (-) TimerEvent: {}
[239.306578] (-) TimerEvent: {}
[239.407004] (-) TimerEvent: {}
[239.507832] (-) TimerEvent: {}
[239.608346] (-) TimerEvent: {}
[239.708759] (-) TimerEvent: {}
[239.810323] (-) TimerEvent: {}
[239.911413] (-) TimerEvent: {}
[240.012019] (-) TimerEvent: {}
[240.113162] (-) TimerEvent: {}
[240.214158] (-) TimerEvent: {}
[240.315222] (-) TimerEvent: {}
[240.415679] (-) TimerEvent: {}
[240.516273] (-) TimerEvent: {}
[240.617110] (-) TimerEvent: {}
[240.717493] (-) TimerEvent: {}
[240.818179] (-) TimerEvent: {}
[240.918886] (-) TimerEvent: {}
[241.019593] (-) TimerEvent: {}
[241.120248] (-) TimerEvent: {}
[241.220843] (-) TimerEvent: {}
[241.321244] (-) TimerEvent: {}
[241.422276] (-) TimerEvent: {}
[241.522638] (-) TimerEvent: {}
[241.622991] (-) TimerEvent: {}
[241.723332] (-) TimerEvent: {}
[241.823724] (-) TimerEvent: {}
[241.924800] (-) TimerEvent: {}
[242.025165] (-) TimerEvent: {}
[242.045238] (ros1_bridge) StdoutLine: {'line': b'[ 31%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseStamped__factories.cpp.o\x1b[0m\n'}
[242.130728] (-) TimerEvent: {}
[242.234176] (-) TimerEvent: {}
[242.334532] (-) TimerEvent: {}
[242.435218] (-) TimerEvent: {}
[242.535579] (-) TimerEvent: {}
[242.636138] (-) TimerEvent: {}
[242.736495] (-) TimerEvent: {}
[242.830554] (ros1_bridge) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseWithCovariance__factories.cpp.o\x1b[0m\n'}
[242.836924] (-) TimerEvent: {}
[242.937287] (-) TimerEvent: {}
[243.038155] (-) TimerEvent: {}
[243.138821] (-) TimerEvent: {}
[243.239717] (-) TimerEvent: {}
[243.340124] (-) TimerEvent: {}
[243.440726] (-) TimerEvent: {}
[243.542151] (-) TimerEvent: {}
[243.642592] (-) TimerEvent: {}
[243.743138] (-) TimerEvent: {}
[243.843843] (-) TimerEvent: {}
[243.944252] (-) TimerEvent: {}
[244.044980] (-) TimerEvent: {}
[244.146155] (-) TimerEvent: {}
[244.247185] (-) TimerEvent: {}
[244.347676] (-) TimerEvent: {}
[244.448252] (-) TimerEvent: {}
[244.549230] (-) TimerEvent: {}
[244.650158] (-) TimerEvent: {}
[244.750892] (-) TimerEvent: {}
[244.851809] (-) TimerEvent: {}
[244.952249] (-) TimerEvent: {}
[245.052870] (-) TimerEvent: {}
[245.153633] (-) TimerEvent: {}
[245.254711] (-) TimerEvent: {}
[245.355318] (-) TimerEvent: {}
[245.455736] (-) TimerEvent: {}
[245.556376] (-) TimerEvent: {}
[245.657026] (-) TimerEvent: {}
[245.757585] (-) TimerEvent: {}
[245.859160] (-) TimerEvent: {}
[245.959654] (-) TimerEvent: {}
[246.060133] (-) TimerEvent: {}
[246.160623] (-) TimerEvent: {}
[246.261344] (-) TimerEvent: {}
[246.362712] (-) TimerEvent: {}
[246.463456] (-) TimerEvent: {}
[246.563918] (-) TimerEvent: {}
[246.664588] (-) TimerEvent: {}
[246.765153] (-) TimerEvent: {}
[246.865614] (-) TimerEvent: {}
[246.966145] (-) TimerEvent: {}
[247.066710] (-) TimerEvent: {}
[247.167298] (-) TimerEvent: {}
[247.267744] (-) TimerEvent: {}
[247.368225] (-) TimerEvent: {}
[247.468896] (-) TimerEvent: {}
[247.570236] (-) TimerEvent: {}
[247.671032] (-) TimerEvent: {}
[247.771736] (-) TimerEvent: {}
[247.872154] (-) TimerEvent: {}
[247.972890] (-) TimerEvent: {}
[248.073551] (-) TimerEvent: {}
[248.174145] (-) TimerEvent: {}
[248.274698] (-) TimerEvent: {}
[248.375603] (-) TimerEvent: {}
[248.475963] (-) TimerEvent: {}
[248.576391] (-) TimerEvent: {}
[248.676795] (-) TimerEvent: {}
[248.778161] (-) TimerEvent: {}
[248.878982] (-) TimerEvent: {}
[248.979420] (-) TimerEvent: {}
[249.079879] (-) TimerEvent: {}
[249.180313] (-) TimerEvent: {}
[249.280927] (-) TimerEvent: {}
[249.381470] (-) TimerEvent: {}
[249.482538] (-) TimerEvent: {}
[249.583033] (-) TimerEvent: {}
[249.683819] (-) TimerEvent: {}
[249.784866] (-) TimerEvent: {}
[249.886163] (-) TimerEvent: {}
[249.986617] (-) TimerEvent: {}
[250.000358] (ros1_bridge) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseWithCovarianceStamped__factories.cpp.o\x1b[0m\n'}
[250.093630] (-) TimerEvent: {}
[250.202719] (-) TimerEvent: {}
[250.303177] (-) TimerEvent: {}
[250.403611] (-) TimerEvent: {}
[250.504044] (-) TimerEvent: {}
[250.604565] (-) TimerEvent: {}
[250.705011] (-) TimerEvent: {}
[250.805546] (-) TimerEvent: {}
[250.823741] (ros1_bridge) StdoutLine: {'line': b'[ 32%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Quaternion__factories.cpp.o\x1b[0m\n'}
[250.905664] (-) TimerEvent: {}
[251.006630] (-) TimerEvent: {}
[251.107068] (-) TimerEvent: {}
[251.207886] (-) TimerEvent: {}
[251.308987] (-) TimerEvent: {}
[251.410167] (-) TimerEvent: {}
[251.510921] (-) TimerEvent: {}
[251.611425] (-) TimerEvent: {}
[251.712566] (-) TimerEvent: {}
[251.813627] (-) TimerEvent: {}
[251.914299] (-) TimerEvent: {}
[252.015156] (-) TimerEvent: {}
[252.115535] (-) TimerEvent: {}
[252.216023] (-) TimerEvent: {}
[252.316441] (-) TimerEvent: {}
[252.417470] (-) TimerEvent: {}
[252.518178] (-) TimerEvent: {}
[252.619018] (-) TimerEvent: {}
[252.719502] (-) TimerEvent: {}
[252.819976] (-) TimerEvent: {}
[252.920455] (-) TimerEvent: {}
[253.021168] (-) TimerEvent: {}
[253.122209] (-) TimerEvent: {}
[253.222984] (-) TimerEvent: {}
[253.323626] (-) TimerEvent: {}
[253.424082] (-) TimerEvent: {}
[253.524525] (-) TimerEvent: {}
[253.625000] (-) TimerEvent: {}
[253.725624] (-) TimerEvent: {}
[253.826642] (-) TimerEvent: {}
[253.927134] (-) TimerEvent: {}
[254.027832] (-) TimerEvent: {}
[254.128292] (-) TimerEvent: {}
[254.228830] (-) TimerEvent: {}
[254.330171] (-) TimerEvent: {}
[254.430613] (-) TimerEvent: {}
[254.531023] (-) TimerEvent: {}
[254.631497] (-) TimerEvent: {}
[254.731956] (-) TimerEvent: {}
[254.833055] (-) TimerEvent: {}
[254.933439] (-) TimerEvent: {}
[255.034500] (-) TimerEvent: {}
[255.135118] (-) TimerEvent: {}
[255.236043] (-) TimerEvent: {}
[255.336725] (-) TimerEvent: {}
[255.438140] (-) TimerEvent: {}
[255.538528] (-) TimerEvent: {}
[255.638992] (-) TimerEvent: {}
[255.739608] (-) TimerEvent: {}
[255.840229] (-) TimerEvent: {}
[255.940620] (-) TimerEvent: {}
[256.041253] (-) TimerEvent: {}
[256.142210] (-) TimerEvent: {}
[256.242606] (-) TimerEvent: {}
[256.343035] (-) TimerEvent: {}
[256.443524] (-) TimerEvent: {}
[256.543945] (-) TimerEvent: {}
[256.644629] (-) TimerEvent: {}
[256.745643] (-) TimerEvent: {}
[256.846176] (-) TimerEvent: {}
[256.946758] (-) TimerEvent: {}
[257.047119] (-) TimerEvent: {}
[257.147503] (-) TimerEvent: {}
[257.247980] (-) TimerEvent: {}
[257.348963] (-) TimerEvent: {}
[257.450133] (-) TimerEvent: {}
[257.550492] (-) TimerEvent: {}
[257.650959] (-) TimerEvent: {}
[257.751580] (-) TimerEvent: {}
[257.852014] (-) TimerEvent: {}
[257.952572] (-) TimerEvent: {}
[258.053049] (-) TimerEvent: {}
[258.154205] (-) TimerEvent: {}
[258.254924] (-) TimerEvent: {}
[258.355331] (-) TimerEvent: {}
[258.414423] (ros1_bridge) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__QuaternionStamped__factories.cpp.o\x1b[0m\n'}
[258.455503] (-) TimerEvent: {}
[258.556126] (-) TimerEvent: {}
[258.657028] (-) TimerEvent: {}
[258.757458] (-) TimerEvent: {}
[258.858163] (-) TimerEvent: {}
[258.958868] (-) TimerEvent: {}
[259.059277] (-) TimerEvent: {}
[259.159691] (-) TimerEvent: {}
[259.188565] (ros1_bridge) StdoutLine: {'line': b'[ 33%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Transform__factories.cpp.o\x1b[0m\n'}
[259.259774] (-) TimerEvent: {}
[259.360167] (-) TimerEvent: {}
[259.461142] (-) TimerEvent: {}
[259.562214] (-) TimerEvent: {}
[259.662692] (-) TimerEvent: {}
[259.763209] (-) TimerEvent: {}
[259.863816] (-) TimerEvent: {}
[259.964657] (-) TimerEvent: {}
[260.065365] (-) TimerEvent: {}
[260.166156] (-) TimerEvent: {}
[260.266556] (-) TimerEvent: {}
[260.366933] (-) TimerEvent: {}
[260.467762] (-) TimerEvent: {}
[260.568342] (-) TimerEvent: {}
[260.669053] (-) TimerEvent: {}
[260.769510] (-) TimerEvent: {}
[260.870154] (-) TimerEvent: {}
[260.970564] (-) TimerEvent: {}
[261.070929] (-) TimerEvent: {}
[261.171355] (-) TimerEvent: {}
[261.271939] (-) TimerEvent: {}
[261.372382] (-) TimerEvent: {}
[261.473312] (-) TimerEvent: {}
[261.574160] (-) TimerEvent: {}
[261.674708] (-) TimerEvent: {}
[261.775762] (-) TimerEvent: {}
[261.876355] (-) TimerEvent: {}
[261.976847] (-) TimerEvent: {}
[262.078183] (-) TimerEvent: {}
[262.178581] (-) TimerEvent: {}
[262.279276] (-) TimerEvent: {}
[262.379836] (-) TimerEvent: {}
[262.480351] (-) TimerEvent: {}
[262.580739] (-) TimerEvent: {}
[262.681301] (-) TimerEvent: {}
[262.782276] (-) TimerEvent: {}
[262.883327] (-) TimerEvent: {}
[262.983937] (-) TimerEvent: {}
[263.084345] (-) TimerEvent: {}
[263.184994] (-) TimerEvent: {}
[263.286146] (-) TimerEvent: {}
[263.386539] (-) TimerEvent: {}
[263.486928] (-) TimerEvent: {}
[263.587545] (-) TimerEvent: {}
[263.687941] (-) TimerEvent: {}
[263.788356] (-) TimerEvent: {}
[263.889025] (-) TimerEvent: {}
[263.990145] (-) TimerEvent: {}
[264.090549] (-) TimerEvent: {}
[264.190940] (-) TimerEvent: {}
[264.291386] (-) TimerEvent: {}
[264.391962] (-) TimerEvent: {}
[264.492507] (-) TimerEvent: {}
[264.593272] (-) TimerEvent: {}
[264.693684] (-) TimerEvent: {}
[264.794453] (-) TimerEvent: {}
[264.895026] (-) TimerEvent: {}
[264.995486] (-) TimerEvent: {}
[265.095857] (-) TimerEvent: {}
[265.196254] (-) TimerEvent: {}
[265.296636] (-) TimerEvent: {}
[265.397016] (-) TimerEvent: {}
[265.497401] (-) TimerEvent: {}
[265.598372] (-) TimerEvent: {}
[265.698832] (-) TimerEvent: {}
[265.799381] (-) TimerEvent: {}
[265.899725] (-) TimerEvent: {}
[266.000112] (-) TimerEvent: {}
[266.101214] (-) TimerEvent: {}
[266.202382] (-) TimerEvent: {}
[266.303309] (-) TimerEvent: {}
[266.403740] (-) TimerEvent: {}
[266.504550] (-) TimerEvent: {}
[266.605296] (-) TimerEvent: {}
[266.706271] (-) TimerEvent: {}
[266.808483] (-) TimerEvent: {}
[266.861310] (ros1_bridge) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TransformStamped__factories.cpp.o\x1b[0m\n'}
[266.909313] (-) TimerEvent: {}
[267.010184] (-) TimerEvent: {}
[267.110538] (-) TimerEvent: {}
[267.211368] (-) TimerEvent: {}
[267.215738] (ros1_bridge) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Twist__factories.cpp.o\x1b[0m\n'}
[267.311466] (-) TimerEvent: {}
[267.411922] (-) TimerEvent: {}
[267.512338] (-) TimerEvent: {}
[267.613322] (-) TimerEvent: {}
[267.714171] (-) TimerEvent: {}
[267.814832] (-) TimerEvent: {}
[267.915345] (-) TimerEvent: {}
[268.016297] (-) TimerEvent: {}
[268.117217] (-) TimerEvent: {}
[268.218574] (-) TimerEvent: {}
[268.319139] (-) TimerEvent: {}
[268.419612] (-) TimerEvent: {}
[268.520600] (-) TimerEvent: {}
[268.621121] (-) TimerEvent: {}
[268.721524] (-) TimerEvent: {}
[268.822208] (-) TimerEvent: {}
[268.922634] (-) TimerEvent: {}
[269.023578] (-) TimerEvent: {}
[269.124075] (-) TimerEvent: {}
[269.225185] (-) TimerEvent: {}
[269.326208] (-) TimerEvent: {}
[269.426757] (-) TimerEvent: {}
[269.527541] (-) TimerEvent: {}
[269.628063] (-) TimerEvent: {}
[269.728539] (-) TimerEvent: {}
[269.829472] (-) TimerEvent: {}
[269.930498] (-) TimerEvent: {}
[270.030905] (-) TimerEvent: {}
[270.131424] (-) TimerEvent: {}
[270.231951] (-) TimerEvent: {}
[270.332404] (-) TimerEvent: {}
[270.433503] (-) TimerEvent: {}
[270.534771] (-) TimerEvent: {}
[270.635225] (-) TimerEvent: {}
[270.735708] (-) TimerEvent: {}
[270.836223] (-) TimerEvent: {}
[270.937109] (-) TimerEvent: {}
[271.037622] (-) TimerEvent: {}
[271.138212] (-) TimerEvent: {}
[271.239065] (-) TimerEvent: {}
[271.339556] (-) TimerEvent: {}
[271.440064] (-) TimerEvent: {}
[271.541333] (-) TimerEvent: {}
[271.642242] (-) TimerEvent: {}
[271.742863] (-) TimerEvent: {}
[271.843377] (-) TimerEvent: {}
[271.943845] (-) TimerEvent: {}
[272.044280] (-) TimerEvent: {}
[272.144685] (-) TimerEvent: {}
[272.245250] (-) TimerEvent: {}
[272.346171] (-) TimerEvent: {}
[272.446528] (-) TimerEvent: {}
[272.547667] (-) TimerEvent: {}
[272.648181] (-) TimerEvent: {}
[272.748779] (-) TimerEvent: {}
[272.849493] (-) TimerEvent: {}
[272.950233] (-) TimerEvent: {}
[273.050623] (-) TimerEvent: {}
[273.151107] (-) TimerEvent: {}
[273.251646] (-) TimerEvent: {}
[273.352037] (-) TimerEvent: {}
[273.453025] (-) TimerEvent: {}
[273.554159] (-) TimerEvent: {}
[273.654562] (-) TimerEvent: {}
[273.755018] (-) TimerEvent: {}
[273.855447] (-) TimerEvent: {}
[273.955821] (-) TimerEvent: {}
[274.056238] (-) TimerEvent: {}
[274.157024] (-) TimerEvent: {}
[274.258176] (-) TimerEvent: {}
[274.360489] (-) TimerEvent: {}
[274.460992] (-) TimerEvent: {}
[274.562169] (-) TimerEvent: {}
[274.662536] (-) TimerEvent: {}
[274.763115] (-) TimerEvent: {}
[274.865350] (-) TimerEvent: {}
[274.966159] (-) TimerEvent: {}
[275.010963] (ros1_bridge) StdoutLine: {'line': b'[ 34%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistStamped__factories.cpp.o\x1b[0m\n'}
[275.074192] (-) TimerEvent: {}
[275.178171] (-) TimerEvent: {}
[275.278503] (-) TimerEvent: {}
[275.379184] (-) TimerEvent: {}
[275.403187] (ros1_bridge) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistWithCovariance__factories.cpp.o\x1b[0m\n'}
[275.479271] (-) TimerEvent: {}
[275.579806] (-) TimerEvent: {}
[275.680208] (-) TimerEvent: {}
[275.780583] (-) TimerEvent: {}
[275.881253] (-) TimerEvent: {}
[275.982267] (-) TimerEvent: {}
[276.082672] (-) TimerEvent: {}
[276.183081] (-) TimerEvent: {}
[276.283771] (-) TimerEvent: {}
[276.384914] (-) TimerEvent: {}
[276.486171] (-) TimerEvent: {}
[276.586626] (-) TimerEvent: {}
[276.687094] (-) TimerEvent: {}
[276.787587] (-) TimerEvent: {}
[276.888560] (-) TimerEvent: {}
[276.989238] (-) TimerEvent: {}
[277.090180] (-) TimerEvent: {}
[277.190630] (-) TimerEvent: {}
[277.291102] (-) TimerEvent: {}
[277.391662] (-) TimerEvent: {}
[277.492134] (-) TimerEvent: {}
[277.593344] (-) TimerEvent: {}
[277.694837] (-) TimerEvent: {}
[277.795457] (-) TimerEvent: {}
[277.895882] (-) TimerEvent: {}
[277.996278] (-) TimerEvent: {}
[278.097432] (-) TimerEvent: {}
[278.198156] (-) TimerEvent: {}
[278.298570] (-) TimerEvent: {}
[278.399088] (-) TimerEvent: {}
[278.499493] (-) TimerEvent: {}
[278.599936] (-) TimerEvent: {}
[278.701041] (-) TimerEvent: {}
[278.802162] (-) TimerEvent: {}
[278.902573] (-) TimerEvent: {}
[279.003247] (-) TimerEvent: {}
[279.104080] (-) TimerEvent: {}
[279.204497] (-) TimerEvent: {}
[279.304882] (-) TimerEvent: {}
[279.405218] (-) TimerEvent: {}
[279.506155] (-) TimerEvent: {}
[279.606540] (-) TimerEvent: {}
[279.706951] (-) TimerEvent: {}
[279.807675] (-) TimerEvent: {}
[279.908037] (-) TimerEvent: {}
[280.008402] (-) TimerEvent: {}
[280.109365] (-) TimerEvent: {}
[280.210158] (-) TimerEvent: {}
[280.310943] (-) TimerEvent: {}
[280.411585] (-) TimerEvent: {}
[280.511931] (-) TimerEvent: {}
[280.612401] (-) TimerEvent: {}
[280.712922] (-) TimerEvent: {}
[280.814146] (-) TimerEvent: {}
[280.914548] (-) TimerEvent: {}
[281.015002] (-) TimerEvent: {}
[281.115871] (-) TimerEvent: {}
[281.216896] (-) TimerEvent: {}
[281.317666] (-) TimerEvent: {}
[281.418477] (-) TimerEvent: {}
[281.518922] (-) TimerEvent: {}
[281.619467] (-) TimerEvent: {}
[281.720002] (-) TimerEvent: {}
[281.820520] (-) TimerEvent: {}
[281.920972] (-) TimerEvent: {}
[282.022135] (-) TimerEvent: {}
[282.122451] (-) TimerEvent: {}
[282.223248] (-) TimerEvent: {}
[282.323665] (-) TimerEvent: {}
[282.424100] (-) TimerEvent: {}
[282.524509] (-) TimerEvent: {}
[282.624951] (-) TimerEvent: {}
[282.726156] (-) TimerEvent: {}
[282.826567] (-) TimerEvent: {}
[282.926955] (-) TimerEvent: {}
[283.027265] (-) TimerEvent: {}
[283.127828] (-) TimerEvent: {}
[283.228390] (-) TimerEvent: {}
[283.329031] (-) TimerEvent: {}
[283.429594] (-) TimerEvent: {}
[283.439142] (ros1_bridge) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistWithCovarianceStamped__factories.cpp.o\x1b[0m\n'}
[283.448969] (ros1_bridge) StdoutLine: {'line': b'[ 35%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Vector3__factories.cpp.o\x1b[0m\n'}
[283.530203] (-) TimerEvent: {}
[283.630800] (-) TimerEvent: {}
[283.731564] (-) TimerEvent: {}
[283.832067] (-) TimerEvent: {}
[283.932845] (-) TimerEvent: {}
[284.033240] (-) TimerEvent: {}
[284.133679] (-) TimerEvent: {}
[284.234192] (-) TimerEvent: {}
[284.334610] (-) TimerEvent: {}
[284.435063] (-) TimerEvent: {}
[284.535474] (-) TimerEvent: {}
[284.635964] (-) TimerEvent: {}
[284.736377] (-) TimerEvent: {}
[284.837337] (-) TimerEvent: {}
[284.938536] (-) TimerEvent: {}
[285.039364] (-) TimerEvent: {}
[285.139944] (-) TimerEvent: {}
[285.241105] (-) TimerEvent: {}
[285.342188] (-) TimerEvent: {}
[285.443217] (-) TimerEvent: {}
[285.543781] (-) TimerEvent: {}
[285.644291] (-) TimerEvent: {}
[285.744751] (-) TimerEvent: {}
[285.845257] (-) TimerEvent: {}
[285.946225] (-) TimerEvent: {}
[286.046760] (-) TimerEvent: {}
[286.147298] (-) TimerEvent: {}
[286.247808] (-) TimerEvent: {}
[286.348318] (-) TimerEvent: {}
[286.448876] (-) TimerEvent: {}
[286.549290] (-) TimerEvent: {}
[286.650234] (-) TimerEvent: {}
[286.751147] (-) TimerEvent: {}
[286.851594] (-) TimerEvent: {}
[286.952231] (-) TimerEvent: {}
[287.053285] (-) TimerEvent: {}
[287.154146] (-) TimerEvent: {}
[287.254769] (-) TimerEvent: {}
[287.355797] (-) TimerEvent: {}
[287.456265] (-) TimerEvent: {}
[287.556760] (-) TimerEvent: {}
[287.657227] (-) TimerEvent: {}
[287.758147] (-) TimerEvent: {}
[287.858616] (-) TimerEvent: {}
[287.959061] (-) TimerEvent: {}
[288.059517] (-) TimerEvent: {}
[288.159898] (-) TimerEvent: {}
[288.260313] (-) TimerEvent: {}
[288.361154] (-) TimerEvent: {}
[288.462226] (-) TimerEvent: {}
[288.562911] (-) TimerEvent: {}
[288.663408] (-) TimerEvent: {}
[288.763745] (-) TimerEvent: {}
[288.864283] (-) TimerEvent: {}
[288.965385] (-) TimerEvent: {}
[289.066175] (-) TimerEvent: {}
[289.167052] (-) TimerEvent: {}
[289.267452] (-) TimerEvent: {}
[289.368128] (-) TimerEvent: {}
[289.468490] (-) TimerEvent: {}
[289.569227] (-) TimerEvent: {}
[289.670285] (-) TimerEvent: {}
[289.770908] (-) TimerEvent: {}
[289.871639] (-) TimerEvent: {}
[289.971971] (-) TimerEvent: {}
[290.072377] (-) TimerEvent: {}
[290.172769] (-) TimerEvent: {}
[290.273172] (-) TimerEvent: {}
[290.373545] (-) TimerEvent: {}
[290.474181] (-) TimerEvent: {}
[290.574546] (-) TimerEvent: {}
[290.674951] (-) TimerEvent: {}
[290.775323] (-) TimerEvent: {}
[290.875870] (-) TimerEvent: {}
[290.976652] (-) TimerEvent: {}
[291.078144] (-) TimerEvent: {}
[291.178749] (-) TimerEvent: {}
[291.279129] (-) TimerEvent: {}
[291.379826] (-) TimerEvent: {}
[291.480172] (-) TimerEvent: {}
[291.557595] (ros1_bridge) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Vector3Stamped__factories.cpp.o\x1b[0m\n'}
[291.569427] (ros1_bridge) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Wrench__factories.cpp.o\x1b[0m\n'}
[291.580226] (-) TimerEvent: {}
[291.690263] (-) TimerEvent: {}
[291.790940] (-) TimerEvent: {}
[291.891648] (-) TimerEvent: {}
[291.992178] (-) TimerEvent: {}
[292.093313] (-) TimerEvent: {}
[292.194193] (-) TimerEvent: {}
[292.294967] (-) TimerEvent: {}
[292.395449] (-) TimerEvent: {}
[292.496147] (-) TimerEvent: {}
[292.596630] (-) TimerEvent: {}
[292.697595] (-) TimerEvent: {}
[292.798163] (-) TimerEvent: {}
[292.898709] (-) TimerEvent: {}
[292.999192] (-) TimerEvent: {}
[293.099927] (-) TimerEvent: {}
[293.200465] (-) TimerEvent: {}
[293.300992] (-) TimerEvent: {}
[293.402234] (-) TimerEvent: {}
[293.502688] (-) TimerEvent: {}
[293.603131] (-) TimerEvent: {}
[293.703689] (-) TimerEvent: {}
[293.804116] (-) TimerEvent: {}
[293.904583] (-) TimerEvent: {}
[294.005615] (-) TimerEvent: {}
[294.106170] (-) TimerEvent: {}
[294.206672] (-) TimerEvent: {}
[294.307150] (-) TimerEvent: {}
[294.407689] (-) TimerEvent: {}
[294.508227] (-) TimerEvent: {}
[294.608678] (-) TimerEvent: {}
[294.709037] (-) TimerEvent: {}
[294.810162] (-) TimerEvent: {}
[294.910967] (-) TimerEvent: {}
[295.011517] (-) TimerEvent: {}
[295.111992] (-) TimerEvent: {}
[295.212552] (-) TimerEvent: {}
[295.313329] (-) TimerEvent: {}
[295.414147] (-) TimerEvent: {}
[295.514508] (-) TimerEvent: {}
[295.615078] (-) TimerEvent: {}
[295.715530] (-) TimerEvent: {}
[295.815969] (-) TimerEvent: {}
[295.916437] (-) TimerEvent: {}
[296.016887] (-) TimerEvent: {}
[296.118151] (-) TimerEvent: {}
[296.218583] (-) TimerEvent: {}
[296.319088] (-) TimerEvent: {}
[296.419578] (-) TimerEvent: {}
[296.520044] (-) TimerEvent: {}
[296.621034] (-) TimerEvent: {}
[296.721595] (-) TimerEvent: {}
[296.822683] (-) TimerEvent: {}
[296.923357] (-) TimerEvent: {}
[297.023927] (-) TimerEvent: {}
[297.124611] (-) TimerEvent: {}
[297.225498] (-) TimerEvent: {}
[297.326555] (-) TimerEvent: {}
[297.427199] (-) TimerEvent: {}
[297.527783] (-) TimerEvent: {}
[297.628141] (-) TimerEvent: {}
[297.728859] (-) TimerEvent: {}
[297.829529] (-) TimerEvent: {}
[297.930235] (-) TimerEvent: {}
[298.030692] (-) TimerEvent: {}
[298.131681] (-) TimerEvent: {}
[298.232108] (-) TimerEvent: {}
[298.332528] (-) TimerEvent: {}
[298.433488] (-) TimerEvent: {}
[298.534141] (-) TimerEvent: {}
[298.634579] (-) TimerEvent: {}
[298.735133] (-) TimerEvent: {}
[298.835633] (-) TimerEvent: {}
[298.936008] (-) TimerEvent: {}
[299.036845] (-) TimerEvent: {}
[299.138147] (-) TimerEvent: {}
[299.238991] (-) TimerEvent: {}
[299.339388] (-) TimerEvent: {}
[299.439727] (-) TimerEvent: {}
[299.540094] (-) TimerEvent: {}
[299.640816] (-) TimerEvent: {}
[299.653806] (ros1_bridge) StdoutLine: {'line': b'[ 36%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__WrenchStamped__factories.cpp.o\x1b[0m\n'}
[299.741161] (-) TimerEvent: {}
[299.813489] (ros1_bridge) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/libstatistics_collector_factories.cpp.o\x1b[0m\n'}
[299.841233] (-) TimerEvent: {}
[299.942137] (-) TimerEvent: {}
[300.043012] (-) TimerEvent: {}
[300.143416] (-) TimerEvent: {}
[300.243794] (-) TimerEvent: {}
[300.344173] (-) TimerEvent: {}
[300.444526] (-) TimerEvent: {}
[300.551118] (-) TimerEvent: {}
[300.651563] (-) TimerEvent: {}
[300.751967] (-) TimerEvent: {}
[300.853075] (-) TimerEvent: {}
[300.954159] (-) TimerEvent: {}
[301.054593] (-) TimerEvent: {}
[301.155047] (-) TimerEvent: {}
[301.255443] (-) TimerEvent: {}
[301.356046] (-) TimerEvent: {}
[301.456987] (-) TimerEvent: {}
[301.558125] (-) TimerEvent: {}
[301.658441] (-) TimerEvent: {}
[301.758840] (-) TimerEvent: {}
[301.859665] (-) TimerEvent: {}
[301.960062] (-) TimerEvent: {}
[302.060516] (-) TimerEvent: {}
[302.160968] (-) TimerEvent: {}
[302.261500] (-) TimerEvent: {}
[302.362419] (-) TimerEvent: {}
[302.463158] (-) TimerEvent: {}
[302.564222] (-) TimerEvent: {}
[302.665022] (-) TimerEvent: {}
[302.765508] (-) TimerEvent: {}
[302.866203] (-) TimerEvent: {}
[302.966626] (-) TimerEvent: {}
[303.067093] (-) TimerEvent: {}
[303.168306] (-) TimerEvent: {}
[303.269390] (-) TimerEvent: {}
[303.370178] (-) TimerEvent: {}
[303.451614] (ros1_bridge) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/libstatistics_collector__msg__DummyMessage__factories.cpp.o\x1b[0m\n'}
[303.470456] (-) TimerEvent: {}
[303.571002] (-) TimerEvent: {}
[303.671420] (-) TimerEvent: {}
[303.771999] (-) TimerEvent: {}
[303.872943] (-) TimerEvent: {}
[303.973357] (-) TimerEvent: {}
[304.074198] (-) TimerEvent: {}
[304.174629] (-) TimerEvent: {}
[304.275517] (-) TimerEvent: {}
[304.375924] (-) TimerEvent: {}
[304.477078] (-) TimerEvent: {}
[304.578167] (-) TimerEvent: {}
[304.678559] (-) TimerEvent: {}
[304.779770] (-) TimerEvent: {}
[304.881032] (-) TimerEvent: {}
[304.982186] (-) TimerEvent: {}
[305.082687] (-) TimerEvent: {}
[305.183591] (-) TimerEvent: {}
[305.284029] (-) TimerEvent: {}
[305.384912] (-) TimerEvent: {}
[305.485363] (-) TimerEvent: {}
[305.586423] (-) TimerEvent: {}
[305.687330] (-) TimerEvent: {}
[305.787767] (-) TimerEvent: {}
[305.888104] (-) TimerEvent: {}
[305.988491] (-) TimerEvent: {}
[306.088930] (-) TimerEvent: {}
[306.190154] (-) TimerEvent: {}
[306.290488] (-) TimerEvent: {}
[306.391039] (-) TimerEvent: {}
[306.491645] (-) TimerEvent: {}
[306.592066] (-) TimerEvent: {}
[306.692515] (-) TimerEvent: {}
[306.793487] (-) TimerEvent: {}
[306.894360] (-) TimerEvent: {}
[306.995230] (-) TimerEvent: {}
[307.095798] (-) TimerEvent: {}
[307.106505] (ros1_bridge) StdoutLine: {'line': b'[ 37%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs_factories.cpp.o\x1b[0m\n'}
[307.196130] (-) TimerEvent: {}
[307.296526] (-) TimerEvent: {}
[307.396915] (-) TimerEvent: {}
[307.497325] (-) TimerEvent: {}
[307.598172] (-) TimerEvent: {}
[307.698643] (-) TimerEvent: {}
[307.799018] (-) TimerEvent: {}
[307.899349] (-) TimerEvent: {}
[307.999953] (-) TimerEvent: {}
[308.049103] (ros1_bridge) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__State__factories.cpp.o\x1b[0m\n'}
[308.100268] (-) TimerEvent: {}
[308.201234] (-) TimerEvent: {}
[308.301629] (-) TimerEvent: {}
[308.402701] (-) TimerEvent: {}
[308.503086] (-) TimerEvent: {}
[308.603757] (-) TimerEvent: {}
[308.704224] (-) TimerEvent: {}
[308.804771] (-) TimerEvent: {}
[308.906295] (-) TimerEvent: {}
[309.006818] (-) TimerEvent: {}
[309.107522] (-) TimerEvent: {}
[309.208277] (-) TimerEvent: {}
[309.308711] (-) TimerEvent: {}
[309.409455] (-) TimerEvent: {}
[309.510152] (-) TimerEvent: {}
[309.610608] (-) TimerEvent: {}
[309.711486] (-) TimerEvent: {}
[309.811896] (-) TimerEvent: {}
[309.912956] (-) TimerEvent: {}
[310.013571] (-) TimerEvent: {}
[310.114249] (-) TimerEvent: {}
[310.214662] (-) TimerEvent: {}
[310.315329] (-) TimerEvent: {}
[310.415713] (-) TimerEvent: {}
[310.516212] (-) TimerEvent: {}
[310.616892] (-) TimerEvent: {}
[310.717522] (-) TimerEvent: {}
[310.766816] (ros1_bridge) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__Transition__factories.cpp.o\x1b[0m\n'}
[310.818212] (-) TimerEvent: {}
[310.918604] (-) TimerEvent: {}
[311.019320] (-) TimerEvent: {}
[311.119938] (-) TimerEvent: {}
[311.220917] (-) TimerEvent: {}
[311.322252] (-) TimerEvent: {}
[311.422640] (-) TimerEvent: {}
[311.523117] (-) TimerEvent: {}
[311.623562] (-) TimerEvent: {}
[311.723964] (-) TimerEvent: {}
[311.778606] (ros1_bridge) StdoutLine: {'line': b'[ 38%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__TransitionDescription__factories.cpp.o\x1b[0m\n'}
[311.824293] (-) TimerEvent: {}
[311.924985] (-) TimerEvent: {}
[312.025346] (-) TimerEvent: {}
[312.126153] (-) TimerEvent: {}
[312.226540] (-) TimerEvent: {}
[312.327176] (-) TimerEvent: {}
[312.427509] (-) TimerEvent: {}
[312.527887] (-) TimerEvent: {}
[312.628930] (-) TimerEvent: {}
[312.730160] (-) TimerEvent: {}
[312.830543] (-) TimerEvent: {}
[312.931092] (-) TimerEvent: {}
[313.031528] (-) TimerEvent: {}
[313.132493] (-) TimerEvent: {}
[313.233339] (-) TimerEvent: {}
[313.334181] (-) TimerEvent: {}
[313.435171] (-) TimerEvent: {}
[313.535724] (-) TimerEvent: {}
[313.636261] (-) TimerEvent: {}
[313.736652] (-) TimerEvent: {}
[313.837186] (-) TimerEvent: {}
[313.937624] (-) TimerEvent: {}
[314.038657] (-) TimerEvent: {}
[314.139133] (-) TimerEvent: {}
[314.240569] (-) TimerEvent: {}
[314.292710] (ros1_bridge) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__TransitionEvent__factories.cpp.o\x1b[0m\n'}
[314.341206] (-) TimerEvent: {}
[314.442215] (-) TimerEvent: {}
[314.542645] (-) TimerEvent: {}
[314.643509] (-) TimerEvent: {}
[314.743914] (-) TimerEvent: {}
[314.844300] (-) TimerEvent: {}
[314.945085] (-) TimerEvent: {}
[315.046174] (-) TimerEvent: {}
[315.146643] (-) TimerEvent: {}
[315.247269] (-) TimerEvent: {}
[315.347839] (-) TimerEvent: {}
[315.443901] (ros1_bridge) StdoutLine: {'line': b'[ 39%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__ChangeState__factories.cpp.o\x1b[0m\n'}
[315.447910] (-) TimerEvent: {}
[315.548306] (-) TimerEvent: {}
[315.649062] (-) TimerEvent: {}
[315.749484] (-) TimerEvent: {}
[315.850191] (-) TimerEvent: {}
[315.950839] (-) TimerEvent: {}
[316.051298] (-) TimerEvent: {}
[316.151797] (-) TimerEvent: {}
[316.252264] (-) TimerEvent: {}
[316.353317] (-) TimerEvent: {}
[316.454156] (-) TimerEvent: {}
[316.555086] (-) TimerEvent: {}
[316.655688] (-) TimerEvent: {}
[316.756290] (-) TimerEvent: {}
[316.857264] (-) TimerEvent: {}
[316.958190] (-) TimerEvent: {}
[317.059065] (-) TimerEvent: {}
[317.159623] (-) TimerEvent: {}
[317.260142] (-) TimerEvent: {}
[317.360609] (-) TimerEvent: {}
[317.461149] (-) TimerEvent: {}
[317.562166] (-) TimerEvent: {}
[317.663045] (-) TimerEvent: {}
[317.763484] (-) TimerEvent: {}
[317.864072] (-) TimerEvent: {}
[317.965304] (-) TimerEvent: {}
[318.066221] (-) TimerEvent: {}
[318.077855] (ros1_bridge) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetAvailableStates__factories.cpp.o\x1b[0m\n'}
[318.166322] (-) TimerEvent: {}
[318.267098] (-) TimerEvent: {}
[318.367716] (-) TimerEvent: {}
[318.468196] (-) TimerEvent: {}
[318.569171] (-) TimerEvent: {}
[318.670162] (-) TimerEvent: {}
[318.771026] (-) TimerEvent: {}
[318.871536] (-) TimerEvent: {}
[318.972030] (-) TimerEvent: {}
[319.072553] (-) TimerEvent: {}
[319.173040] (-) TimerEvent: {}
[319.181031] (ros1_bridge) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetAvailableTransitions__factories.cpp.o\x1b[0m\n'}
[319.273142] (-) TimerEvent: {}
[319.374154] (-) TimerEvent: {}
[319.474594] (-) TimerEvent: {}
[319.575295] (-) TimerEvent: {}
[319.675758] (-) TimerEvent: {}
[319.776204] (-) TimerEvent: {}
[319.877091] (-) TimerEvent: {}
[319.978185] (-) TimerEvent: {}
[320.078590] (-) TimerEvent: {}
[320.179221] (-) TimerEvent: {}
[320.279835] (-) TimerEvent: {}
[320.380482] (-) TimerEvent: {}
[320.480954] (-) TimerEvent: {}
[320.589852] (-) TimerEvent: {}
[320.690325] (-) TimerEvent: {}
[320.791265] (-) TimerEvent: {}
[320.891816] (-) TimerEvent: {}
[320.992221] (-) TimerEvent: {}
[321.093208] (-) TimerEvent: {}
[321.194181] (-) TimerEvent: {}
[321.294972] (-) TimerEvent: {}
[321.395428] (-) TimerEvent: {}
[321.495957] (-) TimerEvent: {}
[321.596992] (-) TimerEvent: {}
[321.698203] (-) TimerEvent: {}
[321.799093] (-) TimerEvent: {}
[321.900075] (-) TimerEvent: {}
[321.955310] (ros1_bridge) StdoutLine: {'line': b'[ 40%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetState__factories.cpp.o\x1b[0m\n'}
[322.000200] (-) TimerEvent: {}
[322.100999] (-) TimerEvent: {}
[322.202205] (-) TimerEvent: {}
[322.303136] (-) TimerEvent: {}
[322.403573] (-) TimerEvent: {}
[322.504307] (-) TimerEvent: {}
[322.605489] (-) TimerEvent: {}
[322.706155] (-) TimerEvent: {}
[322.806630] (-) TimerEvent: {}
[322.907098] (-) TimerEvent: {}
[322.937325] (ros1_bridge) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/logging_demo_factories.cpp.o\x1b[0m\n'}
[323.007268] (-) TimerEvent: {}
[323.107873] (-) TimerEvent: {}
[323.208872] (-) TimerEvent: {}
[323.310162] (-) TimerEvent: {}
[323.410953] (-) TimerEvent: {}
[323.511528] (-) TimerEvent: {}
[323.612136] (-) TimerEvent: {}
[323.712783] (-) TimerEvent: {}
[323.813137] (-) TimerEvent: {}
[323.914152] (-) TimerEvent: {}
[324.015185] (-) TimerEvent: {}
[324.115919] (-) TimerEvent: {}
[324.217236] (-) TimerEvent: {}
[324.318202] (-) TimerEvent: {}
[324.418847] (-) TimerEvent: {}
[324.519493] (-) TimerEvent: {}
[324.620186] (-) TimerEvent: {}
[324.720711] (-) TimerEvent: {}
[324.821165] (-) TimerEvent: {}
[324.922189] (-) TimerEvent: {}
[325.023318] (-) TimerEvent: {}
[325.123813] (-) TimerEvent: {}
[325.224251] (-) TimerEvent: {}
[325.324754] (-) TimerEvent: {}
[325.426147] (-) TimerEvent: {}
[325.526938] (-) TimerEvent: {}
[325.627607] (-) TimerEvent: {}
[325.728819] (-) TimerEvent: {}
[325.829272] (-) TimerEvent: {}
[325.929646] (-) TimerEvent: {}
[326.030276] (-) TimerEvent: {}
[326.131045] (-) TimerEvent: {}
[326.232388] (-) TimerEvent: {}
[326.332954] (-) TimerEvent: {}
[326.433233] (-) TimerEvent: {}
[326.499705] (ros1_bridge) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/logging_demo__srv__ConfigLogger__factories.cpp.o\x1b[0m\n'}
[326.533306] (-) TimerEvent: {}
[326.633627] (-) TimerEvent: {}
[326.734174] (-) TimerEvent: {}
[326.834883] (-) TimerEvent: {}
[326.936083] (-) TimerEvent: {}
[327.037572] (-) TimerEvent: {}
[327.138168] (-) TimerEvent: {}
[327.238637] (-) TimerEvent: {}
[327.339361] (-) TimerEvent: {}
[327.439791] (-) TimerEvent: {}
[327.540207] (-) TimerEvent: {}
[327.640568] (-) TimerEvent: {}
[327.741029] (-) TimerEvent: {}
[327.841337] (-) TimerEvent: {}
[327.942214] (-) TimerEvent: {}
[328.042858] (-) TimerEvent: {}
[328.143172] (-) TimerEvent: {}
[328.243663] (-) TimerEvent: {}
[328.343928] (-) TimerEvent: {}
[328.444203] (-) TimerEvent: {}
[328.544475] (-) TimerEvent: {}
[328.645145] (-) TimerEvent: {}
[328.746459] (-) TimerEvent: {}
[328.847277] (-) TimerEvent: {}
[328.947586] (-) TimerEvent: {}
[329.047935] (-) TimerEvent: {}
[329.148303] (-) TimerEvent: {}
[329.248565] (-) TimerEvent: {}
[329.348841] (-) TimerEvent: {}
[329.447467] (ros1_bridge) StdoutLine: {'line': b'[ 41%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs_factories.cpp.o\x1b[0m\n'}
[329.448920] (-) TimerEvent: {}
[329.550151] (-) TimerEvent: {}
[329.650789] (-) TimerEvent: {}
[329.751148] (-) TimerEvent: {}
[329.851712] (-) TimerEvent: {}
[329.952474] (-) TimerEvent: {}
[330.053398] (-) TimerEvent: {}
[330.154267] (-) TimerEvent: {}
[330.255300] (-) TimerEvent: {}
[330.355631] (-) TimerEvent: {}
[330.455920] (-) TimerEvent: {}
[330.556323] (-) TimerEvent: {}
[330.657262] (-) TimerEvent: {}
[330.758165] (-) TimerEvent: {}
[330.858451] (-) TimerEvent: {}
[330.958706] (-) TimerEvent: {}
[331.058983] (-) TimerEvent: {}
[331.159407] (-) TimerEvent: {}
[331.266202] (-) TimerEvent: {}
[331.366832] (-) TimerEvent: {}
[331.464997] (ros1_bridge) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__OccupancyGridUpdate__factories.cpp.o\x1b[0m\n'}
[331.466923] (-) TimerEvent: {}
[331.567279] (-) TimerEvent: {}
[331.667885] (-) TimerEvent: {}
[331.768917] (-) TimerEvent: {}
[331.869345] (-) TimerEvent: {}
[331.970177] (-) TimerEvent: {}
[332.071258] (-) TimerEvent: {}
[332.172679] (-) TimerEvent: {}
[332.274145] (-) TimerEvent: {}
[332.374587] (-) TimerEvent: {}
[332.475019] (-) TimerEvent: {}
[332.575462] (-) TimerEvent: {}
[332.676148] (-) TimerEvent: {}
[332.776922] (-) TimerEvent: {}
[332.866585] (ros1_bridge) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__PointCloud2Update__factories.cpp.o\x1b[0m\n'}
[332.876998] (-) TimerEvent: {}
[332.977363] (-) TimerEvent: {}
[333.078169] (-) TimerEvent: {}
[333.178578] (-) TimerEvent: {}
[333.278961] (-) TimerEvent: {}
[333.379792] (-) TimerEvent: {}
[333.480161] (-) TimerEvent: {}
[333.581038] (-) TimerEvent: {}
[333.681411] (-) TimerEvent: {}
[333.782153] (-) TimerEvent: {}
[333.882523] (-) TimerEvent: {}
[333.983088] (-) TimerEvent: {}
[334.083488] (-) TimerEvent: {}
[334.183922] (-) TimerEvent: {}
[334.284314] (-) TimerEvent: {}
[334.385187] (-) TimerEvent: {}
[334.486155] (-) TimerEvent: {}
[334.586565] (-) TimerEvent: {}
[334.687259] (-) TimerEvent: {}
[334.787741] (-) TimerEvent: {}
[334.888829] (-) TimerEvent: {}
[334.989229] (-) TimerEvent: {}
[335.090366] (-) TimerEvent: {}
[335.190794] (-) TimerEvent: {}
[335.291454] (-) TimerEvent: {}
[335.391868] (-) TimerEvent: {}
[335.492282] (-) TimerEvent: {}
[335.592792] (-) TimerEvent: {}
[335.693192] (-) TimerEvent: {}
[335.794305] (-) TimerEvent: {}
[335.894699] (-) TimerEvent: {}
[335.995119] (-) TimerEvent: {}
[336.095614] (-) TimerEvent: {}
[336.196017] (-) TimerEvent: {}
[336.297209] (-) TimerEvent: {}
[336.398204] (-) TimerEvent: {}
[336.498984] (-) TimerEvent: {}
[336.599402] (-) TimerEvent: {}
[336.699792] (-) TimerEvent: {}
[336.800313] (-) TimerEvent: {}
[336.901201] (-) TimerEvent: {}
[337.002221] (-) TimerEvent: {}
[337.102672] (-) TimerEvent: {}
[337.203236] (-) TimerEvent: {}
[337.303575] (-) TimerEvent: {}
[337.403964] (-) TimerEvent: {}
[337.504379] (-) TimerEvent: {}
[337.604806] (-) TimerEvent: {}
[337.706161] (-) TimerEvent: {}
[337.806581] (-) TimerEvent: {}
[337.906982] (-) TimerEvent: {}
[338.007498] (-) TimerEvent: {}
[338.107904] (-) TimerEvent: {}
[338.208305] (-) TimerEvent: {}
[338.308703] (-) TimerEvent: {}
[338.409618] (-) TimerEvent: {}
[338.510130] (-) TimerEvent: {}
[338.610430] (-) TimerEvent: {}
[338.711179] (-) TimerEvent: {}
[338.811557] (-) TimerEvent: {}
[338.911961] (-) TimerEvent: {}
[339.013000] (-) TimerEvent: {}
[339.113386] (-) TimerEvent: {}
[339.218615] (-) TimerEvent: {}
[339.319876] (-) TimerEvent: {}
[339.420957] (-) TimerEvent: {}
[339.521548] (-) TimerEvent: {}
[339.623167] (-) TimerEvent: {}
[339.724294] (-) TimerEvent: {}
[339.824628] (-) TimerEvent: {}
[339.926270] (-) TimerEvent: {}
[340.026677] (-) TimerEvent: {}
[340.127257] (-) TimerEvent: {}
[340.227681] (-) TimerEvent: {}
[340.328078] (-) TimerEvent: {}
[340.402686] (ros1_bridge) StdoutLine: {'line': b'[ 42%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__ProjectedMapInfo__factories.cpp.o\x1b[0m\n'}
[340.428837] (-) TimerEvent: {}
[340.529197] (-) TimerEvent: {}
[340.630238] (-) TimerEvent: {}
[340.730869] (-) TimerEvent: {}
[340.831363] (-) TimerEvent: {}
[340.931899] (-) TimerEvent: {}
[341.032365] (-) TimerEvent: {}
[341.037792] (ros1_bridge) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__ProjectedMap__factories.cpp.o\x1b[0m\n'}
[341.132540] (-) TimerEvent: {}
[341.233083] (-) TimerEvent: {}
[341.333577] (-) TimerEvent: {}
[341.434315] (-) TimerEvent: {}
[341.534799] (-) TimerEvent: {}
[341.635282] (-) TimerEvent: {}
[341.735785] (-) TimerEvent: {}
[341.836277] (-) TimerEvent: {}
[341.937062] (-) TimerEvent: {}
[342.037609] (-) TimerEvent: {}
[342.138260] (-) TimerEvent: {}
[342.238887] (-) TimerEvent: {}
[342.339397] (-) TimerEvent: {}
[342.439960] (-) TimerEvent: {}
[342.540604] (-) TimerEvent: {}
[342.641049] (-) TimerEvent: {}
[342.741500] (-) TimerEvent: {}
[342.842295] (-) TimerEvent: {}
[342.942755] (-) TimerEvent: {}
[343.043479] (-) TimerEvent: {}
[343.143949] (-) TimerEvent: {}
[343.244944] (-) TimerEvent: {}
[343.346166] (-) TimerEvent: {}
[343.446738] (-) TimerEvent: {}
[343.547370] (-) TimerEvent: {}
[343.647844] (-) TimerEvent: {}
[343.749002] (-) TimerEvent: {}
[343.849677] (-) TimerEvent: {}
[343.950423] (-) TimerEvent: {}
[344.051099] (-) TimerEvent: {}
[344.151572] (-) TimerEvent: {}
[344.252039] (-) TimerEvent: {}
[344.352869] (-) TimerEvent: {}
[344.453560] (-) TimerEvent: {}
[344.554287] (-) TimerEvent: {}
[344.654740] (-) TimerEvent: {}
[344.755208] (-) TimerEvent: {}
[344.855653] (-) TimerEvent: {}
[344.956115] (-) TimerEvent: {}
[345.056792] (-) TimerEvent: {}
[345.157414] (-) TimerEvent: {}
[345.258172] (-) TimerEvent: {}
[345.358900] (-) TimerEvent: {}
[345.460601] (-) TimerEvent: {}
[345.561430] (-) TimerEvent: {}
[345.662197] (-) TimerEvent: {}
[345.762681] (-) TimerEvent: {}
[345.863771] (-) TimerEvent: {}
[345.964827] (-) TimerEvent: {}
[346.065218] (-) TimerEvent: {}
[346.166183] (-) TimerEvent: {}
[346.267081] (-) TimerEvent: {}
[346.367934] (-) TimerEvent: {}
[346.468388] (-) TimerEvent: {}
[346.568727] (-) TimerEvent: {}
[346.669297] (-) TimerEvent: {}
[346.770155] (-) TimerEvent: {}
[346.870544] (-) TimerEvent: {}
[346.970934] (-) TimerEvent: {}
[347.071448] (-) TimerEvent: {}
[347.171956] (-) TimerEvent: {}
[347.272449] (-) TimerEvent: {}
[347.372868] (-) TimerEvent: {}
[347.473572] (-) TimerEvent: {}
[347.574893] (-) TimerEvent: {}
[347.675471] (-) TimerEvent: {}
[347.775902] (-) TimerEvent: {}
[347.876366] (-) TimerEvent: {}
[347.976786] (-) TimerEvent: {}
[348.078313] (-) TimerEvent: {}
[348.190151] (-) TimerEvent: {}
[348.290567] (-) TimerEvent: {}
[348.318095] (ros1_bridge) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetMapROI__factories.cpp.o\x1b[0m\n'}
[348.391095] (-) TimerEvent: {}
[348.491445] (-) TimerEvent: {}
[348.591847] (-) TimerEvent: {}
[348.692813] (-) TimerEvent: {}
[348.793516] (-) TimerEvent: {}
[348.894206] (-) TimerEvent: {}
[348.994596] (-) TimerEvent: {}
[349.088157] (ros1_bridge) StdoutLine: {'line': b'[ 43%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetPointMapROI__factories.cpp.o\x1b[0m\n'}
[349.094671] (-) TimerEvent: {}
[349.195405] (-) TimerEvent: {}
[349.295905] (-) TimerEvent: {}
[349.396910] (-) TimerEvent: {}
[349.498160] (-) TimerEvent: {}
[349.598573] (-) TimerEvent: {}
[349.699006] (-) TimerEvent: {}
[349.799448] (-) TimerEvent: {}
[349.899895] (-) TimerEvent: {}
[350.001017] (-) TimerEvent: {}
[350.102202] (-) TimerEvent: {}
[350.202715] (-) TimerEvent: {}
[350.303294] (-) TimerEvent: {}
[350.403978] (-) TimerEvent: {}
[350.504746] (-) TimerEvent: {}
[350.605357] (-) TimerEvent: {}
[350.706254] (-) TimerEvent: {}
[350.807401] (-) TimerEvent: {}
[350.907876] (-) TimerEvent: {}
[351.008364] (-) TimerEvent: {}
[351.109543] (-) TimerEvent: {}
[351.210560] (-) TimerEvent: {}
[351.311508] (-) TimerEvent: {}
[351.412513] (-) TimerEvent: {}
[351.513623] (-) TimerEvent: {}
[351.614220] (-) TimerEvent: {}
[351.715308] (-) TimerEvent: {}
[351.815779] (-) TimerEvent: {}
[351.916931] (-) TimerEvent: {}
[352.017618] (-) TimerEvent: {}
[352.118152] (-) TimerEvent: {}
[352.218649] (-) TimerEvent: {}
[352.319291] (-) TimerEvent: {}
[352.419777] (-) TimerEvent: {}
[352.520215] (-) TimerEvent: {}
[352.621294] (-) TimerEvent: {}
[352.722322] (-) TimerEvent: {}
[352.822917] (-) TimerEvent: {}
[352.923388] (-) TimerEvent: {}
[353.024309] (-) TimerEvent: {}
[353.124792] (-) TimerEvent: {}
[353.225310] (-) TimerEvent: {}
[353.326410] (-) TimerEvent: {}
[353.427013] (-) TimerEvent: {}
[353.527663] (-) TimerEvent: {}
[353.628328] (-) TimerEvent: {}
[353.729252] (-) TimerEvent: {}
[353.751768] (ros1_bridge) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetPointMap__factories.cpp.o\x1b[0m\n'}
[353.829345] (-) TimerEvent: {}
[353.930303] (-) TimerEvent: {}
[354.030840] (-) TimerEvent: {}
[354.131286] (-) TimerEvent: {}
[354.232220] (-) TimerEvent: {}
[354.333198] (-) TimerEvent: {}
[354.434169] (-) TimerEvent: {}
[354.534783] (-) TimerEvent: {}
[354.635813] (-) TimerEvent: {}
[354.736235] (-) TimerEvent: {}
[354.796391] (ros1_bridge) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__ProjectedMapsInfo__factories.cpp.o\x1b[0m\n'}
[354.836315] (-) TimerEvent: {}
[354.937270] (-) TimerEvent: {}
[355.038387] (-) TimerEvent: {}
[355.138832] (-) TimerEvent: {}
[355.239368] (-) TimerEvent: {}
[355.339839] (-) TimerEvent: {}
[355.440917] (-) TimerEvent: {}
[355.542191] (-) TimerEvent: {}
[355.642808] (-) TimerEvent: {}
[355.743566] (-) TimerEvent: {}
[355.844080] (-) TimerEvent: {}
[355.945087] (-) TimerEvent: {}
[356.046694] (-) TimerEvent: {}
[356.147179] (-) TimerEvent: {}
[356.247603] (-) TimerEvent: {}
[356.348122] (-) TimerEvent: {}
[356.448933] (-) TimerEvent: {}
[356.549497] (-) TimerEvent: {}
[356.650158] (-) TimerEvent: {}
[356.750698] (-) TimerEvent: {}
[356.851149] (-) TimerEvent: {}
[356.951857] (-) TimerEvent: {}
[357.052242] (-) TimerEvent: {}
[357.153080] (-) TimerEvent: {}
[357.254183] (-) TimerEvent: {}
[357.354594] (-) TimerEvent: {}
[357.455149] (-) TimerEvent: {}
[357.555667] (-) TimerEvent: {}
[357.656149] (-) TimerEvent: {}
[357.758637] (-) TimerEvent: {}
[357.859036] (-) TimerEvent: {}
[357.959833] (-) TimerEvent: {}
[358.060293] (-) TimerEvent: {}
[358.161107] (-) TimerEvent: {}
[358.262183] (-) TimerEvent: {}
[358.362573] (-) TimerEvent: {}
[358.462957] (-) TimerEvent: {}
[358.563361] (-) TimerEvent: {}
[358.664100] (-) TimerEvent: {}
[358.764483] (-) TimerEvent: {}
[358.865565] (-) TimerEvent: {}
[358.966433] (-) TimerEvent: {}
[359.067233] (-) TimerEvent: {}
[359.167709] (-) TimerEvent: {}
[359.268247] (-) TimerEvent: {}
[359.368772] (-) TimerEvent: {}
[359.414948] (ros1_bridge) StdoutLine: {'line': b'[ 44%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__SaveMap__factories.cpp.o\x1b[0m\n'}
[359.469485] (-) TimerEvent: {}
[359.570211] (-) TimerEvent: {}
[359.671186] (-) TimerEvent: {}
[359.771593] (-) TimerEvent: {}
[359.871980] (-) TimerEvent: {}
[359.972374] (-) TimerEvent: {}
[360.073079] (-) TimerEvent: {}
[360.174173] (-) TimerEvent: {}
[360.274612] (-) TimerEvent: {}
[360.375044] (-) TimerEvent: {}
[360.435751] (ros1_bridge) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__SetMapProjections__factories.cpp.o\x1b[0m\n'}
[360.475543] (-) TimerEvent: {}
[360.576148] (-) TimerEvent: {}
[360.677064] (-) TimerEvent: {}
[360.778162] (-) TimerEvent: {}
[360.879105] (-) TimerEvent: {}
[360.979535] (-) TimerEvent: {}
[361.080078] (-) TimerEvent: {}
[361.181286] (-) TimerEvent: {}
[361.282212] (-) TimerEvent: {}
[361.382768] (-) TimerEvent: {}
[361.483270] (-) TimerEvent: {}
[361.583726] (-) TimerEvent: {}
[361.684297] (-) TimerEvent: {}
[361.785075] (-) TimerEvent: {}
[361.886182] (-) TimerEvent: {}
[361.987023] (-) TimerEvent: {}
[362.087547] (-) TimerEvent: {}
[362.188083] (-) TimerEvent: {}
[362.288994] (-) TimerEvent: {}
[362.390157] (-) TimerEvent: {}
[362.490561] (-) TimerEvent: {}
[362.591181] (-) TimerEvent: {}
[362.691626] (-) TimerEvent: {}
[362.792142] (-) TimerEvent: {}
[362.892591] (-) TimerEvent: {}
[362.993102] (-) TimerEvent: {}
[363.093643] (-) TimerEvent: {}
[363.194372] (-) TimerEvent: {}
[363.294881] (-) TimerEvent: {}
[363.395401] (-) TimerEvent: {}
[363.495772] (-) TimerEvent: {}
[363.596203] (-) TimerEvent: {}
[363.697006] (-) TimerEvent: {}
[363.798165] (-) TimerEvent: {}
[363.898581] (-) TimerEvent: {}
[363.999369] (-) TimerEvent: {}
[364.099966] (-) TimerEvent: {}
[364.201040] (-) TimerEvent: {}
[364.301535] (-) TimerEvent: {}
[364.402516] (-) TimerEvent: {}
[364.503393] (-) TimerEvent: {}
[364.603953] (-) TimerEvent: {}
[364.704353] (-) TimerEvent: {}
[364.704684] (ros1_bridge) StdoutLine: {'line': b'[ 45%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs_factories.cpp.o\x1b[0m\n'}
[364.804561] (-) TimerEvent: {}
[364.904990] (-) TimerEvent: {}
[365.005389] (-) TimerEvent: {}
[365.106345] (-) TimerEvent: {}
[365.207229] (-) TimerEvent: {}
[365.307577] (-) TimerEvent: {}
[365.408011] (-) TimerEvent: {}
[365.508397] (-) TimerEvent: {}
[365.609154] (-) TimerEvent: {}
[365.710172] (-) TimerEvent: {}
[365.810567] (-) TimerEvent: {}
[365.910979] (-) TimerEvent: {}
[366.012013] (-) TimerEvent: {}
[366.112513] (-) TimerEvent: {}
[366.213024] (-) TimerEvent: {}
[366.299595] (ros1_bridge) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__GridCells__factories.cpp.o\x1b[0m\n'}
[366.313087] (-) TimerEvent: {}
[366.414172] (-) TimerEvent: {}
[366.514582] (-) TimerEvent: {}
[366.615037] (-) TimerEvent: {}
[366.716005] (-) TimerEvent: {}
[366.816630] (-) TimerEvent: {}
[366.917064] (-) TimerEvent: {}
[367.018177] (-) TimerEvent: {}
[367.118961] (-) TimerEvent: {}
[367.219639] (-) TimerEvent: {}
[367.320126] (-) TimerEvent: {}
[367.420892] (-) TimerEvent: {}
[367.521474] (-) TimerEvent: {}
[367.622168] (-) TimerEvent: {}
[367.723120] (-) TimerEvent: {}
[367.824160] (-) TimerEvent: {}
[367.924648] (-) TimerEvent: {}
[368.026179] (-) TimerEvent: {}
[368.127007] (-) TimerEvent: {}
[368.228098] (-) TimerEvent: {}
[368.329308] (-) TimerEvent: {}
[368.430145] (-) TimerEvent: {}
[368.530572] (-) TimerEvent: {}
[368.630956] (-) TimerEvent: {}
[368.637214] (ros1_bridge) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__MapMetaData__factories.cpp.o\x1b[0m\n'}
[368.731077] (-) TimerEvent: {}
[368.831470] (-) TimerEvent: {}
[368.931850] (-) TimerEvent: {}
[369.032187] (-) TimerEvent: {}
[369.132994] (-) TimerEvent: {}
[369.234204] (-) TimerEvent: {}
[369.334799] (-) TimerEvent: {}
[369.435260] (-) TimerEvent: {}
[369.535723] (-) TimerEvent: {}
[369.636223] (-) TimerEvent: {}
[369.736835] (-) TimerEvent: {}
[369.837310] (-) TimerEvent: {}
[369.938325] (-) TimerEvent: {}
[370.038851] (-) TimerEvent: {}
[370.139379] (-) TimerEvent: {}
[370.239876] (-) TimerEvent: {}
[370.340270] (-) TimerEvent: {}
[370.440883] (-) TimerEvent: {}
[370.541304] (-) TimerEvent: {}
[370.642257] (-) TimerEvent: {}
[370.742711] (-) TimerEvent: {}
[370.843190] (-) TimerEvent: {}
[370.943805] (-) TimerEvent: {}
[371.044309] (-) TimerEvent: {}
[371.144997] (-) TimerEvent: {}
[371.245380] (-) TimerEvent: {}
[371.346140] (-) TimerEvent: {}
[371.446475] (-) TimerEvent: {}
[371.546889] (-) TimerEvent: {}
[371.647428] (-) TimerEvent: {}
[371.747831] (-) TimerEvent: {}
[371.848861] (-) TimerEvent: {}
[371.949265] (-) TimerEvent: {}
[372.050138] (-) TimerEvent: {}
[372.150461] (-) TimerEvent: {}
[372.251198] (-) TimerEvent: {}
[372.351663] (-) TimerEvent: {}
[372.452062] (-) TimerEvent: {}
[372.552519] (-) TimerEvent: {}
[372.652993] (-) TimerEvent: {}
[372.753377] (-) TimerEvent: {}
[372.854163] (-) TimerEvent: {}
[372.954558] (-) TimerEvent: {}
[373.055317] (-) TimerEvent: {}
[373.155982] (-) TimerEvent: {}
[373.256844] (-) TimerEvent: {}
[373.357259] (-) TimerEvent: {}
[373.458164] (-) TimerEvent: {}
[373.558590] (-) TimerEvent: {}
[373.659169] (-) TimerEvent: {}
[373.759615] (-) TimerEvent: {}
[373.860062] (-) TimerEvent: {}
[373.960795] (-) TimerEvent: {}
[374.062156] (-) TimerEvent: {}
[374.162576] (-) TimerEvent: {}
[374.263042] (-) TimerEvent: {}
[374.370845] (-) TimerEvent: {}
[374.471225] (-) TimerEvent: {}
[374.571671] (-) TimerEvent: {}
[374.672170] (-) TimerEvent: {}
[374.772533] (-) TimerEvent: {}
[374.808838] (ros1_bridge) StdoutLine: {'line': b'[ 46%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__OccupancyGrid__factories.cpp.o\x1b[0m\n'}
[374.872774] (-) TimerEvent: {}
[374.973698] (-) TimerEvent: {}
[375.074582] (-) TimerEvent: {}
[375.175500] (-) TimerEvent: {}
[375.276054] (-) TimerEvent: {}
[375.376794] (-) TimerEvent: {}
[375.478189] (-) TimerEvent: {}
[375.578677] (-) TimerEvent: {}
[375.679174] (-) TimerEvent: {}
[375.779858] (-) TimerEvent: {}
[375.880352] (-) TimerEvent: {}
[375.981144] (-) TimerEvent: {}
[376.082154] (-) TimerEvent: {}
[376.182608] (-) TimerEvent: {}
[376.283099] (-) TimerEvent: {}
[376.383752] (-) TimerEvent: {}
[376.484232] (-) TimerEvent: {}
[376.584655] (-) TimerEvent: {}
[376.685355] (-) TimerEvent: {}
[376.786446] (-) TimerEvent: {}
[376.886989] (-) TimerEvent: {}
[376.898848] (ros1_bridge) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__Odometry__factories.cpp.o\x1b[0m\n'}
[376.987237] (-) TimerEvent: {}
[377.088037] (-) TimerEvent: {}
[377.188939] (-) TimerEvent: {}
[377.290200] (-) TimerEvent: {}
[377.391079] (-) TimerEvent: {}
[377.491681] (-) TimerEvent: {}
[377.592206] (-) TimerEvent: {}
[377.693344] (-) TimerEvent: {}
[377.794188] (-) TimerEvent: {}
[377.894654] (-) TimerEvent: {}
[377.995149] (-) TimerEvent: {}
[378.095707] (-) TimerEvent: {}
[378.196209] (-) TimerEvent: {}
[378.297311] (-) TimerEvent: {}
[378.398174] (-) TimerEvent: {}
[378.499129] (-) TimerEvent: {}
[378.600192] (-) TimerEvent: {}
[378.701123] (-) TimerEvent: {}
[378.801590] (-) TimerEvent: {}
[378.902151] (-) TimerEvent: {}
[379.002721] (-) TimerEvent: {}
[379.103277] (-) TimerEvent: {}
[379.203687] (-) TimerEvent: {}
[379.304183] (-) TimerEvent: {}
[379.404808] (-) TimerEvent: {}
[379.505295] (-) TimerEvent: {}
[379.606170] (-) TimerEvent: {}
[379.706645] (-) TimerEvent: {}
[379.807255] (-) TimerEvent: {}
[379.907751] (-) TimerEvent: {}
[380.008170] (-) TimerEvent: {}
[380.108603] (-) TimerEvent: {}
[380.209063] (-) TimerEvent: {}
[380.309495] (-) TimerEvent: {}
[380.410220] (-) TimerEvent: {}
[380.510591] (-) TimerEvent: {}
[380.610970] (-) TimerEvent: {}
[380.711439] (-) TimerEvent: {}
[380.811925] (-) TimerEvent: {}
[380.912345] (-) TimerEvent: {}
[381.013087] (-) TimerEvent: {}
[381.113589] (-) TimerEvent: {}
[381.214536] (-) TimerEvent: {}
[381.315094] (-) TimerEvent: {}
[381.415676] (-) TimerEvent: {}
[381.516143] (-) TimerEvent: {}
[381.616863] (-) TimerEvent: {}
[381.718174] (-) TimerEvent: {}
[381.818853] (-) TimerEvent: {}
[381.919300] (-) TimerEvent: {}
[382.019788] (-) TimerEvent: {}
[382.120180] (-) TimerEvent: {}
[382.220846] (-) TimerEvent: {}
[382.322148] (-) TimerEvent: {}
[382.422594] (-) TimerEvent: {}
[382.523064] (-) TimerEvent: {}
[382.623511] (-) TimerEvent: {}
[382.724190] (-) TimerEvent: {}
[382.824784] (-) TimerEvent: {}
[382.925251] (-) TimerEvent: {}
[383.026145] (-) TimerEvent: {}
[383.126986] (-) TimerEvent: {}
[383.164352] (ros1_bridge) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__Path__factories.cpp.o\x1b[0m\n'}
[383.227102] (-) TimerEvent: {}
[383.327581] (-) TimerEvent: {}
[383.428157] (-) TimerEvent: {}
[383.528911] (-) TimerEvent: {}
[383.629391] (-) TimerEvent: {}
[383.730164] (-) TimerEvent: {}
[383.830947] (-) TimerEvent: {}
[383.931664] (-) TimerEvent: {}
[384.032330] (-) TimerEvent: {}
[384.133175] (-) TimerEvent: {}
[384.234452] (-) TimerEvent: {}
[384.335958] (-) TimerEvent: {}
[384.437026] (-) TimerEvent: {}
[384.538227] (-) TimerEvent: {}
[384.638992] (-) TimerEvent: {}
[384.739914] (-) TimerEvent: {}
[384.840920] (-) TimerEvent: {}
[384.942230] (-) TimerEvent: {}
[385.043703] (-) TimerEvent: {}
[385.144955] (-) TimerEvent: {}
[385.246374] (-) TimerEvent: {}
[385.347070] (-) TimerEvent: {}
[385.447711] (-) TimerEvent: {}
[385.548587] (-) TimerEvent: {}
[385.649536] (-) TimerEvent: {}
[385.750573] (-) TimerEvent: {}
[385.851379] (-) TimerEvent: {}
[385.952659] (-) TimerEvent: {}
[386.053383] (-) TimerEvent: {}
[386.154721] (-) TimerEvent: {}
[386.255680] (-) TimerEvent: {}
[386.356475] (-) TimerEvent: {}
[386.457657] (-) TimerEvent: {}
[386.558743] (-) TimerEvent: {}
[386.659526] (-) TimerEvent: {}
[386.760864] (-) TimerEvent: {}
[386.861572] (-) TimerEvent: {}
[386.962548] (-) TimerEvent: {}
[387.063200] (-) TimerEvent: {}
[387.163911] (-) TimerEvent: {}
[387.264625] (-) TimerEvent: {}
[387.365581] (-) TimerEvent: {}
[387.466602] (-) TimerEvent: {}
[387.567266] (-) TimerEvent: {}
[387.667960] (-) TimerEvent: {}
[387.768582] (-) TimerEvent: {}
[387.869185] (-) TimerEvent: {}
[387.970297] (-) TimerEvent: {}
[388.070829] (-) TimerEvent: {}
[388.171270] (-) TimerEvent: {}
[388.271753] (-) TimerEvent: {}
[388.372486] (-) TimerEvent: {}
[388.472997] (-) TimerEvent: {}
[388.573629] (-) TimerEvent: {}
[388.674197] (-) TimerEvent: {}
[388.774648] (-) TimerEvent: {}
[388.875446] (-) TimerEvent: {}
[388.975981] (-) TimerEvent: {}
[389.076454] (-) TimerEvent: {}
[389.177549] (-) TimerEvent: {}
[389.278300] (-) TimerEvent: {}
[389.378878] (-) TimerEvent: {}
[389.479444] (-) TimerEvent: {}
[389.579910] (-) TimerEvent: {}
[389.680408] (-) TimerEvent: {}
[389.780982] (-) TimerEvent: {}
[389.881477] (-) TimerEvent: {}
[389.982452] (-) TimerEvent: {}
[390.082853] (-) TimerEvent: {}
[390.183556] (-) TimerEvent: {}
[390.284326] (-) TimerEvent: {}
[390.384919] (-) TimerEvent: {}
[390.485493] (-) TimerEvent: {}
[390.586151] (-) TimerEvent: {}
[390.647724] (ros1_bridge) StdoutLine: {'line': b'[ 47%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__GetMap__factories.cpp.o\x1b[0m\n'}
[390.686331] (-) TimerEvent: {}
[390.786844] (-) TimerEvent: {}
[390.887603] (-) TimerEvent: {}
[390.988087] (-) TimerEvent: {}
[391.089349] (-) TimerEvent: {}
[391.190451] (-) TimerEvent: {}
[391.291083] (-) TimerEvent: {}
[391.391700] (-) TimerEvent: {}
[391.492205] (-) TimerEvent: {}
[391.594689] (-) TimerEvent: {}
[391.695928] (-) TimerEvent: {}
[391.797363] (-) TimerEvent: {}
[391.898636] (-) TimerEvent: {}
[392.000509] (-) TimerEvent: {}
[392.101098] (-) TimerEvent: {}
[392.201663] (-) TimerEvent: {}
[392.302445] (-) TimerEvent: {}
[392.403501] (-) TimerEvent: {}
[392.504466] (-) TimerEvent: {}
[392.605392] (-) TimerEvent: {}
[392.706369] (-) TimerEvent: {}
[392.806872] (-) TimerEvent: {}
[392.907544] (-) TimerEvent: {}
[393.008112] (-) TimerEvent: {}
[393.108880] (-) TimerEvent: {}
[393.209516] (-) TimerEvent: {}
[393.310596] (-) TimerEvent: {}
[393.411119] (-) TimerEvent: {}
[393.511749] (-) TimerEvent: {}
[393.612408] (-) TimerEvent: {}
[393.712981] (-) TimerEvent: {}
[393.813570] (-) TimerEvent: {}
[393.914433] (-) TimerEvent: {}
[394.015364] (-) TimerEvent: {}
[394.115915] (-) TimerEvent: {}
[394.216570] (-) TimerEvent: {}
[394.317064] (-) TimerEvent: {}
[394.417602] (-) TimerEvent: {}
[394.518261] (-) TimerEvent: {}
[394.618777] (-) TimerEvent: {}
[394.719193] (-) TimerEvent: {}
[394.820324] (-) TimerEvent: {}
[394.921303] (-) TimerEvent: {}
[395.022224] (-) TimerEvent: {}
[395.122692] (-) TimerEvent: {}
[395.223293] (-) TimerEvent: {}
[395.323932] (-) TimerEvent: {}
[395.424519] (-) TimerEvent: {}
[395.525099] (-) TimerEvent: {}
[395.625654] (-) TimerEvent: {}
[395.726441] (-) TimerEvent: {}
[395.827379] (-) TimerEvent: {}
[395.927967] (-) TimerEvent: {}
[396.028624] (-) TimerEvent: {}
[396.129263] (-) TimerEvent: {}
[396.230322] (-) TimerEvent: {}
[396.330885] (-) TimerEvent: {}
[396.431440] (-) TimerEvent: {}
[396.531982] (-) TimerEvent: {}
[396.632609] (-) TimerEvent: {}
[396.733581] (-) TimerEvent: {}
[396.834255] (-) TimerEvent: {}
[396.935316] (-) TimerEvent: {}
[397.035933] (-) TimerEvent: {}
[397.136561] (-) TimerEvent: {}
[397.237163] (-) TimerEvent: {}
[397.338778] (-) TimerEvent: {}
[397.439236] (-) TimerEvent: {}
[397.539794] (-) TimerEvent: {}
[397.640411] (-) TimerEvent: {}
[397.741279] (-) TimerEvent: {}
[397.842258] (-) TimerEvent: {}
[397.943308] (-) TimerEvent: {}
[398.043949] (-) TimerEvent: {}
[398.144651] (-) TimerEvent: {}
[398.246349] (-) TimerEvent: {}
[398.347184] (-) TimerEvent: {}
[398.448251] (-) TimerEvent: {}
[398.548922] (-) TimerEvent: {}
[398.650245] (-) TimerEvent: {}
[398.750909] (-) TimerEvent: {}
[398.851563] (-) TimerEvent: {}
[398.952303] (-) TimerEvent: {}
[399.052926] (-) TimerEvent: {}
[399.131593] (ros1_bridge) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__GetPlan__factories.cpp.o\x1b[0m\n'}
[399.153228] (-) TimerEvent: {}
[399.254191] (-) TimerEvent: {}
[399.355034] (-) TimerEvent: {}
[399.455794] (-) TimerEvent: {}
[399.556458] (-) TimerEvent: {}
[399.657032] (-) TimerEvent: {}
[399.757548] (-) TimerEvent: {}
[399.858577] (-) TimerEvent: {}
[399.959201] (-) TimerEvent: {}
[400.060165] (-) TimerEvent: {}
[400.160666] (-) TimerEvent: {}
[400.261334] (-) TimerEvent: {}
[400.362243] (-) TimerEvent: {}
[400.462825] (-) TimerEvent: {}
[400.563449] (-) TimerEvent: {}
[400.664118] (-) TimerEvent: {}
[400.714477] (ros1_bridge) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__SetMap__factories.cpp.o\x1b[0m\n'}
[400.764266] (-) TimerEvent: {}
[400.865220] (-) TimerEvent: {}
[400.966198] (-) TimerEvent: {}
[401.067202] (-) TimerEvent: {}
[401.167739] (-) TimerEvent: {}
[401.268257] (-) TimerEvent: {}
[401.369194] (-) TimerEvent: {}
[401.470237] (-) TimerEvent: {}
[401.571234] (-) TimerEvent: {}
[401.673231] (-) TimerEvent: {}
[401.774293] (-) TimerEvent: {}
[401.875231] (-) TimerEvent: {}
[401.975911] (-) TimerEvent: {}
[402.076551] (-) TimerEvent: {}
[402.177165] (-) TimerEvent: {}
[402.278315] (-) TimerEvent: {}
[402.379192] (-) TimerEvent: {}
[402.480290] (-) TimerEvent: {}
[402.581221] (-) TimerEvent: {}
[402.682275] (-) TimerEvent: {}
[402.783163] (-) TimerEvent: {}
[402.884126] (-) TimerEvent: {}
[402.985133] (-) TimerEvent: {}
[403.086200] (-) TimerEvent: {}
[403.186884] (-) TimerEvent: {}
[403.287417] (-) TimerEvent: {}
[403.388070] (-) TimerEvent: {}
[403.488642] (-) TimerEvent: {}
[403.589212] (-) TimerEvent: {}
[403.690258] (-) TimerEvent: {}
[403.790993] (-) TimerEvent: {}
[403.891602] (-) TimerEvent: {}
[403.992376] (-) TimerEvent: {}
[404.093153] (-) TimerEvent: {}
[404.193683] (-) TimerEvent: {}
[404.294474] (-) TimerEvent: {}
[404.395170] (-) TimerEvent: {}
[404.496370] (-) TimerEvent: {}
[404.597045] (-) TimerEvent: {}
[404.697663] (-) TimerEvent: {}
[404.798429] (-) TimerEvent: {}
[404.899123] (-) TimerEvent: {}
[405.000218] (-) TimerEvent: {}
[405.101294] (-) TimerEvent: {}
[405.202236] (-) TimerEvent: {}
[405.303120] (-) TimerEvent: {}
[405.403651] (-) TimerEvent: {}
[405.504500] (-) TimerEvent: {}
[405.605397] (-) TimerEvent: {}
[405.706239] (-) TimerEvent: {}
[405.807007] (-) TimerEvent: {}
[405.907561] (-) TimerEvent: {}
[406.008156] (-) TimerEvent: {}
[406.109118] (-) TimerEvent: {}
[406.209605] (-) TimerEvent: {}
[406.310192] (-) TimerEvent: {}
[406.410994] (-) TimerEvent: {}
[406.512026] (-) TimerEvent: {}
[406.612584] (-) TimerEvent: {}
[406.714272] (-) TimerEvent: {}
[406.814870] (-) TimerEvent: {}
[406.915387] (-) TimerEvent: {}
[407.016043] (-) TimerEvent: {}
[407.117069] (-) TimerEvent: {}
[407.217673] (-) TimerEvent: {}
[407.318668] (-) TimerEvent: {}
[407.419335] (-) TimerEvent: {}
[407.520022] (-) TimerEvent: {}
[407.621010] (-) TimerEvent: {}
[407.722219] (-) TimerEvent: {}
[407.822980] (-) TimerEvent: {}
[407.923593] (-) TimerEvent: {}
[408.024265] (-) TimerEvent: {}
[408.124998] (-) TimerEvent: {}
[408.226223] (-) TimerEvent: {}
[408.326885] (-) TimerEvent: {}
[408.427544] (-) TimerEvent: {}
[408.528237] (-) TimerEvent: {}
[408.628943] (-) TimerEvent: {}
[408.729503] (-) TimerEvent: {}
[408.830203] (-) TimerEvent: {}
[408.930722] (-) TimerEvent: {}
[409.031103] (-) TimerEvent: {}
[409.131580] (-) TimerEvent: {}
[409.186545] (ros1_bridge) StdoutLine: {'line': b'[ 48%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs_factories.cpp.o\x1b[0m\n'}
[409.231677] (-) TimerEvent: {}
[409.332199] (-) TimerEvent: {}
[409.433055] (-) TimerEvent: {}
[409.534230] (-) TimerEvent: {}
[409.634792] (-) TimerEvent: {}
[409.735245] (-) TimerEvent: {}
[409.835792] (-) TimerEvent: {}
[409.936533] (-) TimerEvent: {}
[410.037117] (-) TimerEvent: {}
[410.137684] (-) TimerEvent: {}
[410.238733] (-) TimerEvent: {}
[410.339292] (-) TimerEvent: {}
[410.439824] (-) TimerEvent: {}
[410.540923] (-) TimerEvent: {}
[410.641650] (-) TimerEvent: {}
[410.719961] (ros1_bridge) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__ModelCoefficients__factories.cpp.o\x1b[0m\n'}
[410.742255] (-) TimerEvent: {}
[410.842861] (-) TimerEvent: {}
[410.943479] (-) TimerEvent: {}
[411.044030] (-) TimerEvent: {}
[411.145041] (-) TimerEvent: {}
[411.245606] (-) TimerEvent: {}
[411.346559] (-) TimerEvent: {}
[411.447329] (-) TimerEvent: {}
[411.547974] (-) TimerEvent: {}
[411.648535] (-) TimerEvent: {}
[411.750186] (-) TimerEvent: {}
[411.850730] (-) TimerEvent: {}
[411.951306] (-) TimerEvent: {}
[412.051883] (-) TimerEvent: {}
[412.152324] (-) TimerEvent: {}
[412.253053] (-) TimerEvent: {}
[412.354300] (-) TimerEvent: {}
[412.454747] (-) TimerEvent: {}
[412.555934] (-) TimerEvent: {}
[412.656880] (-) TimerEvent: {}
[412.757515] (-) TimerEvent: {}
[412.858424] (-) TimerEvent: {}
[412.959052] (-) TimerEvent: {}
[413.059982] (-) TimerEvent: {}
[413.161057] (-) TimerEvent: {}
[413.262208] (-) TimerEvent: {}
[413.362899] (-) TimerEvent: {}
[413.463915] (-) TimerEvent: {}
[413.564459] (-) TimerEvent: {}
[413.665152] (-) TimerEvent: {}
[413.766285] (-) TimerEvent: {}
[413.866855] (-) TimerEvent: {}
[413.968027] (-) TimerEvent: {}
[414.068580] (-) TimerEvent: {}
[414.169157] (-) TimerEvent: {}
[414.270335] (-) TimerEvent: {}
[414.371005] (-) TimerEvent: {}
[414.471836] (-) TimerEvent: {}
[414.572381] (-) TimerEvent: {}
[414.672997] (-) TimerEvent: {}
[414.773637] (-) TimerEvent: {}
[414.874633] (-) TimerEvent: {}
[414.975865] (-) TimerEvent: {}
[415.076549] (-) TimerEvent: {}
[415.177167] (-) TimerEvent: {}
[415.277645] (-) TimerEvent: {}
[415.378342] (-) TimerEvent: {}
[415.478953] (-) TimerEvent: {}
[415.579526] (-) TimerEvent: {}
[415.680249] (-) TimerEvent: {}
[415.780947] (-) TimerEvent: {}
[415.881628] (-) TimerEvent: {}
[415.982646] (-) TimerEvent: {}
[416.083280] (-) TimerEvent: {}
[416.109629] (ros1_bridge) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__PointIndices__factories.cpp.o\x1b[0m\n'}
[416.183433] (-) TimerEvent: {}
[416.283980] (-) TimerEvent: {}
[416.384468] (-) TimerEvent: {}
[416.484962] (-) TimerEvent: {}
[416.586307] (-) TimerEvent: {}
[416.686821] (-) TimerEvent: {}
[416.787950] (-) TimerEvent: {}
[416.888833] (-) TimerEvent: {}
[416.990269] (-) TimerEvent: {}
[417.090845] (-) TimerEvent: {}
[417.194359] (-) TimerEvent: {}
[417.298347] (-) TimerEvent: {}
[417.398850] (-) TimerEvent: {}
[417.499865] (-) TimerEvent: {}
[417.600750] (-) TimerEvent: {}
[417.701232] (-) TimerEvent: {}
[417.802243] (-) TimerEvent: {}
[417.902759] (-) TimerEvent: {}
[418.003782] (-) TimerEvent: {}
[418.104316] (-) TimerEvent: {}
[418.204859] (-) TimerEvent: {}
[418.305353] (-) TimerEvent: {}
[418.406197] (-) TimerEvent: {}
[418.506981] (-) TimerEvent: {}
[418.607513] (-) TimerEvent: {}
[418.708026] (-) TimerEvent: {}
[418.808362] (-) TimerEvent: {}
[418.908952] (-) TimerEvent: {}
[419.009585] (-) TimerEvent: {}
[419.110244] (-) TimerEvent: {}
[419.210903] (-) TimerEvent: {}
[419.311462] (-) TimerEvent: {}
[419.412094] (-) TimerEvent: {}
[419.512907] (-) TimerEvent: {}
[419.613622] (-) TimerEvent: {}
[419.715014] (-) TimerEvent: {}
[419.815693] (-) TimerEvent: {}
[419.916734] (-) TimerEvent: {}
[420.018220] (-) TimerEvent: {}
[420.118948] (-) TimerEvent: {}
[420.219575] (-) TimerEvent: {}
[420.320098] (-) TimerEvent: {}
[420.420534] (-) TimerEvent: {}
[420.521067] (-) TimerEvent: {}
[420.622196] (-) TimerEvent: {}
[420.722825] (-) TimerEvent: {}
[420.823371] (-) TimerEvent: {}
[420.923917] (-) TimerEvent: {}
[421.024625] (-) TimerEvent: {}
[421.125305] (-) TimerEvent: {}
[421.226339] (-) TimerEvent: {}
[421.326825] (-) TimerEvent: {}
[421.427752] (-) TimerEvent: {}
[421.528653] (-) TimerEvent: {}
[421.629272] (-) TimerEvent: {}
[421.730176] (-) TimerEvent: {}
[421.830788] (-) TimerEvent: {}
[421.931303] (-) TimerEvent: {}
[422.031747] (-) TimerEvent: {}
[422.132316] (-) TimerEvent: {}
[422.232961] (-) TimerEvent: {}
[422.334169] (-) TimerEvent: {}
[422.434981] (-) TimerEvent: {}
[422.535586] (-) TimerEvent: {}
[422.636622] (-) TimerEvent: {}
[422.737103] (-) TimerEvent: {}
[422.838204] (-) TimerEvent: {}
[422.938851] (-) TimerEvent: {}
[423.039612] (-) TimerEvent: {}
[423.140647] (-) TimerEvent: {}
[423.241577] (-) TimerEvent: {}
[423.342768] (-) TimerEvent: {}
[423.443233] (-) TimerEvent: {}
[423.543813] (-) TimerEvent: {}
[423.644293] (-) TimerEvent: {}
[423.744751] (-) TimerEvent: {}
[423.761049] (ros1_bridge) StdoutLine: {'line': b'[ 49%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__PolygonMesh__factories.cpp.o\x1b[0m\n'}
[423.844875] (-) TimerEvent: {}
[423.945436] (-) TimerEvent: {}
[424.046220] (-) TimerEvent: {}
[424.146774] (-) TimerEvent: {}
[424.247941] (-) TimerEvent: {}
[424.348525] (-) TimerEvent: {}
[424.449186] (-) TimerEvent: {}
[424.550176] (-) TimerEvent: {}
[424.650643] (-) TimerEvent: {}
[424.751100] (-) TimerEvent: {}
[424.851592] (-) TimerEvent: {}
[424.952081] (-) TimerEvent: {}
[425.052718] (-) TimerEvent: {}
[425.153678] (-) TimerEvent: {}
[425.254737] (-) TimerEvent: {}
[425.355280] (-) TimerEvent: {}
[425.455885] (-) TimerEvent: {}
[425.556364] (-) TimerEvent: {}
[425.656864] (-) TimerEvent: {}
[425.757338] (-) TimerEvent: {}
[425.858177] (-) TimerEvent: {}
[425.958802] (-) TimerEvent: {}
[426.059615] (-) TimerEvent: {}
[426.160287] (-) TimerEvent: {}
[426.260697] (-) TimerEvent: {}
[426.361214] (-) TimerEvent: {}
[426.462179] (-) TimerEvent: {}
[426.562583] (-) TimerEvent: {}
[426.663037] (-) TimerEvent: {}
[426.763612] (-) TimerEvent: {}
[426.864599] (-) TimerEvent: {}
[426.965059] (-) TimerEvent: {}
[427.065665] (-) TimerEvent: {}
[427.166211] (-) TimerEvent: {}
[427.266724] (-) TimerEvent: {}
[427.367100] (-) TimerEvent: {}
[427.468798] (-) TimerEvent: {}
[427.569278] (-) TimerEvent: {}
[427.670221] (-) TimerEvent: {}
[427.770750] (-) TimerEvent: {}
[427.871296] (-) TimerEvent: {}
[427.972659] (-) TimerEvent: {}
[428.073093] (-) TimerEvent: {}
[428.163001] (ros1_bridge) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__Vertices__factories.cpp.o\x1b[0m\n'}
[428.173190] (-) TimerEvent: {}
[428.273618] (-) TimerEvent: {}
[428.374364] (-) TimerEvent: {}
[428.475153] (-) TimerEvent: {}
[428.575673] (-) TimerEvent: {}
[428.676242] (-) TimerEvent: {}
[428.776763] (-) TimerEvent: {}
[428.878310] (-) TimerEvent: {}
[428.978866] (-) TimerEvent: {}
[429.079231] (-) TimerEvent: {}
[429.179792] (-) TimerEvent: {}
[429.280661] (-) TimerEvent: {}
[429.381382] (-) TimerEvent: {}
[429.482402] (-) TimerEvent: {}
[429.582954] (-) TimerEvent: {}
[429.683615] (-) TimerEvent: {}
[429.784095] (-) TimerEvent: {}
[429.884580] (-) TimerEvent: {}
[429.985118] (-) TimerEvent: {}
[430.085603] (-) TimerEvent: {}
[430.186172] (-) TimerEvent: {}
[430.286704] (-) TimerEvent: {}
[430.387210] (-) TimerEvent: {}
[430.487779] (-) TimerEvent: {}
[430.588292] (-) TimerEvent: {}
[430.688856] (-) TimerEvent: {}
[430.789456] (-) TimerEvent: {}
[430.890207] (-) TimerEvent: {}
[430.990807] (-) TimerEvent: {}
[431.091330] (-) TimerEvent: {}
[431.191750] (-) TimerEvent: {}
[431.292465] (-) TimerEvent: {}
[431.393012] (-) TimerEvent: {}
[431.493686] (-) TimerEvent: {}
[431.594532] (-) TimerEvent: {}
[431.695228] (-) TimerEvent: {}
[431.796458] (-) TimerEvent: {}
[431.898232] (-) TimerEvent: {}
[431.998895] (-) TimerEvent: {}
[432.099644] (-) TimerEvent: {}
[432.200213] (-) TimerEvent: {}
[432.301174] (-) TimerEvent: {}
[432.402177] (-) TimerEvent: {}
[432.502790] (-) TimerEvent: {}
[432.603508] (-) TimerEvent: {}
[432.704115] (-) TimerEvent: {}
[432.804816] (-) TimerEvent: {}
[432.906174] (-) TimerEvent: {}
[433.007056] (-) TimerEvent: {}
[433.107633] (-) TimerEvent: {}
[433.208153] (-) TimerEvent: {}
[433.308628] (-) TimerEvent: {}
[433.409559] (-) TimerEvent: {}
[433.510166] (-) TimerEvent: {}
[433.610697] (-) TimerEvent: {}
[433.711185] (-) TimerEvent: {}
[433.811719] (-) TimerEvent: {}
[433.912108] (-) TimerEvent: {}
[434.012636] (-) TimerEvent: {}
[434.113349] (-) TimerEvent: {}
[434.214189] (-) TimerEvent: {}
[434.314700] (-) TimerEvent: {}
[434.415507] (-) TimerEvent: {}
[434.516666] (-) TimerEvent: {}
[434.617238] (-) TimerEvent: {}
[434.718200] (-) TimerEvent: {}
[434.818810] (-) TimerEvent: {}
[434.919495] (-) TimerEvent: {}
[435.019923] (-) TimerEvent: {}
[435.120481] (-) TimerEvent: {}
[435.221353] (-) TimerEvent: {}
[435.322415] (-) TimerEvent: {}
[435.423351] (-) TimerEvent: {}
[435.523917] (-) TimerEvent: {}
[435.624471] (-) TimerEvent: {}
[435.725160] (-) TimerEvent: {}
[435.826200] (-) TimerEvent: {}
[435.926920] (-) TimerEvent: {}
[436.027427] (-) TimerEvent: {}
[436.128164] (-) TimerEvent: {}
[436.229119] (-) TimerEvent: {}
[436.329540] (-) TimerEvent: {}
[436.430212] (-) TimerEvent: {}
[436.530709] (-) TimerEvent: {}
[436.631188] (-) TimerEvent: {}
[436.731783] (-) TimerEvent: {}
[436.832180] (-) TimerEvent: {}
[436.932585] (-) TimerEvent: {}
[437.033399] (-) TimerEvent: {}
[437.134572] (-) TimerEvent: {}
[437.236199] (-) TimerEvent: {}
[437.336546] (-) TimerEvent: {}
[437.437544] (-) TimerEvent: {}
[437.538157] (-) TimerEvent: {}
[437.638641] (-) TimerEvent: {}
[437.739660] (-) TimerEvent: {}
[437.839940] (-) TimerEvent: {}
[437.940248] (-) TimerEvent: {}
[438.040566] (-) TimerEvent: {}
[438.140915] (-) TimerEvent: {}
[438.241565] (-) TimerEvent: {}
[438.342950] (-) TimerEvent: {}
[438.368835] (ros1_bridge) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__srv__UpdateFilename__factories.cpp.o\x1b[0m\n'}
[438.443319] (-) TimerEvent: {}
[438.543775] (-) TimerEvent: {}
[438.644558] (-) TimerEvent: {}
[438.745582] (-) TimerEvent: {}
[438.847182] (-) TimerEvent: {}
[438.948200] (-) TimerEvent: {}
[439.048890] (-) TimerEvent: {}
[439.150239] (-) TimerEvent: {}
[439.251297] (-) TimerEvent: {}
[439.351767] (-) TimerEvent: {}
[439.452087] (-) TimerEvent: {}
[439.552457] (-) TimerEvent: {}
[439.652814] (-) TimerEvent: {}
[439.753403] (-) TimerEvent: {}
[439.854158] (-) TimerEvent: {}
[439.954907] (-) TimerEvent: {}
[440.055323] (-) TimerEvent: {}
[440.155740] (-) TimerEvent: {}
[440.256190] (-) TimerEvent: {}
[440.356712] (-) TimerEvent: {}
[440.386289] (ros1_bridge) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs_factories.cpp.o\x1b[0m\n'}
[440.456803] (-) TimerEvent: {}
[440.557624] (-) TimerEvent: {}
[440.658375] (-) TimerEvent: {}
[440.759254] (-) TimerEvent: {}
[440.859688] (-) TimerEvent: {}
[440.960254] (-) TimerEvent: {}
[441.060938] (-) TimerEvent: {}
[441.161597] (-) TimerEvent: {}
[441.262240] (-) TimerEvent: {}
[441.362857] (-) TimerEvent: {}
[441.463521] (-) TimerEvent: {}
[441.564200] (-) TimerEvent: {}
[441.664781] (-) TimerEvent: {}
[441.765593] (-) TimerEvent: {}
[441.866507] (-) TimerEvent: {}
[441.967166] (-) TimerEvent: {}
[442.067790] (-) TimerEvent: {}
[442.168485] (-) TimerEvent: {}
[442.269255] (-) TimerEvent: {}
[442.370190] (-) TimerEvent: {}
[442.471321] (-) TimerEvent: {}
[442.571820] (-) TimerEvent: {}
[442.672413] (-) TimerEvent: {}
[442.773138] (-) TimerEvent: {}
[442.874329] (-) TimerEvent: {}
[442.974998] (-) TimerEvent: {}
[443.076397] (-) TimerEvent: {}
[443.177102] (-) TimerEvent: {}
[443.278281] (-) TimerEvent: {}
[443.378922] (-) TimerEvent: {}
[443.480176] (-) TimerEvent: {}
[443.581691] (-) TimerEvent: {}
[443.682416] (-) TimerEvent: {}
[443.783334] (-) TimerEvent: {}
[443.884185] (-) TimerEvent: {}
[443.985155] (-) TimerEvent: {}
[444.085608] (-) TimerEvent: {}
[444.186525] (-) TimerEvent: {}
[444.286895] (-) TimerEvent: {}
[444.387318] (-) TimerEvent: {}
[444.487987] (-) TimerEvent: {}
[444.588768] (-) TimerEvent: {}
[444.690249] (-) TimerEvent: {}
[444.791699] (-) TimerEvent: {}
[444.892811] (-) TimerEvent: {}
[444.994094] (-) TimerEvent: {}
[445.095272] (-) TimerEvent: {}
[445.195873] (-) TimerEvent: {}
[445.296919] (-) TimerEvent: {}
[445.398233] (-) TimerEvent: {}
[445.498828] (-) TimerEvent: {}
[445.599379] (-) TimerEvent: {}
[445.619425] (ros1_bridge) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__JointState__factories.cpp.o\x1b[0m\n'}
[445.699558] (-) TimerEvent: {}
[445.800204] (-) TimerEvent: {}
[445.901214] (-) TimerEvent: {}
[446.002232] (-) TimerEvent: {}
[446.103183] (-) TimerEvent: {}
[446.203832] (-) TimerEvent: {}
[446.305034] (-) TimerEvent: {}
[446.405552] (-) TimerEvent: {}
[446.506194] (-) TimerEvent: {}
[446.589271] (ros1_bridge) StdoutLine: {'line': b'[ 51%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__JointCommand__factories.cpp.o\x1b[0m\n'}
[446.606478] (-) TimerEvent: {}
[446.707120] (-) TimerEvent: {}
[446.807811] (-) TimerEvent: {}
[446.908225] (-) TimerEvent: {}
[447.009057] (-) TimerEvent: {}
[447.110205] (-) TimerEvent: {}
[447.211065] (-) TimerEvent: {}
[447.311666] (-) TimerEvent: {}
[447.413082] (-) TimerEvent: {}
[447.514635] (-) TimerEvent: {}
[447.615423] (-) TimerEvent: {}
[447.716018] (-) TimerEvent: {}
[447.816643] (-) TimerEvent: {}
[447.917170] (-) TimerEvent: {}
[448.018232] (-) TimerEvent: {}
[448.118736] (-) TimerEvent: {}
[448.219553] (-) TimerEvent: {}
[448.320039] (-) TimerEvent: {}
[448.420428] (-) TimerEvent: {}
[448.521228] (-) TimerEvent: {}
[448.622169] (-) TimerEvent: {}
[448.722781] (-) TimerEvent: {}
[448.823912] (-) TimerEvent: {}
[448.925075] (-) TimerEvent: {}
[449.026482] (-) TimerEvent: {}
[449.127044] (-) TimerEvent: {}
[449.227597] (-) TimerEvent: {}
[449.328166] (-) TimerEvent: {}
[449.429020] (-) TimerEvent: {}
[449.529436] (-) TimerEvent: {}
[449.630194] (-) TimerEvent: {}
[449.731014] (-) TimerEvent: {}
[449.831506] (-) TimerEvent: {}
[449.932010] (-) TimerEvent: {}
[450.033415] (-) TimerEvent: {}
[450.134256] (-) TimerEvent: {}
[450.234831] (-) TimerEvent: {}
[450.335416] (-) TimerEvent: {}
[450.435976] (-) TimerEvent: {}
[450.544370] (-) TimerEvent: {}
[450.645335] (-) TimerEvent: {}
[450.665704] (ros1_bridge) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__RttestResults__factories.cpp.o\x1b[0m\n'}
[450.746188] (-) TimerEvent: {}
[450.846577] (-) TimerEvent: {}
[450.946976] (-) TimerEvent: {}
[451.047303] (-) TimerEvent: {}
[451.147705] (-) TimerEvent: {}
[451.248176] (-) TimerEvent: {}
[451.342000] (ros1_bridge) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces_factories.cpp.o\x1b[0m\n'}
[451.348278] (-) TimerEvent: {}
[451.448845] (-) TimerEvent: {}
[451.550246] (-) TimerEvent: {}
[451.650802] (-) TimerEvent: {}
[451.751764] (-) TimerEvent: {}
[451.852225] (-) TimerEvent: {}
[451.953184] (-) TimerEvent: {}
[452.053745] (-) TimerEvent: {}
[452.154545] (-) TimerEvent: {}
[452.255097] (-) TimerEvent: {}
[452.355759] (-) TimerEvent: {}
[452.456293] (-) TimerEvent: {}
[452.556747] (-) TimerEvent: {}
[452.657439] (-) TimerEvent: {}
[452.758208] (-) TimerEvent: {}
[452.858598] (-) TimerEvent: {}
[452.959107] (-) TimerEvent: {}
[453.060136] (-) TimerEvent: {}
[453.161076] (-) TimerEvent: {}
[453.262147] (-) TimerEvent: {}
[453.362590] (-) TimerEvent: {}
[453.463037] (-) TimerEvent: {}
[453.563397] (-) TimerEvent: {}
[453.663953] (-) TimerEvent: {}
[453.764357] (-) TimerEvent: {}
[453.864906] (-) TimerEvent: {}
[453.965474] (-) TimerEvent: {}
[454.066163] (-) TimerEvent: {}
[454.166582] (-) TimerEvent: {}
[454.267058] (-) TimerEvent: {}
[454.367497] (-) TimerEvent: {}
[454.467911] (-) TimerEvent: {}
[454.568918] (-) TimerEvent: {}
[454.670206] (-) TimerEvent: {}
[454.687789] (ros1_bridge) StdoutLine: {'line': b'[ 52%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__FloatingPointRange__factories.cpp.o\x1b[0m\n'}
[454.770315] (-) TimerEvent: {}
[454.870830] (-) TimerEvent: {}
[454.971373] (-) TimerEvent: {}
[455.071832] (-) TimerEvent: {}
[455.172560] (-) TimerEvent: {}
[455.273156] (-) TimerEvent: {}
[455.338251] (ros1_bridge) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__IntegerRange__factories.cpp.o\x1b[0m\n'}
[455.373266] (-) TimerEvent: {}
[455.474190] (-) TimerEvent: {}
[455.574602] (-) TimerEvent: {}
[455.675206] (-) TimerEvent: {}
[455.775676] (-) TimerEvent: {}
[455.876146] (-) TimerEvent: {}
[455.976852] (-) TimerEvent: {}
[456.077394] (-) TimerEvent: {}
[456.178168] (-) TimerEvent: {}
[456.278980] (-) TimerEvent: {}
[456.379542] (-) TimerEvent: {}
[456.479979] (-) TimerEvent: {}
[456.580835] (-) TimerEvent: {}
[456.682148] (-) TimerEvent: {}
[456.782570] (-) TimerEvent: {}
[456.883049] (-) TimerEvent: {}
[456.983589] (-) TimerEvent: {}
[457.084116] (-) TimerEvent: {}
[457.184937] (-) TimerEvent: {}
[457.286183] (-) TimerEvent: {}
[457.386978] (-) TimerEvent: {}
[457.487564] (-) TimerEvent: {}
[457.588033] (-) TimerEvent: {}
[457.688510] (-) TimerEvent: {}
[457.788972] (-) TimerEvent: {}
[457.890143] (-) TimerEvent: {}
[457.990754] (-) TimerEvent: {}
[458.092035] (-) TimerEvent: {}
[458.193047] (-) TimerEvent: {}
[458.294163] (-) TimerEvent: {}
[458.395047] (-) TimerEvent: {}
[458.495544] (-) TimerEvent: {}
[458.596579] (-) TimerEvent: {}
[458.616452] (ros1_bridge) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ListParametersResult__factories.cpp.o\x1b[0m\n'}
[458.697211] (-) TimerEvent: {}
[458.798217] (-) TimerEvent: {}
[458.898579] (-) TimerEvent: {}
[458.999215] (-) TimerEvent: {}
[459.099946] (-) TimerEvent: {}
[459.201268] (-) TimerEvent: {}
[459.302185] (-) TimerEvent: {}
[459.389330] (ros1_bridge) StdoutLine: {'line': b'[ 53%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__Log__factories.cpp.o\x1b[0m\n'}
[459.402289] (-) TimerEvent: {}
[459.502988] (-) TimerEvent: {}
[459.603411] (-) TimerEvent: {}
[459.703874] (-) TimerEvent: {}
[459.804363] (-) TimerEvent: {}
[459.904677] (-) TimerEvent: {}
[460.005084] (-) TimerEvent: {}
[460.106162] (-) TimerEvent: {}
[460.206544] (-) TimerEvent: {}
[460.306916] (-) TimerEvent: {}
[460.407293] (-) TimerEvent: {}
[460.507669] (-) TimerEvent: {}
[460.608093] (-) TimerEvent: {}
[460.708486] (-) TimerEvent: {}
[460.808894] (-) TimerEvent: {}
[460.910150] (-) TimerEvent: {}
[461.010524] (-) TimerEvent: {}
[461.110919] (-) TimerEvent: {}
[461.211801] (-) TimerEvent: {}
[461.312180] (-) TimerEvent: {}
[461.413055] (-) TimerEvent: {}
[461.514160] (-) TimerEvent: {}
[461.614696] (-) TimerEvent: {}
[461.715063] (-) TimerEvent: {}
[461.815476] (-) TimerEvent: {}
[461.915847] (-) TimerEvent: {}
[462.016780] (-) TimerEvent: {}
[462.117271] (-) TimerEvent: {}
[462.218151] (-) TimerEvent: {}
[462.318498] (-) TimerEvent: {}
[462.418828] (-) TimerEvent: {}
[462.519262] (-) TimerEvent: {}
[462.558048] (ros1_bridge) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterDescriptor__factories.cpp.o\x1b[0m\n'}
[462.619549] (-) TimerEvent: {}
[462.719880] (-) TimerEvent: {}
[462.820872] (-) TimerEvent: {}
[462.921297] (-) TimerEvent: {}
[463.022187] (-) TimerEvent: {}
[463.122572] (-) TimerEvent: {}
[463.223038] (-) TimerEvent: {}
[463.323751] (-) TimerEvent: {}
[463.424174] (-) TimerEvent: {}
[463.524627] (-) TimerEvent: {}
[463.625178] (-) TimerEvent: {}
[463.726176] (-) TimerEvent: {}
[463.827060] (-) TimerEvent: {}
[463.927569] (-) TimerEvent: {}
[464.028093] (-) TimerEvent: {}
[464.129127] (-) TimerEvent: {}
[464.230548] (-) TimerEvent: {}
[464.331161] (-) TimerEvent: {}
[464.431725] (-) TimerEvent: {}
[464.532310] (-) TimerEvent: {}
[464.632700] (-) TimerEvent: {}
[464.733221] (-) TimerEvent: {}
[464.834156] (-) TimerEvent: {}
[464.934559] (-) TimerEvent: {}
[465.034982] (-) TimerEvent: {}
[465.135577] (-) TimerEvent: {}
[465.236992] (-) TimerEvent: {}
[465.337394] (-) TimerEvent: {}
[465.438155] (-) TimerEvent: {}
[465.538561] (-) TimerEvent: {}
[465.638973] (-) TimerEvent: {}
[465.740022] (-) TimerEvent: {}
[465.840472] (-) TimerEvent: {}
[465.940875] (-) TimerEvent: {}
[466.041307] (-) TimerEvent: {}
[466.142279] (-) TimerEvent: {}
[466.243225] (-) TimerEvent: {}
[466.245975] (ros1_bridge) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterEventDescriptors__factories.cpp.o\x1b[0m\n'}
[466.343633] (-) TimerEvent: {}
[466.444066] (-) TimerEvent: {}
[466.544540] (-) TimerEvent: {}
[466.645223] (-) TimerEvent: {}
[466.745639] (-) TimerEvent: {}
[466.846646] (-) TimerEvent: {}
[466.947131] (-) TimerEvent: {}
[467.047587] (-) TimerEvent: {}
[467.148302] (-) TimerEvent: {}
[467.249054] (-) TimerEvent: {}
[467.351403] (-) TimerEvent: {}
[467.451922] (-) TimerEvent: {}
[467.552925] (-) TimerEvent: {}
[467.654172] (-) TimerEvent: {}
[467.754909] (-) TimerEvent: {}
[467.816284] (ros1_bridge) StdoutLine: {'line': b'[ 54%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterEvent__factories.cpp.o\x1b[0m\n'}
[467.855098] (-) TimerEvent: {}
[467.955758] (-) TimerEvent: {}
[468.056254] (-) TimerEvent: {}
[468.156713] (-) TimerEvent: {}
[468.257567] (-) TimerEvent: {}
[468.358179] (-) TimerEvent: {}
[468.458819] (-) TimerEvent: {}
[468.559251] (-) TimerEvent: {}
[468.659724] (-) TimerEvent: {}
[468.760187] (-) TimerEvent: {}
[468.861193] (-) TimerEvent: {}
[468.962169] (-) TimerEvent: {}
[469.062740] (-) TimerEvent: {}
[469.163567] (-) TimerEvent: {}
[469.264660] (-) TimerEvent: {}
[469.365038] (-) TimerEvent: {}
[469.466168] (-) TimerEvent: {}
[469.566642] (-) TimerEvent: {}
[469.667522] (-) TimerEvent: {}
[469.768516] (-) TimerEvent: {}
[469.869542] (-) TimerEvent: {}
[469.927490] (ros1_bridge) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__Parameter__factories.cpp.o\x1b[0m\n'}
[469.970116] (-) TimerEvent: {}
[470.070515] (-) TimerEvent: {}
[470.170946] (-) TimerEvent: {}
[470.272044] (-) TimerEvent: {}
[470.372488] (-) TimerEvent: {}
[470.472948] (-) TimerEvent: {}
[470.573396] (-) TimerEvent: {}
[470.674159] (-) TimerEvent: {}
[470.774537] (-) TimerEvent: {}
[470.875018] (-) TimerEvent: {}
[470.975652] (-) TimerEvent: {}
[471.076180] (-) TimerEvent: {}
[471.177277] (-) TimerEvent: {}
[471.278312] (-) TimerEvent: {}
[471.379059] (-) TimerEvent: {}
[471.479632] (-) TimerEvent: {}
[471.581136] (-) TimerEvent: {}
[471.637990] (ros1_bridge) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterType__factories.cpp.o\x1b[0m\n'}
[471.681576] (-) TimerEvent: {}
[471.782189] (-) TimerEvent: {}
[471.883164] (-) TimerEvent: {}
[471.983732] (-) TimerEvent: {}
[472.084140] (-) TimerEvent: {}
[472.184644] (-) TimerEvent: {}
[472.285198] (-) TimerEvent: {}
[472.386289] (-) TimerEvent: {}
[472.487252] (-) TimerEvent: {}
[472.587902] (-) TimerEvent: {}
[472.688656] (-) TimerEvent: {}
[472.790238] (-) TimerEvent: {}
[472.891475] (-) TimerEvent: {}
[472.992225] (-) TimerEvent: {}
[473.092751] (-) TimerEvent: {}
[473.193190] (-) TimerEvent: {}
[473.294155] (-) TimerEvent: {}
[473.394638] (-) TimerEvent: {}
[473.495154] (-) TimerEvent: {}
[473.596095] (-) TimerEvent: {}
[473.697201] (-) TimerEvent: {}
[473.798143] (-) TimerEvent: {}
[473.898499] (-) TimerEvent: {}
[473.998981] (-) TimerEvent: {}
[474.100338] (-) TimerEvent: {}
[474.144696] (ros1_bridge) StdoutLine: {'line': b'[ 55%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterValue__factories.cpp.o\x1b[0m\n'}
[474.200576] (-) TimerEvent: {}
[474.302191] (-) TimerEvent: {}
[474.403144] (-) TimerEvent: {}
[474.503712] (-) TimerEvent: {}
[474.604460] (-) TimerEvent: {}
[474.704956] (-) TimerEvent: {}
[474.806266] (-) TimerEvent: {}
[474.906867] (-) TimerEvent: {}
[475.007653] (-) TimerEvent: {}
[475.108228] (-) TimerEvent: {}
[475.208811] (-) TimerEvent: {}
[475.309286] (-) TimerEvent: {}
[475.410169] (-) TimerEvent: {}
[475.510796] (-) TimerEvent: {}
[475.611239] (-) TimerEvent: {}
[475.711680] (-) TimerEvent: {}
[475.812123] (-) TimerEvent: {}
[475.913248] (-) TimerEvent: {}
[476.015066] (-) TimerEvent: {}
[476.115508] (-) TimerEvent: {}
[476.215968] (-) TimerEvent: {}
[476.316437] (-) TimerEvent: {}
[476.417223] (-) TimerEvent: {}
[476.518830] (-) TimerEvent: {}
[476.619199] (-) TimerEvent: {}
[476.719674] (-) TimerEvent: {}
[476.820130] (-) TimerEvent: {}
[476.920547] (-) TimerEvent: {}
[477.020904] (-) TimerEvent: {}
[477.121752] (-) TimerEvent: {}
[477.223893] (-) TimerEvent: {}
[477.325182] (-) TimerEvent: {}
[477.426584] (-) TimerEvent: {}
[477.526994] (-) TimerEvent: {}
[477.627429] (-) TimerEvent: {}
[477.678529] (ros1_bridge) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__SetParametersResult__factories.cpp.o\x1b[0m\n'}
[477.727533] (-) TimerEvent: {}
[477.827984] (-) TimerEvent: {}
[477.928370] (-) TimerEvent: {}
[478.028828] (-) TimerEvent: {}
[478.129261] (-) TimerEvent: {}
[478.230133] (-) TimerEvent: {}
[478.330534] (-) TimerEvent: {}
[478.431001] (-) TimerEvent: {}
[478.531639] (-) TimerEvent: {}
[478.632430] (-) TimerEvent: {}
[478.733361] (-) TimerEvent: {}
[478.834224] (-) TimerEvent: {}
[478.935605] (-) TimerEvent: {}
[479.036612] (-) TimerEvent: {}
[479.137375] (-) TimerEvent: {}
[479.238138] (-) TimerEvent: {}
[479.339547] (-) TimerEvent: {}
[479.439909] (-) TimerEvent: {}
[479.541072] (-) TimerEvent: {}
[479.642921] (-) TimerEvent: {}
[479.746922] (-) TimerEvent: {}
[479.847367] (-) TimerEvent: {}
[479.947700] (-) TimerEvent: {}
[480.048079] (-) TimerEvent: {}
[480.148506] (-) TimerEvent: {}
[480.248882] (-) TimerEvent: {}
[480.350142] (-) TimerEvent: {}
[480.450659] (-) TimerEvent: {}
[480.551211] (-) TimerEvent: {}
[480.651572] (-) TimerEvent: {}
[480.704805] (ros1_bridge) StdoutLine: {'line': b'[ 56%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__DescribeParameters__factories.cpp.o\x1b[0m\n'}
[480.751754] (-) TimerEvent: {}
[480.852224] (-) TimerEvent: {}
[480.887632] (ros1_bridge) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__GetParameters__factories.cpp.o\x1b[0m\n'}
[480.952328] (-) TimerEvent: {}
[481.053296] (-) TimerEvent: {}
[481.154488] (-) TimerEvent: {}
[481.255316] (-) TimerEvent: {}
[481.355763] (-) TimerEvent: {}
[481.456181] (-) TimerEvent: {}
[481.556622] (-) TimerEvent: {}
[481.657030] (-) TimerEvent: {}
[481.757388] (-) TimerEvent: {}
[481.858388] (-) TimerEvent: {}
[481.958887] (-) TimerEvent: {}
[482.059350] (-) TimerEvent: {}
[482.159812] (-) TimerEvent: {}
[482.260327] (-) TimerEvent: {}
[482.361132] (-) TimerEvent: {}
[482.462149] (-) TimerEvent: {}
[482.562614] (-) TimerEvent: {}
[482.663346] (-) TimerEvent: {}
[482.764533] (-) TimerEvent: {}
[482.865092] (-) TimerEvent: {}
[482.966142] (-) TimerEvent: {}
[483.066552] (-) TimerEvent: {}
[483.167371] (-) TimerEvent: {}
[483.268065] (-) TimerEvent: {}
[483.368525] (-) TimerEvent: {}
[483.470209] (-) TimerEvent: {}
[483.571191] (-) TimerEvent: {}
[483.671650] (-) TimerEvent: {}
[483.772164] (-) TimerEvent: {}
[483.873231] (-) TimerEvent: {}
[483.974148] (-) TimerEvent: {}
[484.074834] (-) TimerEvent: {}
[484.175520] (-) TimerEvent: {}
[484.276735] (-) TimerEvent: {}
[484.377201] (-) TimerEvent: {}
[484.404088] (ros1_bridge) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__GetParameterTypes__factories.cpp.o\x1b[0m\n'}
[484.405522] (ros1_bridge) StdoutLine: {'line': b'[ 57%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__ListParameters__factories.cpp.o\x1b[0m\n'}
[484.477307] (-) TimerEvent: {}
[484.578187] (-) TimerEvent: {}
[484.678587] (-) TimerEvent: {}
[484.779496] (-) TimerEvent: {}
[484.879880] (-) TimerEvent: {}
[484.980238] (-) TimerEvent: {}
[485.080754] (-) TimerEvent: {}
[485.181529] (-) TimerEvent: {}
[485.282279] (-) TimerEvent: {}
[485.382824] (-) TimerEvent: {}
[485.483312] (-) TimerEvent: {}
[485.583758] (-) TimerEvent: {}
[485.684162] (-) TimerEvent: {}
[485.785172] (-) TimerEvent: {}
[485.886208] (-) TimerEvent: {}
[485.986768] (-) TimerEvent: {}
[486.087342] (-) TimerEvent: {}
[486.187771] (-) TimerEvent: {}
[486.288205] (-) TimerEvent: {}
[486.389200] (-) TimerEvent: {}
[486.489586] (-) TimerEvent: {}
[486.590215] (-) TimerEvent: {}
[486.690589] (-) TimerEvent: {}
[486.791220] (-) TimerEvent: {}
[486.891606] (-) TimerEvent: {}
[486.991990] (-) TimerEvent: {}
[487.092414] (-) TimerEvent: {}
[487.192832] (-) TimerEvent: {}
[487.293222] (-) TimerEvent: {}
[487.393612] (-) TimerEvent: {}
[487.494297] (-) TimerEvent: {}
[487.595152] (-) TimerEvent: {}
[487.695542] (-) TimerEvent: {}
[487.795942] (-) TimerEvent: {}
[487.896350] (-) TimerEvent: {}
[487.996768] (-) TimerEvent: {}
[488.058324] (ros1_bridge) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__SetParametersAtomically__factories.cpp.o\x1b[0m\n'}
[488.097183] (-) TimerEvent: {}
[488.197531] (-) TimerEvent: {}
[488.204496] (ros1_bridge) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__SetParameters__factories.cpp.o\x1b[0m\n'}
[488.298226] (-) TimerEvent: {}
[488.398644] (-) TimerEvent: {}
[488.499050] (-) TimerEvent: {}
[488.599432] (-) TimerEvent: {}
[488.700190] (-) TimerEvent: {}
[488.801133] (-) TimerEvent: {}
[488.901603] (-) TimerEvent: {}
[489.002211] (-) TimerEvent: {}
[489.103057] (-) TimerEvent: {}
[489.203577] (-) TimerEvent: {}
[489.304042] (-) TimerEvent: {}
[489.404521] (-) TimerEvent: {}
[489.505043] (-) TimerEvent: {}
[489.605575] (-) TimerEvent: {}
[489.706239] (-) TimerEvent: {}
[489.806705] (-) TimerEvent: {}
[489.907194] (-) TimerEvent: {}
[490.007637] (-) TimerEvent: {}
[490.108057] (-) TimerEvent: {}
[490.209025] (-) TimerEvent: {}
[490.309408] (-) TimerEvent: {}
[490.410203] (-) TimerEvent: {}
[490.510606] (-) TimerEvent: {}
[490.611173] (-) TimerEvent: {}
[490.711614] (-) TimerEvent: {}
[490.812025] (-) TimerEvent: {}
[490.913045] (-) TimerEvent: {}
[491.013420] (-) TimerEvent: {}
[491.114171] (-) TimerEvent: {}
[491.214589] (-) TimerEvent: {}
[491.315066] (-) TimerEvent: {}
[491.415484] (-) TimerEvent: {}
[491.515897] (-) TimerEvent: {}
[491.595627] (ros1_bridge) StdoutLine: {'line': b'[ 58%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common_factories.cpp.o\x1b[0m\n'}
[491.616053] (-) TimerEvent: {}
[491.716434] (-) TimerEvent: {}
[491.817205] (-) TimerEvent: {}
[491.859954] (ros1_bridge) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__Gid__factories.cpp.o\x1b[0m\n'}
[491.917266] (-) TimerEvent: {}
[492.017621] (-) TimerEvent: {}
[492.118289] (-) TimerEvent: {}
[492.218679] (-) TimerEvent: {}
[492.319027] (-) TimerEvent: {}
[492.419395] (-) TimerEvent: {}
[492.519773] (-) TimerEvent: {}
[492.620205] (-) TimerEvent: {}
[492.721128] (-) TimerEvent: {}
[492.822168] (-) TimerEvent: {}
[492.922593] (-) TimerEvent: {}
[493.023065] (-) TimerEvent: {}
[493.123910] (-) TimerEvent: {}
[493.224299] (-) TimerEvent: {}
[493.325279] (-) TimerEvent: {}
[493.426198] (-) TimerEvent: {}
[493.527075] (-) TimerEvent: {}
[493.627641] (-) TimerEvent: {}
[493.728162] (-) TimerEvent: {}
[493.829216] (-) TimerEvent: {}
[493.929586] (-) TimerEvent: {}
[494.030151] (-) TimerEvent: {}
[494.130548] (-) TimerEvent: {}
[494.231109] (-) TimerEvent: {}
[494.331521] (-) TimerEvent: {}
[494.432012] (-) TimerEvent: {}
[494.532901] (-) TimerEvent: {}
[494.633341] (-) TimerEvent: {}
[494.734157] (-) TimerEvent: {}
[494.834590] (-) TimerEvent: {}
[494.935044] (-) TimerEvent: {}
[495.035972] (-) TimerEvent: {}
[495.136638] (-) TimerEvent: {}
[495.198767] (ros1_bridge) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__NodeEntitiesInfo__factories.cpp.o\x1b[0m\n'}
[495.236798] (-) TimerEvent: {}
[495.337226] (-) TimerEvent: {}
[495.437615] (-) TimerEvent: {}
[495.538200] (-) TimerEvent: {}
[495.560419] (ros1_bridge) StdoutLine: {'line': b'[ 59%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__ParticipantEntitiesInfo__factories.cpp.o\x1b[0m\n'}
[495.638326] (-) TimerEvent: {}
[495.738924] (-) TimerEvent: {}
[495.839407] (-) TimerEvent: {}
[495.939895] (-) TimerEvent: {}
[496.040250] (-) TimerEvent: {}
[496.140601] (-) TimerEvent: {}
[496.241180] (-) TimerEvent: {}
[496.341562] (-) TimerEvent: {}
[496.442146] (-) TimerEvent: {}
[496.542506] (-) TimerEvent: {}
[496.642844] (-) TimerEvent: {}
[496.743244] (-) TimerEvent: {}
[496.843805] (-) TimerEvent: {}
[496.944211] (-) TimerEvent: {}
[497.044604] (-) TimerEvent: {}
[497.145039] (-) TimerEvent: {}
[497.246154] (-) TimerEvent: {}
[497.346569] (-) TimerEvent: {}
[497.447419] (-) TimerEvent: {}
[497.547756] (-) TimerEvent: {}
[497.648219] (-) TimerEvent: {}
[497.749004] (-) TimerEvent: {}
[497.849406] (-) TimerEvent: {}
[497.950143] (-) TimerEvent: {}
[498.050591] (-) TimerEvent: {}
[498.151105] (-) TimerEvent: {}
[498.251657] (-) TimerEvent: {}
[498.352019] (-) TimerEvent: {}
[498.453013] (-) TimerEvent: {}
[498.554171] (-) TimerEvent: {}
[498.654604] (-) TimerEvent: {}
[498.755066] (-) TimerEvent: {}
[498.758188] (ros1_bridge) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rosgraph_msgs_factories.cpp.o\x1b[0m\n'}
[498.855179] (-) TimerEvent: {}
[498.955963] (-) TimerEvent: {}
[499.056401] (-) TimerEvent: {}
[499.157113] (-) TimerEvent: {}
[499.258264] (-) TimerEvent: {}
[499.289432] (ros1_bridge) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rosgraph_msgs__msg__Clock__factories.cpp.o\x1b[0m\n'}
[499.358375] (-) TimerEvent: {}
[499.458762] (-) TimerEvent: {}
[499.559168] (-) TimerEvent: {}
[499.659547] (-) TimerEvent: {}
[499.760510] (-) TimerEvent: {}
[499.862182] (-) TimerEvent: {}
[499.962577] (-) TimerEvent: {}
[500.063162] (-) TimerEvent: {}
[500.164065] (-) TimerEvent: {}
[500.264915] (-) TimerEvent: {}
[500.366199] (-) TimerEvent: {}
[500.467019] (-) TimerEvent: {}
[500.567595] (-) TimerEvent: {}
[500.668107] (-) TimerEvent: {}
[500.768975] (-) TimerEvent: {}
[500.870157] (-) TimerEvent: {}
[500.970569] (-) TimerEvent: {}
[501.071153] (-) TimerEvent: {}
[501.171603] (-) TimerEvent: {}
[501.271993] (-) TimerEvent: {}
[501.372817] (-) TimerEvent: {}
[501.473198] (-) TimerEvent: {}
[501.573598] (-) TimerEvent: {}
[501.674271] (-) TimerEvent: {}
[501.774960] (-) TimerEvent: {}
[501.875359] (-) TimerEvent: {}
[501.975830] (-) TimerEvent: {}
[502.076610] (-) TimerEvent: {}
[502.177054] (-) TimerEvent: {}
[502.278168] (-) TimerEvent: {}
[502.379038] (-) TimerEvent: {}
[502.479531] (-) TimerEvent: {}
[502.580113] (-) TimerEvent: {}
[502.652560] (ros1_bridge) StdoutLine: {'line': b'[ 60%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs_factories.cpp.o\x1b[0m\n'}
[502.680279] (-) TimerEvent: {}
[502.780832] (-) TimerEvent: {}
[502.882224] (-) TimerEvent: {}
[502.982901] (-) TimerEvent: {}
[503.083367] (-) TimerEvent: {}
[503.183901] (-) TimerEvent: {}
[503.284314] (-) TimerEvent: {}
[503.385279] (-) TimerEvent: {}
[503.486207] (-) TimerEvent: {}
[503.586602] (-) TimerEvent: {}
[503.687064] (-) TimerEvent: {}
[503.787509] (-) TimerEvent: {}
[503.887973] (-) TimerEvent: {}
[503.988379] (-) TimerEvent: {}
[504.088758] (-) TimerEvent: {}
[504.190170] (-) TimerEvent: {}
[504.290585] (-) TimerEvent: {}
[504.391078] (-) TimerEvent: {}
[504.491478] (-) TimerEvent: {}
[504.592077] (-) TimerEvent: {}
[504.693133] (-) TimerEvent: {}
[504.793537] (-) TimerEvent: {}
[504.894154] (-) TimerEvent: {}
[504.994602] (-) TimerEvent: {}
[505.095050] (-) TimerEvent: {}
[505.195512] (-) TimerEvent: {}
[505.295859] (-) TimerEvent: {}
[505.396211] (-) TimerEvent: {}
[505.496582] (-) TimerEvent: {}
[505.596913] (-) TimerEvent: {}
[505.698231] (-) TimerEvent: {}
[505.798685] (-) TimerEvent: {}
[505.899062] (-) TimerEvent: {}
[505.999893] (-) TimerEvent: {}
[506.100288] (-) TimerEvent: {}
[506.201044] (-) TimerEvent: {}
[506.301470] (-) TimerEvent: {}
[506.402419] (-) TimerEvent: {}
[506.502807] (-) TimerEvent: {}
[506.603218] (-) TimerEvent: {}
[506.703800] (-) TimerEvent: {}
[506.804757] (-) TimerEvent: {}
[506.843939] (ros1_bridge) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__BatteryState__factories.cpp.o\x1b[0m\n'}
[506.904877] (-) TimerEvent: {}
[507.005360] (-) TimerEvent: {}
[507.106729] (-) TimerEvent: {}
[507.207184] (-) TimerEvent: {}
[507.308408] (-) TimerEvent: {}
[507.409116] (-) TimerEvent: {}
[507.509523] (-) TimerEvent: {}
[507.610311] (-) TimerEvent: {}
[507.711146] (-) TimerEvent: {}
[507.805740] (ros1_bridge) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__CameraInfo__factories.cpp.o\x1b[0m\n'}
[507.811374] (-) TimerEvent: {}
[507.911727] (-) TimerEvent: {}
[508.012113] (-) TimerEvent: {}
[508.112636] (-) TimerEvent: {}
[508.212998] (-) TimerEvent: {}
[508.313363] (-) TimerEvent: {}
[508.414208] (-) TimerEvent: {}
[508.514593] (-) TimerEvent: {}
[508.614969] (-) TimerEvent: {}
[508.716031] (-) TimerEvent: {}
[508.816433] (-) TimerEvent: {}
[508.916921] (-) TimerEvent: {}
[509.018151] (-) TimerEvent: {}
[509.118565] (-) TimerEvent: {}
[509.219361] (-) TimerEvent: {}
[509.320050] (-) TimerEvent: {}
[509.420779] (-) TimerEvent: {}
[509.521192] (-) TimerEvent: {}
[509.622186] (-) TimerEvent: {}
[509.722716] (-) TimerEvent: {}
[509.823642] (-) TimerEvent: {}
[509.924050] (-) TimerEvent: {}
[510.024970] (-) TimerEvent: {}
[510.126162] (-) TimerEvent: {}
[510.226594] (-) TimerEvent: {}
[510.327050] (-) TimerEvent: {}
[510.427445] (-) TimerEvent: {}
[510.527836] (-) TimerEvent: {}
[510.628798] (-) TimerEvent: {}
[510.729291] (-) TimerEvent: {}
[510.830172] (-) TimerEvent: {}
[510.930701] (-) TimerEvent: {}
[511.031270] (-) TimerEvent: {}
[511.131862] (-) TimerEvent: {}
[511.232820] (-) TimerEvent: {}
[511.333286] (-) TimerEvent: {}
[511.434375] (-) TimerEvent: {}
[511.535123] (-) TimerEvent: {}
[511.637222] (-) TimerEvent: {}
[511.739071] (-) TimerEvent: {}
[511.839420] (-) TimerEvent: {}
[511.939991] (-) TimerEvent: {}
[512.040419] (-) TimerEvent: {}
[512.141013] (-) TimerEvent: {}
[512.242168] (-) TimerEvent: {}
[512.342557] (-) TimerEvent: {}
[512.443028] (-) TimerEvent: {}
[512.543432] (-) TimerEvent: {}
[512.643975] (-) TimerEvent: {}
[512.744368] (-) TimerEvent: {}
[512.844796] (-) TimerEvent: {}
[512.946148] (-) TimerEvent: {}
[513.046539] (-) TimerEvent: {}
[513.146942] (-) TimerEvent: {}
[513.248838] (-) TimerEvent: {}
[513.354696] (-) TimerEvent: {}
[513.455152] (-) TimerEvent: {}
[513.555605] (-) TimerEvent: {}
[513.656087] (-) TimerEvent: {}
[513.756529] (-) TimerEvent: {}
[513.858379] (-) TimerEvent: {}
[513.958975] (-) TimerEvent: {}
[514.060373] (-) TimerEvent: {}
[514.161093] (-) TimerEvent: {}
[514.261485] (-) TimerEvent: {}
[514.362552] (-) TimerEvent: {}
[514.463010] (-) TimerEvent: {}
[514.563952] (-) TimerEvent: {}
[514.665202] (-) TimerEvent: {}
[514.765543] (-) TimerEvent: {}
[514.866181] (-) TimerEvent: {}
[514.966706] (-) TimerEvent: {}
[515.067489] (-) TimerEvent: {}
[515.168368] (-) TimerEvent: {}
[515.269607] (-) TimerEvent: {}
[515.350836] (ros1_bridge) StdoutLine: {'line': b'[ 61%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__ChannelFloat32__factories.cpp.o\x1b[0m\n'}
[515.370413] (-) TimerEvent: {}
[515.470914] (-) TimerEvent: {}
[515.571297] (-) TimerEvent: {}
[515.671674] (-) TimerEvent: {}
[515.772583] (-) TimerEvent: {}
[515.873055] (-) TimerEvent: {}
[515.974158] (-) TimerEvent: {}
[516.074732] (-) TimerEvent: {}
[516.175425] (-) TimerEvent: {}
[516.275900] (-) TimerEvent: {}
[516.376393] (-) TimerEvent: {}
[516.406393] (ros1_bridge) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__CompressedImage__factories.cpp.o\x1b[0m\n'}
[516.476569] (-) TimerEvent: {}
[516.577187] (-) TimerEvent: {}
[516.678201] (-) TimerEvent: {}
[516.778605] (-) TimerEvent: {}
[516.878980] (-) TimerEvent: {}
[516.979568] (-) TimerEvent: {}
[517.080477] (-) TimerEvent: {}
[517.180997] (-) TimerEvent: {}
[517.281465] (-) TimerEvent: {}
[517.382611] (-) TimerEvent: {}
[517.483162] (-) TimerEvent: {}
[517.583802] (-) TimerEvent: {}
[517.684448] (-) TimerEvent: {}
[517.784897] (-) TimerEvent: {}
[517.885638] (-) TimerEvent: {}
[517.986409] (-) TimerEvent: {}
[518.086909] (-) TimerEvent: {}
[518.187454] (-) TimerEvent: {}
[518.288039] (-) TimerEvent: {}
[518.388473] (-) TimerEvent: {}
[518.489097] (-) TimerEvent: {}
[518.589548] (-) TimerEvent: {}
[518.690188] (-) TimerEvent: {}
[518.790682] (-) TimerEvent: {}
[518.891966] (-) TimerEvent: {}
[518.992529] (-) TimerEvent: {}
[519.094213] (-) TimerEvent: {}
[519.194913] (-) TimerEvent: {}
[519.295796] (-) TimerEvent: {}
[519.396247] (-) TimerEvent: {}
[519.496866] (-) TimerEvent: {}
[519.597478] (-) TimerEvent: {}
[519.698322] (-) TimerEvent: {}
[519.798801] (-) TimerEvent: {}
[519.899752] (-) TimerEvent: {}
[520.000745] (-) TimerEvent: {}
[520.101643] (-) TimerEvent: {}
[520.202430] (-) TimerEvent: {}
[520.302876] (-) TimerEvent: {}
[520.403321] (-) TimerEvent: {}
[520.504067] (-) TimerEvent: {}
[520.604608] (-) TimerEvent: {}
[520.705234] (-) TimerEvent: {}
[520.806170] (-) TimerEvent: {}
[520.906521] (-) TimerEvent: {}
[521.007244] (-) TimerEvent: {}
[521.107750] (-) TimerEvent: {}
[521.208400] (-) TimerEvent: {}
[521.308890] (-) TimerEvent: {}
[521.410154] (-) TimerEvent: {}
[521.510586] (-) TimerEvent: {}
[521.611380] (-) TimerEvent: {}
[521.711962] (-) TimerEvent: {}
[521.812333] (-) TimerEvent: {}
[521.912829] (-) TimerEvent: {}
[522.014357] (-) TimerEvent: {}
[522.115608] (-) TimerEvent: {}
[522.216068] (-) TimerEvent: {}
[522.316871] (-) TimerEvent: {}
[522.418107] (-) TimerEvent: {}
[522.518712] (-) TimerEvent: {}
[522.619334] (-) TimerEvent: {}
[522.719837] (-) TimerEvent: {}
[522.820828] (-) TimerEvent: {}
[522.921384] (-) TimerEvent: {}
[523.022194] (-) TimerEvent: {}
[523.122748] (-) TimerEvent: {}
[523.223677] (-) TimerEvent: {}
[523.324219] (-) TimerEvent: {}
[523.425156] (-) TimerEvent: {}
[523.526205] (-) TimerEvent: {}
[523.626620] (-) TimerEvent: {}
[523.727068] (-) TimerEvent: {}
[523.827409] (-) TimerEvent: {}
[523.927848] (-) TimerEvent: {}
[524.028398] (-) TimerEvent: {}
[524.073261] (ros1_bridge) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__FluidPressure__factories.cpp.o\x1b[0m\n'}
[524.128857] (-) TimerEvent: {}
[524.229464] (-) TimerEvent: {}
[524.330233] (-) TimerEvent: {}
[524.430670] (-) TimerEvent: {}
[524.531284] (-) TimerEvent: {}
[524.631806] (-) TimerEvent: {}
[524.732221] (-) TimerEvent: {}
[524.832930] (-) TimerEvent: {}
[524.933354] (-) TimerEvent: {}
[525.034154] (-) TimerEvent: {}
[525.135348] (-) TimerEvent: {}
[525.235783] (-) TimerEvent: {}
[525.336406] (-) TimerEvent: {}
[525.394335] (ros1_bridge) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Illuminance__factories.cpp.o\x1b[0m\n'}
[525.436484] (-) TimerEvent: {}
[525.536893] (-) TimerEvent: {}
[525.637339] (-) TimerEvent: {}
[525.738154] (-) TimerEvent: {}
[525.838631] (-) TimerEvent: {}
[525.939625] (-) TimerEvent: {}
[526.040253] (-) TimerEvent: {}
[526.141240] (-) TimerEvent: {}
[526.242263] (-) TimerEvent: {}
[526.342641] (-) TimerEvent: {}
[526.443140] (-) TimerEvent: {}
[526.543549] (-) TimerEvent: {}
[526.644102] (-) TimerEvent: {}
[526.744745] (-) TimerEvent: {}
[526.845214] (-) TimerEvent: {}
[526.946158] (-) TimerEvent: {}
[527.046542] (-) TimerEvent: {}
[527.146955] (-) TimerEvent: {}
[527.247369] (-) TimerEvent: {}
[527.347715] (-) TimerEvent: {}
[527.448222] (-) TimerEvent: {}
[527.548694] (-) TimerEvent: {}
[527.649573] (-) TimerEvent: {}
[527.750665] (-) TimerEvent: {}
[527.851066] (-) TimerEvent: {}
[527.951588] (-) TimerEvent: {}
[528.052018] (-) TimerEvent: {}
[528.152452] (-) TimerEvent: {}
[528.253130] (-) TimerEvent: {}
[528.354178] (-) TimerEvent: {}
[528.454605] (-) TimerEvent: {}
[528.555213] (-) TimerEvent: {}
[528.655645] (-) TimerEvent: {}
[528.756083] (-) TimerEvent: {}
[528.857098] (-) TimerEvent: {}
[528.957508] (-) TimerEvent: {}
[529.058360] (-) TimerEvent: {}
[529.158824] (-) TimerEvent: {}
[529.259516] (-) TimerEvent: {}
[529.360110] (-) TimerEvent: {}
[529.460576] (-) TimerEvent: {}
[529.561589] (-) TimerEvent: {}
[529.662163] (-) TimerEvent: {}
[529.762613] (-) TimerEvent: {}
[529.863064] (-) TimerEvent: {}
[529.963601] (-) TimerEvent: {}
[530.064571] (-) TimerEvent: {}
[530.164971] (-) TimerEvent: {}
[530.265324] (-) TimerEvent: {}
[530.366304] (-) TimerEvent: {}
[530.466961] (-) TimerEvent: {}
[530.567651] (-) TimerEvent: {}
[530.668001] (-) TimerEvent: {}
[530.768374] (-) TimerEvent: {}
[530.869156] (-) TimerEvent: {}
[530.969533] (-) TimerEvent: {}
[531.070148] (-) TimerEvent: {}
[531.170692] (-) TimerEvent: {}
[531.271198] (-) TimerEvent: {}
[531.371713] (-) TimerEvent: {}
[531.472145] (-) TimerEvent: {}
[531.572550] (-) TimerEvent: {}
[531.673041] (-) TimerEvent: {}
[531.773472] (-) TimerEvent: {}
[531.874216] (-) TimerEvent: {}
[531.974821] (-) TimerEvent: {}
[532.075315] (-) TimerEvent: {}
[532.175865] (-) TimerEvent: {}
[532.276329] (-) TimerEvent: {}
[532.377141] (-) TimerEvent: {}
[532.454830] (ros1_bridge) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Image__factories.cpp.o\x1b[0m\n'}
[532.477242] (-) TimerEvent: {}
[532.578233] (-) TimerEvent: {}
[532.679087] (-) TimerEvent: {}
[532.779508] (-) TimerEvent: {}
[532.879871] (-) TimerEvent: {}
[532.980392] (-) TimerEvent: {}
[533.080928] (-) TimerEvent: {}
[533.182171] (-) TimerEvent: {}
[533.282726] (-) TimerEvent: {}
[533.383576] (-) TimerEvent: {}
[533.484088] (-) TimerEvent: {}
[533.585001] (-) TimerEvent: {}
[533.685588] (-) TimerEvent: {}
[533.786319] (-) TimerEvent: {}
[533.852486] (ros1_bridge) StdoutLine: {'line': b'[ 63%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Imu__factories.cpp.o\x1b[0m\n'}
[533.886416] (-) TimerEvent: {}
[533.987091] (-) TimerEvent: {}
[534.087551] (-) TimerEvent: {}
[534.187953] (-) TimerEvent: {}
[534.288353] (-) TimerEvent: {}
[534.388780] (-) TimerEvent: {}
[534.489204] (-) TimerEvent: {}
[534.590158] (-) TimerEvent: {}
[534.690572] (-) TimerEvent: {}
[534.791475] (-) TimerEvent: {}
[534.892116] (-) TimerEvent: {}
[534.993090] (-) TimerEvent: {}
[535.094194] (-) TimerEvent: {}
[535.195523] (-) TimerEvent: {}
[535.295999] (-) TimerEvent: {}
[535.396411] (-) TimerEvent: {}
[535.496882] (-) TimerEvent: {}
[535.597615] (-) TimerEvent: {}
[535.699498] (-) TimerEvent: {}
[535.800265] (-) TimerEvent: {}
[535.900959] (-) TimerEvent: {}
[536.001391] (-) TimerEvent: {}
[536.102516] (-) TimerEvent: {}
[536.203586] (-) TimerEvent: {}
[536.304134] (-) TimerEvent: {}
[536.405038] (-) TimerEvent: {}
[536.506129] (-) TimerEvent: {}
[536.606580] (-) TimerEvent: {}
[536.707026] (-) TimerEvent: {}
[536.807479] (-) TimerEvent: {}
[536.908225] (-) TimerEvent: {}
[537.008868] (-) TimerEvent: {}
[537.110193] (-) TimerEvent: {}
[537.210960] (-) TimerEvent: {}
[537.311426] (-) TimerEvent: {}
[537.411907] (-) TimerEvent: {}
[537.513012] (-) TimerEvent: {}
[537.613490] (-) TimerEvent: {}
[537.714265] (-) TimerEvent: {}
[537.815026] (-) TimerEvent: {}
[537.915574] (-) TimerEvent: {}
[538.016126] (-) TimerEvent: {}
[538.117077] (-) TimerEvent: {}
[538.218151] (-) TimerEvent: {}
[538.318630] (-) TimerEvent: {}
[538.419114] (-) TimerEvent: {}
[538.519590] (-) TimerEvent: {}
[538.620074] (-) TimerEvent: {}
[538.720456] (-) TimerEvent: {}
[538.821056] (-) TimerEvent: {}
[538.922150] (-) TimerEvent: {}
[539.022996] (-) TimerEvent: {}
[539.123430] (-) TimerEvent: {}
[539.223960] (-) TimerEvent: {}
[539.324435] (-) TimerEvent: {}
[539.424891] (-) TimerEvent: {}
[539.525350] (-) TimerEvent: {}
[539.626173] (-) TimerEvent: {}
[539.726621] (-) TimerEvent: {}
[539.827081] (-) TimerEvent: {}
[539.927453] (-) TimerEvent: {}
[540.027935] (-) TimerEvent: {}
[540.128852] (-) TimerEvent: {}
[540.229244] (-) TimerEvent: {}
[540.330206] (-) TimerEvent: {}
[540.431053] (-) TimerEvent: {}
[540.531504] (-) TimerEvent: {}
[540.632062] (-) TimerEvent: {}
[540.733019] (-) TimerEvent: {}
[540.834206] (-) TimerEvent: {}
[540.934915] (-) TimerEvent: {}
[541.035386] (-) TimerEvent: {}
[541.136019] (-) TimerEvent: {}
[541.237544] (-) TimerEvent: {}
[541.285763] (ros1_bridge) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JointState__factories.cpp.o\x1b[0m\n'}
[541.338267] (-) TimerEvent: {}
[541.438993] (-) TimerEvent: {}
[541.539468] (-) TimerEvent: {}
[541.639888] (-) TimerEvent: {}
[541.740988] (-) TimerEvent: {}
[541.842171] (-) TimerEvent: {}
[541.942573] (-) TimerEvent: {}
[542.043071] (-) TimerEvent: {}
[542.143458] (-) TimerEvent: {}
[542.180423] (ros1_bridge) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Joy__factories.cpp.o\x1b[0m\n'}
[542.243599] (-) TimerEvent: {}
[542.344012] (-) TimerEvent: {}
[542.444884] (-) TimerEvent: {}
[542.546148] (-) TimerEvent: {}
[542.646510] (-) TimerEvent: {}
[542.747303] (-) TimerEvent: {}
[542.847688] (-) TimerEvent: {}
[542.948112] (-) TimerEvent: {}
[543.048514] (-) TimerEvent: {}
[543.148960] (-) TimerEvent: {}
[543.250191] (-) TimerEvent: {}
[543.350591] (-) TimerEvent: {}
[543.451128] (-) TimerEvent: {}
[543.551922] (-) TimerEvent: {}
[543.652889] (-) TimerEvent: {}
[543.754193] (-) TimerEvent: {}
[543.854636] (-) TimerEvent: {}
[543.955112] (-) TimerEvent: {}
[544.055607] (-) TimerEvent: {}
[544.155939] (-) TimerEvent: {}
[544.256381] (-) TimerEvent: {}
[544.357413] (-) TimerEvent: {}
[544.458623] (-) TimerEvent: {}
[544.559013] (-) TimerEvent: {}
[544.659426] (-) TimerEvent: {}
[544.759826] (-) TimerEvent: {}
[544.860847] (-) TimerEvent: {}
[544.961265] (-) TimerEvent: {}
[545.062174] (-) TimerEvent: {}
[545.162637] (-) TimerEvent: {}
[545.263901] (-) TimerEvent: {}
[545.364748] (-) TimerEvent: {}
[545.465157] (-) TimerEvent: {}
[545.566152] (-) TimerEvent: {}
[545.666556] (-) TimerEvent: {}
[545.767060] (-) TimerEvent: {}
[545.868073] (-) TimerEvent: {}
[545.968527] (-) TimerEvent: {}
[546.068999] (-) TimerEvent: {}
[546.170177] (-) TimerEvent: {}
[546.270742] (-) TimerEvent: {}
[546.371171] (-) TimerEvent: {}
[546.471546] (-) TimerEvent: {}
[546.571954] (-) TimerEvent: {}
[546.672326] (-) TimerEvent: {}
[546.772824] (-) TimerEvent: {}
[546.873293] (-) TimerEvent: {}
[546.974152] (-) TimerEvent: {}
[547.074521] (-) TimerEvent: {}
[547.175186] (-) TimerEvent: {}
[547.275626] (-) TimerEvent: {}
[547.376886] (-) TimerEvent: {}
[547.478158] (-) TimerEvent: {}
[547.578878] (-) TimerEvent: {}
[547.679392] (-) TimerEvent: {}
[547.779993] (-) TimerEvent: {}
[547.880447] (-) TimerEvent: {}
[547.980865] (-) TimerEvent: {}
[548.081368] (-) TimerEvent: {}
[548.182428] (-) TimerEvent: {}
[548.282965] (-) TimerEvent: {}
[548.383550] (-) TimerEvent: {}
[548.483996] (-) TimerEvent: {}
[548.584447] (-) TimerEvent: {}
[548.685055] (-) TimerEvent: {}
[548.786162] (-) TimerEvent: {}
[548.886755] (-) TimerEvent: {}
[548.987141] (-) TimerEvent: {}
[549.087471] (-) TimerEvent: {}
[549.187796] (-) TimerEvent: {}
[549.288195] (-) TimerEvent: {}
[549.389369] (-) TimerEvent: {}
[549.490162] (-) TimerEvent: {}
[549.590786] (-) TimerEvent: {}
[549.691310] (-) TimerEvent: {}
[549.792272] (-) TimerEvent: {}
[549.892824] (-) TimerEvent: {}
[549.993225] (-) TimerEvent: {}
[550.094207] (-) TimerEvent: {}
[550.194863] (-) TimerEvent: {}
[550.196451] (ros1_bridge) StdoutLine: {'line': b'[ 64%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JoyFeedback__factories.cpp.o\x1b[0m\n'}
[550.294968] (-) TimerEvent: {}
[550.395628] (-) TimerEvent: {}
[550.496054] (-) TimerEvent: {}
[550.597014] (-) TimerEvent: {}
[550.698136] (-) TimerEvent: {}
[550.798687] (-) TimerEvent: {}
[550.899229] (-) TimerEvent: {}
[550.939690] (ros1_bridge) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JoyFeedbackArray__factories.cpp.o\x1b[0m\n'}
[550.999412] (-) TimerEvent: {}
[551.100031] (-) TimerEvent: {}
[551.200638] (-) TimerEvent: {}
[551.301122] (-) TimerEvent: {}
[551.402168] (-) TimerEvent: {}
[551.502600] (-) TimerEvent: {}
[551.603072] (-) TimerEvent: {}
[551.704049] (-) TimerEvent: {}
[551.804576] (-) TimerEvent: {}
[551.906225] (-) TimerEvent: {}
[552.006794] (-) TimerEvent: {}
[552.107387] (-) TimerEvent: {}
[552.207938] (-) TimerEvent: {}
[552.308449] (-) TimerEvent: {}
[552.408937] (-) TimerEvent: {}
[552.509480] (-) TimerEvent: {}
[552.610286] (-) TimerEvent: {}
[552.710801] (-) TimerEvent: {}
[552.811360] (-) TimerEvent: {}
[552.911879] (-) TimerEvent: {}
[553.012406] (-) TimerEvent: {}
[553.112899] (-) TimerEvent: {}
[553.213544] (-) TimerEvent: {}
[553.314136] (-) TimerEvent: {}
[553.414495] (-) TimerEvent: {}
[553.514956] (-) TimerEvent: {}
[553.615799] (-) TimerEvent: {}
[553.716844] (-) TimerEvent: {}
[553.817420] (-) TimerEvent: {}
[553.918188] (-) TimerEvent: {}
[554.019090] (-) TimerEvent: {}
[554.120180] (-) TimerEvent: {}
[554.220667] (-) TimerEvent: {}
[554.321323] (-) TimerEvent: {}
[554.422156] (-) TimerEvent: {}
[554.522611] (-) TimerEvent: {}
[554.623088] (-) TimerEvent: {}
[554.724347] (-) TimerEvent: {}
[554.824904] (-) TimerEvent: {}
[554.926167] (-) TimerEvent: {}
[555.026781] (-) TimerEvent: {}
[555.127818] (-) TimerEvent: {}
[555.228328] (-) TimerEvent: {}
[555.330173] (-) TimerEvent: {}
[555.430646] (-) TimerEvent: {}
[555.531142] (-) TimerEvent: {}
[555.631583] (-) TimerEvent: {}
[555.732021] (-) TimerEvent: {}
[555.832478] (-) TimerEvent: {}
[555.932851] (-) TimerEvent: {}
[556.033615] (-) TimerEvent: {}
[556.134241] (-) TimerEvent: {}
[556.234764] (-) TimerEvent: {}
[556.335314] (-) TimerEvent: {}
[556.435746] (-) TimerEvent: {}
[556.536206] (-) TimerEvent: {}
[556.637030] (-) TimerEvent: {}
[556.737631] (-) TimerEvent: {}
[556.838311] (-) TimerEvent: {}
[556.938791] (-) TimerEvent: {}
[557.039285] (-) TimerEvent: {}
[557.139758] (-) TimerEvent: {}
[557.240119] (-) TimerEvent: {}
[557.340568] (-) TimerEvent: {}
[557.441000] (-) TimerEvent: {}
[557.541428] (-) TimerEvent: {}
[557.642186] (-) TimerEvent: {}
[557.742715] (-) TimerEvent: {}
[557.843089] (-) TimerEvent: {}
[557.943767] (-) TimerEvent: {}
[558.044986] (-) TimerEvent: {}
[558.145549] (-) TimerEvent: {}
[558.246336] (-) TimerEvent: {}
[558.346790] (-) TimerEvent: {}
[558.447378] (-) TimerEvent: {}
[558.547875] (-) TimerEvent: {}
[558.648417] (-) TimerEvent: {}
[558.749456] (-) TimerEvent: {}
[558.832524] (ros1_bridge) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__LaserEcho__factories.cpp.o\x1b[0m\n'}
[558.850156] (-) TimerEvent: {}
[558.950972] (-) TimerEvent: {}
[559.051428] (-) TimerEvent: {}
[559.151879] (-) TimerEvent: {}
[559.252884] (-) TimerEvent: {}
[559.354279] (-) TimerEvent: {}
[559.418539] (ros1_bridge) StdoutLine: {'line': b'[ 65%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__LaserScan__factories.cpp.o\x1b[0m\n'}
[559.454427] (-) TimerEvent: {}
[559.555321] (-) TimerEvent: {}
[559.655887] (-) TimerEvent: {}
[559.756456] (-) TimerEvent: {}
[559.857405] (-) TimerEvent: {}
[559.958156] (-) TimerEvent: {}
[560.058754] (-) TimerEvent: {}
[560.159164] (-) TimerEvent: {}
[560.259551] (-) TimerEvent: {}
[560.361310] (-) TimerEvent: {}
[560.462563] (-) TimerEvent: {}
[560.563742] (-) TimerEvent: {}
[560.664886] (-) TimerEvent: {}
[560.765610] (-) TimerEvent: {}
[560.866146] (-) TimerEvent: {}
[560.966623] (-) TimerEvent: {}
[561.067115] (-) TimerEvent: {}
[561.167575] (-) TimerEvent: {}
[561.267988] (-) TimerEvent: {}
[561.368439] (-) TimerEvent: {}
[561.469299] (-) TimerEvent: {}
[561.570145] (-) TimerEvent: {}
[561.670589] (-) TimerEvent: {}
[561.771395] (-) TimerEvent: {}
[561.871848] (-) TimerEvent: {}
[561.972419] (-) TimerEvent: {}
[562.073140] (-) TimerEvent: {}
[562.174159] (-) TimerEvent: {}
[562.274521] (-) TimerEvent: {}
[562.375000] (-) TimerEvent: {}
[562.475483] (-) TimerEvent: {}
[562.575880] (-) TimerEvent: {}
[562.676370] (-) TimerEvent: {}
[562.776838] (-) TimerEvent: {}
[562.877566] (-) TimerEvent: {}
[562.978203] (-) TimerEvent: {}
[563.078680] (-) TimerEvent: {}
[563.179169] (-) TimerEvent: {}
[563.279666] (-) TimerEvent: {}
[563.380301] (-) TimerEvent: {}
[563.481206] (-) TimerEvent: {}
[563.582153] (-) TimerEvent: {}
[563.682660] (-) TimerEvent: {}
[563.783251] (-) TimerEvent: {}
[563.883780] (-) TimerEvent: {}
[563.984609] (-) TimerEvent: {}
[564.085069] (-) TimerEvent: {}
[564.186218] (-) TimerEvent: {}
[564.286906] (-) TimerEvent: {}
[564.387891] (-) TimerEvent: {}
[564.488905] (-) TimerEvent: {}
[564.589490] (-) TimerEvent: {}
[564.690155] (-) TimerEvent: {}
[564.790598] (-) TimerEvent: {}
[564.891057] (-) TimerEvent: {}
[564.991542] (-) TimerEvent: {}
[565.091991] (-) TimerEvent: {}
[565.192920] (-) TimerEvent: {}
[565.293344] (-) TimerEvent: {}
[565.394317] (-) TimerEvent: {}
[565.494737] (-) TimerEvent: {}
[565.595433] (-) TimerEvent: {}
[565.696065] (-) TimerEvent: {}
[565.796515] (-) TimerEvent: {}
[565.896897] (-) TimerEvent: {}
[565.998240] (-) TimerEvent: {}
[566.098660] (-) TimerEvent: {}
[566.199272] (-) TimerEvent: {}
[566.299668] (-) TimerEvent: {}
[566.400111] (-) TimerEvent: {}
[566.500533] (-) TimerEvent: {}
[566.601015] (-) TimerEvent: {}
[566.702247] (-) TimerEvent: {}
[566.802665] (-) TimerEvent: {}
[566.903272] (-) TimerEvent: {}
[567.004846] (-) TimerEvent: {}
[567.105358] (-) TimerEvent: {}
[567.206308] (-) TimerEvent: {}
[567.306838] (-) TimerEvent: {}
[567.407788] (-) TimerEvent: {}
[567.461578] (ros1_bridge) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MagneticField__factories.cpp.o\x1b[0m\n'}
[567.507921] (-) TimerEvent: {}
[567.608410] (-) TimerEvent: {}
[567.691775] (ros1_bridge) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MultiDOFJointState__factories.cpp.o\x1b[0m\n'}
[567.708491] (-) TimerEvent: {}
[567.809423] (-) TimerEvent: {}
[567.910149] (-) TimerEvent: {}
[568.010575] (-) TimerEvent: {}
[568.111216] (-) TimerEvent: {}
[568.211664] (-) TimerEvent: {}
[568.312084] (-) TimerEvent: {}
[568.412511] (-) TimerEvent: {}
[568.512927] (-) TimerEvent: {}
[568.613373] (-) TimerEvent: {}
[568.714207] (-) TimerEvent: {}
[568.815123] (-) TimerEvent: {}
[568.915647] (-) TimerEvent: {}
[569.016118] (-) TimerEvent: {}
[569.117122] (-) TimerEvent: {}
[569.218171] (-) TimerEvent: {}
[569.318629] (-) TimerEvent: {}
[569.419167] (-) TimerEvent: {}
[569.519662] (-) TimerEvent: {}
[569.620127] (-) TimerEvent: {}
[569.720614] (-) TimerEvent: {}
[569.821072] (-) TimerEvent: {}
[569.921477] (-) TimerEvent: {}
[570.022167] (-) TimerEvent: {}
[570.122623] (-) TimerEvent: {}
[570.223167] (-) TimerEvent: {}
[570.323959] (-) TimerEvent: {}
[570.424974] (-) TimerEvent: {}
[570.525410] (-) TimerEvent: {}
[570.626179] (-) TimerEvent: {}
[570.726955] (-) TimerEvent: {}
[570.827569] (-) TimerEvent: {}
[570.928162] (-) TimerEvent: {}
[571.029022] (-) TimerEvent: {}
[571.130949] (-) TimerEvent: {}
[571.231602] (-) TimerEvent: {}
[571.332191] (-) TimerEvent: {}
[571.433288] (-) TimerEvent: {}
[571.534177] (-) TimerEvent: {}
[571.634754] (-) TimerEvent: {}
[571.735291] (-) TimerEvent: {}
[571.835716] (-) TimerEvent: {}
[571.936183] (-) TimerEvent: {}
[572.036647] (-) TimerEvent: {}
[572.137109] (-) TimerEvent: {}
[572.238174] (-) TimerEvent: {}
[572.338616] (-) TimerEvent: {}
[572.439031] (-) TimerEvent: {}
[572.539514] (-) TimerEvent: {}
[572.640080] (-) TimerEvent: {}
[572.741091] (-) TimerEvent: {}
[572.842186] (-) TimerEvent: {}
[572.942533] (-) TimerEvent: {}
[573.042961] (-) TimerEvent: {}
[573.143358] (-) TimerEvent: {}
[573.243833] (-) TimerEvent: {}
[573.344259] (-) TimerEvent: {}
[573.444923] (-) TimerEvent: {}
[573.546181] (-) TimerEvent: {}
[573.646842] (-) TimerEvent: {}
[573.747286] (-) TimerEvent: {}
[573.847777] (-) TimerEvent: {}
[573.948807] (-) TimerEvent: {}
[574.049258] (-) TimerEvent: {}
[574.150183] (-) TimerEvent: {}
[574.250631] (-) TimerEvent: {}
[574.351163] (-) TimerEvent: {}
[574.451975] (-) TimerEvent: {}
[574.552451] (-) TimerEvent: {}
[574.652956] (-) TimerEvent: {}
[574.753459] (-) TimerEvent: {}
[574.854161] (-) TimerEvent: {}
[574.954674] (-) TimerEvent: {}
[575.055188] (-) TimerEvent: {}
[575.155629] (-) TimerEvent: {}
[575.256077] (-) TimerEvent: {}
[575.356870] (-) TimerEvent: {}
[575.458331] (-) TimerEvent: {}
[575.559260] (-) TimerEvent: {}
[575.659615] (-) TimerEvent: {}
[575.760018] (-) TimerEvent: {}
[575.860542] (-) TimerEvent: {}
[575.960983] (-) TimerEvent: {}
[576.061427] (-) TimerEvent: {}
[576.162157] (-) TimerEvent: {}
[576.228935] (ros1_bridge) StdoutLine: {'line': b'[ 66%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MultiEchoLaserScan__factories.cpp.o\x1b[0m\n'}
[576.262267] (-) TimerEvent: {}
[576.363102] (-) TimerEvent: {}
[576.463740] (-) TimerEvent: {}
[576.544711] (ros1_bridge) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__NavSatFix__factories.cpp.o\x1b[0m\n'}
[576.563881] (-) TimerEvent: {}
[576.664792] (-) TimerEvent: {}
[576.765229] (-) TimerEvent: {}
[576.866215] (-) TimerEvent: {}
[576.966645] (-) TimerEvent: {}
[577.067076] (-) TimerEvent: {}
[577.167524] (-) TimerEvent: {}
[577.267969] (-) TimerEvent: {}
[577.368442] (-) TimerEvent: {}
[577.469236] (-) TimerEvent: {}
[577.570154] (-) TimerEvent: {}
[577.670618] (-) TimerEvent: {}
[577.771224] (-) TimerEvent: {}
[577.871806] (-) TimerEvent: {}
[577.972238] (-) TimerEvent: {}
[578.072596] (-) TimerEvent: {}
[578.173052] (-) TimerEvent: {}
[578.273569] (-) TimerEvent: {}
[578.374172] (-) TimerEvent: {}
[578.474621] (-) TimerEvent: {}
[578.575150] (-) TimerEvent: {}
[578.675597] (-) TimerEvent: {}
[578.776191] (-) TimerEvent: {}
[578.876761] (-) TimerEvent: {}
[578.978213] (-) TimerEvent: {}
[579.078975] (-) TimerEvent: {}
[579.179478] (-) TimerEvent: {}
[579.280021] (-) TimerEvent: {}
[579.381098] (-) TimerEvent: {}
[579.482200] (-) TimerEvent: {}
[579.582685] (-) TimerEvent: {}
[579.683418] (-) TimerEvent: {}
[579.783917] (-) TimerEvent: {}
[579.885345] (-) TimerEvent: {}
[579.986204] (-) TimerEvent: {}
[580.086976] (-) TimerEvent: {}
[580.187601] (-) TimerEvent: {}
[580.288137] (-) TimerEvent: {}
[580.388493] (-) TimerEvent: {}
[580.488948] (-) TimerEvent: {}
[580.590167] (-) TimerEvent: {}
[580.690991] (-) TimerEvent: {}
[580.791483] (-) TimerEvent: {}
[580.892112] (-) TimerEvent: {}
[580.992682] (-) TimerEvent: {}
[581.094216] (-) TimerEvent: {}
[581.194697] (-) TimerEvent: {}
[581.295682] (-) TimerEvent: {}
[581.396180] (-) TimerEvent: {}
[581.497134] (-) TimerEvent: {}
[581.598334] (-) TimerEvent: {}
[581.699001] (-) TimerEvent: {}
[581.799473] (-) TimerEvent: {}
[581.899986] (-) TimerEvent: {}
[582.000447] (-) TimerEvent: {}
[582.101437] (-) TimerEvent: {}
[582.202167] (-) TimerEvent: {}
[582.302694] (-) TimerEvent: {}
[582.403137] (-) TimerEvent: {}
[582.503615] (-) TimerEvent: {}
[582.604096] (-) TimerEvent: {}
[582.705228] (-) TimerEvent: {}
[582.806168] (-) TimerEvent: {}
[582.906618] (-) TimerEvent: {}
[583.007332] (-) TimerEvent: {}
[583.107805] (-) TimerEvent: {}
[583.208858] (-) TimerEvent: {}
[583.309588] (-) TimerEvent: {}
[583.410546] (-) TimerEvent: {}
[583.510943] (-) TimerEvent: {}
[583.611449] (-) TimerEvent: {}
[583.711861] (-) TimerEvent: {}
[583.812233] (-) TimerEvent: {}
[583.912631] (-) TimerEvent: {}
[584.013128] (-) TimerEvent: {}
[584.113512] (-) TimerEvent: {}
[584.214166] (-) TimerEvent: {}
[584.315223] (-) TimerEvent: {}
[584.416043] (-) TimerEvent: {}
[584.516388] (-) TimerEvent: {}
[584.616923] (-) TimerEvent: {}
[584.718179] (-) TimerEvent: {}
[584.818665] (-) TimerEvent: {}
[584.919543] (-) TimerEvent: {}
[585.020127] (-) TimerEvent: {}
[585.046483] (ros1_bridge) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__NavSatStatus__factories.cpp.o\x1b[0m\n'}
[585.120194] (-) TimerEvent: {}
[585.151832] (ros1_bridge) StdoutLine: {'line': b'[ 67%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointCloud__factories.cpp.o\x1b[0m\n'}
[585.220843] (-) TimerEvent: {}
[585.321558] (-) TimerEvent: {}
[585.422496] (-) TimerEvent: {}
[585.522850] (-) TimerEvent: {}
[585.623475] (-) TimerEvent: {}
[585.724121] (-) TimerEvent: {}
[585.824538] (-) TimerEvent: {}
[585.924917] (-) TimerEvent: {}
[586.026143] (-) TimerEvent: {}
[586.126570] (-) TimerEvent: {}
[586.227073] (-) TimerEvent: {}
[586.327981] (-) TimerEvent: {}
[586.428971] (-) TimerEvent: {}
[586.529588] (-) TimerEvent: {}
[586.630542] (-) TimerEvent: {}
[586.731016] (-) TimerEvent: {}
[586.831760] (-) TimerEvent: {}
[586.932216] (-) TimerEvent: {}
[587.032704] (-) TimerEvent: {}
[587.133490] (-) TimerEvent: {}
[587.234168] (-) TimerEvent: {}
[587.334630] (-) TimerEvent: {}
[587.435130] (-) TimerEvent: {}
[587.535565] (-) TimerEvent: {}
[587.635992] (-) TimerEvent: {}
[587.737091] (-) TimerEvent: {}
[587.837555] (-) TimerEvent: {}
[587.938450] (-) TimerEvent: {}
[588.038850] (-) TimerEvent: {}
[588.139282] (-) TimerEvent: {}
[588.239661] (-) TimerEvent: {}
[588.340096] (-) TimerEvent: {}
[588.441100] (-) TimerEvent: {}
[588.542162] (-) TimerEvent: {}
[588.642570] (-) TimerEvent: {}
[588.743282] (-) TimerEvent: {}
[588.843698] (-) TimerEvent: {}
[588.944125] (-) TimerEvent: {}
[589.044593] (-) TimerEvent: {}
[589.144982] (-) TimerEvent: {}
[589.245385] (-) TimerEvent: {}
[589.346312] (-) TimerEvent: {}
[589.446745] (-) TimerEvent: {}
[589.547119] (-) TimerEvent: {}
[589.647632] (-) TimerEvent: {}
[589.748152] (-) TimerEvent: {}
[589.848605] (-) TimerEvent: {}
[589.949503] (-) TimerEvent: {}
[590.050226] (-) TimerEvent: {}
[590.151087] (-) TimerEvent: {}
[590.251689] (-) TimerEvent: {}
[590.352202] (-) TimerEvent: {}
[590.452935] (-) TimerEvent: {}
[590.553416] (-) TimerEvent: {}
[590.654507] (-) TimerEvent: {}
[590.755269] (-) TimerEvent: {}
[590.855757] (-) TimerEvent: {}
[590.956518] (-) TimerEvent: {}
[591.057328] (-) TimerEvent: {}
[591.158163] (-) TimerEvent: {}
[591.258637] (-) TimerEvent: {}
[591.359109] (-) TimerEvent: {}
[591.459544] (-) TimerEvent: {}
[591.560450] (-) TimerEvent: {}
[591.661456] (-) TimerEvent: {}
[591.762622] (-) TimerEvent: {}
[591.863186] (-) TimerEvent: {}
[591.963564] (-) TimerEvent: {}
[592.063996] (-) TimerEvent: {}
[592.164423] (-) TimerEvent: {}
[592.264869] (-) TimerEvent: {}
[592.365277] (-) TimerEvent: {}
[592.466186] (-) TimerEvent: {}
[592.566988] (-) TimerEvent: {}
[592.667429] (-) TimerEvent: {}
[592.767925] (-) TimerEvent: {}
[592.868436] (-) TimerEvent: {}
[592.968865] (-) TimerEvent: {}
[593.069292] (-) TimerEvent: {}
[593.170329] (-) TimerEvent: {}
[593.270756] (-) TimerEvent: {}
[593.371158] (-) TimerEvent: {}
[593.471598] (-) TimerEvent: {}
[593.571929] (-) TimerEvent: {}
[593.672810] (-) TimerEvent: {}
[593.773308] (-) TimerEvent: {}
[593.791191] (ros1_bridge) StdoutLine: {'line': b'[ 68%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointCloud2__factories.cpp.o\x1b[0m\n'}
[593.873393] (-) TimerEvent: {}
[593.974214] (-) TimerEvent: {}
[594.075111] (-) TimerEvent: {}
[594.175886] (-) TimerEvent: {}
[594.277121] (-) TimerEvent: {}
[594.378392] (-) TimerEvent: {}
[594.479516] (-) TimerEvent: {}
[594.579962] (-) TimerEvent: {}
[594.680455] (-) TimerEvent: {}
[594.781216] (-) TimerEvent: {}
[594.882733] (-) TimerEvent: {}
[594.984262] (-) TimerEvent: {}
[595.087070] (-) TimerEvent: {}
[595.187472] (-) TimerEvent: {}
[595.287900] (-) TimerEvent: {}
[595.388352] (-) TimerEvent: {}
[595.489206] (-) TimerEvent: {}
[595.590382] (-) TimerEvent: {}
[595.691621] (-) TimerEvent: {}
[595.792105] (-) TimerEvent: {}
[595.809121] (ros1_bridge) StdoutLine: {'line': b'[ 68%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointField__factories.cpp.o\x1b[0m\n'}
[595.892347] (-) TimerEvent: {}
[595.992689] (-) TimerEvent: {}
[596.093101] (-) TimerEvent: {}
[596.193420] (-) TimerEvent: {}
[596.294219] (-) TimerEvent: {}
[596.394589] (-) TimerEvent: {}
[596.494945] (-) TimerEvent: {}
[596.595766] (-) TimerEvent: {}
[596.696230] (-) TimerEvent: {}
[596.797187] (-) TimerEvent: {}
[596.898162] (-) TimerEvent: {}
[596.998552] (-) TimerEvent: {}
[597.098991] (-) TimerEvent: {}
[597.199376] (-) TimerEvent: {}
[597.299788] (-) TimerEvent: {}
[597.400149] (-) TimerEvent: {}
[597.500540] (-) TimerEvent: {}
[597.600971] (-) TimerEvent: {}
[597.702166] (-) TimerEvent: {}
[597.802565] (-) TimerEvent: {}
[597.903037] (-) TimerEvent: {}
[598.003435] (-) TimerEvent: {}
[598.103808] (-) TimerEvent: {}
[598.204205] (-) TimerEvent: {}
[598.305144] (-) TimerEvent: {}
[598.405527] (-) TimerEvent: {}
[598.506172] (-) TimerEvent: {}
[598.606577] (-) TimerEvent: {}
[598.707492] (-) TimerEvent: {}
[598.807880] (-) TimerEvent: {}
[598.908210] (-) TimerEvent: {}
[599.008950] (-) TimerEvent: {}
[599.109348] (-) TimerEvent: {}
[599.210298] (-) TimerEvent: {}
[599.310667] (-) TimerEvent: {}
[599.411147] (-) TimerEvent: {}
[599.511539] (-) TimerEvent: {}
[599.611913] (-) TimerEvent: {}
[599.712308] (-) TimerEvent: {}
[599.812673] (-) TimerEvent: {}
[599.913065] (-) TimerEvent: {}
[600.013471] (-) TimerEvent: {}
[600.114149] (-) TimerEvent: {}
[600.214519] (-) TimerEvent: {}
[600.314891] (-) TimerEvent: {}
[600.415238] (-) TimerEvent: {}
[600.516219] (-) TimerEvent: {}
[600.617168] (-) TimerEvent: {}
[600.718252] (-) TimerEvent: {}
[600.818631] (-) TimerEvent: {}
[600.919023] (-) TimerEvent: {}
[601.019371] (-) TimerEvent: {}
[601.119904] (-) TimerEvent: {}
[601.220252] (-) TimerEvent: {}
[601.320617] (-) TimerEvent: {}
[601.420979] (-) TimerEvent: {}
[601.521340] (-) TimerEvent: {}
[601.622203] (-) TimerEvent: {}
[601.722556] (-) TimerEvent: {}
[601.822953] (-) TimerEvent: {}
[601.914194] (ros1_bridge) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Range__factories.cpp.o\x1b[0m\n'}
[601.923011] (-) TimerEvent: {}
[602.023402] (-) TimerEvent: {}
[602.123815] (-) TimerEvent: {}
[602.224834] (-) TimerEvent: {}
[602.325205] (-) TimerEvent: {}
[602.426196] (-) TimerEvent: {}
[602.526579] (-) TimerEvent: {}
[602.631186] (-) TimerEvent: {}
[602.731572] (-) TimerEvent: {}
[602.831922] (-) TimerEvent: {}
[602.932862] (-) TimerEvent: {}
[603.033206] (-) TimerEvent: {}
[603.134143] (-) TimerEvent: {}
[603.234492] (-) TimerEvent: {}
[603.334936] (-) TimerEvent: {}
[603.435409] (-) TimerEvent: {}
[603.535774] (-) TimerEvent: {}
[603.636129] (-) TimerEvent: {}
[603.737089] (-) TimerEvent: {}
[603.838149] (-) TimerEvent: {}
[603.938505] (-) TimerEvent: {}
[604.038887] (-) TimerEvent: {}
[604.139294] (-) TimerEvent: {}
[604.153702] (ros1_bridge) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__RegionOfInterest__factories.cpp.o\x1b[0m\n'}
[604.239534] (-) TimerEvent: {}
[604.339999] (-) TimerEvent: {}
[604.440774] (-) TimerEvent: {}
[604.541152] (-) TimerEvent: {}
[604.642170] (-) TimerEvent: {}
[604.742512] (-) TimerEvent: {}
[604.843346] (-) TimerEvent: {}
[604.943987] (-) TimerEvent: {}
[605.044771] (-) TimerEvent: {}
[605.145277] (-) TimerEvent: {}
[605.246164] (-) TimerEvent: {}
[605.346552] (-) TimerEvent: {}
[605.446947] (-) TimerEvent: {}
[605.547427] (-) TimerEvent: {}
[605.648073] (-) TimerEvent: {}
[605.749029] (-) TimerEvent: {}
[605.850156] (-) TimerEvent: {}
[605.951380] (-) TimerEvent: {}
[606.051772] (-) TimerEvent: {}
[606.152270] (-) TimerEvent: {}
[606.252687] (-) TimerEvent: {}
[606.353068] (-) TimerEvent: {}
[606.454178] (-) TimerEvent: {}
[606.554735] (-) TimerEvent: {}
[606.655182] (-) TimerEvent: {}
[606.756197] (-) TimerEvent: {}
[606.856607] (-) TimerEvent: {}
[606.957376] (-) TimerEvent: {}
[607.058178] (-) TimerEvent: {}
[607.158664] (-) TimerEvent: {}
[607.259564] (-) TimerEvent: {}
[607.360180] (-) TimerEvent: {}
[607.460884] (-) TimerEvent: {}
[607.561634] (-) TimerEvent: {}
[607.662145] (-) TimerEvent: {}
[607.773063] (-) TimerEvent: {}
[607.873488] (-) TimerEvent: {}
[607.974156] (-) TimerEvent: {}
[608.074575] (-) TimerEvent: {}
[608.175000] (-) TimerEvent: {}
[608.275418] (-) TimerEvent: {}
[608.375814] (-) TimerEvent: {}
[608.476230] (-) TimerEvent: {}
[608.577237] (-) TimerEvent: {}
[608.678152] (-) TimerEvent: {}
[608.778618] (-) TimerEvent: {}
[608.879038] (-) TimerEvent: {}
[608.979412] (-) TimerEvent: {}
[609.079786] (-) TimerEvent: {}
[609.180197] (-) TimerEvent: {}
[609.281128] (-) TimerEvent: {}
[609.382152] (-) TimerEvent: {}
[609.482647] (-) TimerEvent: {}
[609.583142] (-) TimerEvent: {}
[609.683531] (-) TimerEvent: {}
[609.783883] (-) TimerEvent: {}
[609.885026] (-) TimerEvent: {}
[609.986175] (-) TimerEvent: {}
[610.086737] (-) TimerEvent: {}
[610.188992] (-) TimerEvent: {}
[610.290199] (-) TimerEvent: {}
[610.390658] (-) TimerEvent: {}
[610.491103] (-) TimerEvent: {}
[610.591709] (-) TimerEvent: {}
[610.692146] (-) TimerEvent: {}
[610.705932] (ros1_bridge) StdoutLine: {'line': b'[ 69%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__RelativeHumidity__factories.cpp.o\x1b[0m\n'}
[610.792259] (-) TimerEvent: {}
[610.892874] (-) TimerEvent: {}
[610.993300] (-) TimerEvent: {}
[611.094257] (-) TimerEvent: {}
[611.194636] (-) TimerEvent: {}
[611.294974] (-) TimerEvent: {}
[611.395478] (-) TimerEvent: {}
[611.495852] (-) TimerEvent: {}
[611.596249] (-) TimerEvent: {}
[611.696822] (-) TimerEvent: {}
[611.797405] (-) TimerEvent: {}
[611.898288] (-) TimerEvent: {}
[611.998750] (-) TimerEvent: {}
[612.099435] (-) TimerEvent: {}
[612.199959] (-) TimerEvent: {}
[612.300327] (-) TimerEvent: {}
[612.400773] (-) TimerEvent: {}
[612.501136] (-) TimerEvent: {}
[612.602192] (-) TimerEvent: {}
[612.702995] (-) TimerEvent: {}
[612.758970] (ros1_bridge) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Temperature__factories.cpp.o\x1b[0m\n'}
[612.803100] (-) TimerEvent: {}
[612.903772] (-) TimerEvent: {}
[613.004275] (-) TimerEvent: {}
[613.104715] (-) TimerEvent: {}
[613.205439] (-) TimerEvent: {}
[613.306323] (-) TimerEvent: {}
[613.406773] (-) TimerEvent: {}
[613.507177] (-) TimerEvent: {}
[613.607681] (-) TimerEvent: {}
[613.708998] (-) TimerEvent: {}
[613.810162] (-) TimerEvent: {}
[613.910596] (-) TimerEvent: {}
[614.011205] (-) TimerEvent: {}
[614.112166] (-) TimerEvent: {}
[614.212641] (-) TimerEvent: {}
[614.313105] (-) TimerEvent: {}
[614.413568] (-) TimerEvent: {}
[614.514595] (-) TimerEvent: {}
[614.615583] (-) TimerEvent: {}
[614.716244] (-) TimerEvent: {}
[614.816646] (-) TimerEvent: {}
[614.917062] (-) TimerEvent: {}
[615.018175] (-) TimerEvent: {}
[615.118678] (-) TimerEvent: {}
[615.219467] (-) TimerEvent: {}
[615.320039] (-) TimerEvent: {}
[615.420965] (-) TimerEvent: {}
[615.522161] (-) TimerEvent: {}
[615.622635] (-) TimerEvent: {}
[615.723468] (-) TimerEvent: {}
[615.824188] (-) TimerEvent: {}
[615.925119] (-) TimerEvent: {}
[616.026135] (-) TimerEvent: {}
[616.126514] (-) TimerEvent: {}
[616.226975] (-) TimerEvent: {}
[616.327415] (-) TimerEvent: {}
[616.427981] (-) TimerEvent: {}
[616.529013] (-) TimerEvent: {}
[616.629400] (-) TimerEvent: {}
[616.730151] (-) TimerEvent: {}
[616.830590] (-) TimerEvent: {}
[616.931401] (-) TimerEvent: {}
[617.031838] (-) TimerEvent: {}
[617.132200] (-) TimerEvent: {}
[617.232567] (-) TimerEvent: {}
[617.333550] (-) TimerEvent: {}
[617.434549] (-) TimerEvent: {}
[617.535485] (-) TimerEvent: {}
[617.636093] (-) TimerEvent: {}
[617.736818] (-) TimerEvent: {}
[617.837334] (-) TimerEvent: {}
[617.938154] (-) TimerEvent: {}
[618.038625] (-) TimerEvent: {}
[618.138996] (-) TimerEvent: {}
[618.239557] (-) TimerEvent: {}
[618.339948] (-) TimerEvent: {}
[618.440404] (-) TimerEvent: {}
[618.540966] (-) TimerEvent: {}
[618.641634] (-) TimerEvent: {}
[618.742878] (-) TimerEvent: {}
[618.843285] (-) TimerEvent: {}
[618.943588] (-) TimerEvent: {}
[619.044512] (-) TimerEvent: {}
[619.145309] (-) TimerEvent: {}
[619.219975] (ros1_bridge) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__TimeReference__factories.cpp.o\x1b[0m\n'}
[619.245511] (-) TimerEvent: {}
[619.346269] (-) TimerEvent: {}
[619.446718] (-) TimerEvent: {}
[619.547351] (-) TimerEvent: {}
[619.647813] (-) TimerEvent: {}
[619.748559] (-) TimerEvent: {}
[619.850186] (-) TimerEvent: {}
[619.950716] (-) TimerEvent: {}
[620.051143] (-) TimerEvent: {}
[620.151529] (-) TimerEvent: {}
[620.251963] (-) TimerEvent: {}
[620.352358] (-) TimerEvent: {}
[620.452925] (-) TimerEvent: {}
[620.554183] (-) TimerEvent: {}
[620.654652] (-) TimerEvent: {}
[620.755070] (-) TimerEvent: {}
[620.855544] (-) TimerEvent: {}
[620.955993] (-) TimerEvent: {}
[621.056465] (-) TimerEvent: {}
[621.156903] (-) TimerEvent: {}
[621.257568] (-) TimerEvent: {}
[621.358166] (-) TimerEvent: {}
[621.458866] (-) TimerEvent: {}
[621.560055] (-) TimerEvent: {}
[621.660496] (-) TimerEvent: {}
[621.761260] (-) TimerEvent: {}
[621.861591] (-) TimerEvent: {}
[621.962237] (-) TimerEvent: {}
[622.062869] (-) TimerEvent: {}
[622.163808] (-) TimerEvent: {}
[622.265211] (-) TimerEvent: {}
[622.366173] (-) TimerEvent: {}
[622.466631] (-) TimerEvent: {}
[622.567034] (-) TimerEvent: {}
[622.667419] (-) TimerEvent: {}
[622.768141] (-) TimerEvent: {}
[622.869078] (-) TimerEvent: {}
[622.970135] (-) TimerEvent: {}
[623.070445] (-) TimerEvent: {}
[623.170946] (-) TimerEvent: {}
[623.271472] (-) TimerEvent: {}
[623.371773] (-) TimerEvent: {}
[623.472317] (-) TimerEvent: {}
[623.572826] (-) TimerEvent: {}
[623.673587] (-) TimerEvent: {}
[623.774760] (-) TimerEvent: {}
[623.875480] (-) TimerEvent: {}
[623.975895] (-) TimerEvent: {}
[624.076416] (-) TimerEvent: {}
[624.177029] (-) TimerEvent: {}
[624.278209] (-) TimerEvent: {}
[624.378586] (-) TimerEvent: {}
[624.478924] (-) TimerEvent: {}
[624.579581] (-) TimerEvent: {}
[624.680439] (-) TimerEvent: {}
[624.781667] (-) TimerEvent: {}
[624.882286] (-) TimerEvent: {}
[624.982760] (-) TimerEvent: {}
[625.083003] (-) TimerEvent: {}
[625.183281] (-) TimerEvent: {}
[625.283648] (-) TimerEvent: {}
[625.383934] (-) TimerEvent: {}
[625.484213] (-) TimerEvent: {}
[625.584447] (-) TimerEvent: {}
[625.685278] (-) TimerEvent: {}
[625.786496] (-) TimerEvent: {}
[625.887190] (-) TimerEvent: {}
[625.987483] (-) TimerEvent: {}
[626.062614] (ros1_bridge) StdoutLine: {'line': b'[ 70%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__srv__SetCameraInfo__factories.cpp.o\x1b[0m\n'}
[626.088132] (-) TimerEvent: {}
[626.189144] (-) TimerEvent: {}
[626.290253] (-) TimerEvent: {}
[626.391142] (-) TimerEvent: {}
[626.491546] (-) TimerEvent: {}
[626.591981] (-) TimerEvent: {}
[626.692429] (-) TimerEvent: {}
[626.793034] (-) TimerEvent: {}
[626.894178] (-) TimerEvent: {}
[626.994665] (-) TimerEvent: {}
[627.095159] (-) TimerEvent: {}
[627.141547] (ros1_bridge) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs_factories.cpp.o\x1b[0m\n'}
[627.195299] (-) TimerEvent: {}
[627.295856] (-) TimerEvent: {}
[627.396345] (-) TimerEvent: {}
[627.497060] (-) TimerEvent: {}
[627.597476] (-) TimerEvent: {}
[627.698373] (-) TimerEvent: {}
[627.799081] (-) TimerEvent: {}
[627.899489] (-) TimerEvent: {}
[627.999991] (-) TimerEvent: {}
[628.100994] (-) TimerEvent: {}
[628.202166] (-) TimerEvent: {}
[628.302678] (-) TimerEvent: {}
[628.403178] (-) TimerEvent: {}
[628.503701] (-) TimerEvent: {}
[628.604184] (-) TimerEvent: {}
[628.704649] (-) TimerEvent: {}
[628.806167] (-) TimerEvent: {}
[628.906636] (-) TimerEvent: {}
[629.007303] (-) TimerEvent: {}
[629.107692] (-) TimerEvent: {}
[629.208157] (-) TimerEvent: {}
[629.308988] (-) TimerEvent: {}
[629.410158] (-) TimerEvent: {}
[629.510622] (-) TimerEvent: {}
[629.611081] (-) TimerEvent: {}
[629.711549] (-) TimerEvent: {}
[629.812098] (-) TimerEvent: {}
[629.912869] (-) TimerEvent: {}
[630.013310] (-) TimerEvent: {}
[630.114154] (-) TimerEvent: {}
[630.214600] (-) TimerEvent: {}
[630.315086] (-) TimerEvent: {}
[630.415459] (-) TimerEvent: {}
[630.516432] (-) TimerEvent: {}
[630.616943] (-) TimerEvent: {}
[630.718174] (-) TimerEvent: {}
[630.818583] (-) TimerEvent: {}
[630.877078] (ros1_bridge) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__Mesh__factories.cpp.o\x1b[0m\n'}
[630.918855] (-) TimerEvent: {}
[631.019270] (-) TimerEvent: {}
[631.119679] (-) TimerEvent: {}
[631.220046] (-) TimerEvent: {}
[631.320362] (-) TimerEvent: {}
[631.420737] (-) TimerEvent: {}
[631.521389] (-) TimerEvent: {}
[631.622154] (-) TimerEvent: {}
[631.722526] (-) TimerEvent: {}
[631.823232] (-) TimerEvent: {}
[631.923699] (-) TimerEvent: {}
[632.024275] (-) TimerEvent: {}
[632.030606] (ros1_bridge) StdoutLine: {'line': b'[ 71%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__MeshTriangle__factories.cpp.o\x1b[0m\n'}
[632.124403] (-) TimerEvent: {}
[632.224797] (-) TimerEvent: {}
[632.326159] (-) TimerEvent: {}
[632.426547] (-) TimerEvent: {}
[632.526936] (-) TimerEvent: {}
[632.628149] (-) TimerEvent: {}
[632.728518] (-) TimerEvent: {}
[632.829347] (-) TimerEvent: {}
[632.930258] (-) TimerEvent: {}
[633.030630] (-) TimerEvent: {}
[633.131148] (-) TimerEvent: {}
[633.234682] (-) TimerEvent: {}
[633.338246] (-) TimerEvent: {}
[633.438556] (-) TimerEvent: {}
[633.538924] (-) TimerEvent: {}
[633.646183] (-) TimerEvent: {}
[633.746669] (-) TimerEvent: {}
[633.847019] (-) TimerEvent: {}
[633.947712] (-) TimerEvent: {}
[634.048091] (-) TimerEvent: {}
[634.148856] (-) TimerEvent: {}
[634.250159] (-) TimerEvent: {}
[634.350554] (-) TimerEvent: {}
[634.450954] (-) TimerEvent: {}
[634.551783] (-) TimerEvent: {}
[634.652200] (-) TimerEvent: {}
[634.752841] (-) TimerEvent: {}
[634.853319] (-) TimerEvent: {}
[634.954580] (-) TimerEvent: {}
[635.055092] (-) TimerEvent: {}
[635.155515] (-) TimerEvent: {}
[635.256262] (-) TimerEvent: {}
[635.362151] (-) TimerEvent: {}
[635.466201] (-) TimerEvent: {}
[635.570402] (-) TimerEvent: {}
[635.670924] (-) TimerEvent: {}
[635.771340] (-) TimerEvent: {}
[635.871755] (-) TimerEvent: {}
[635.972185] (-) TimerEvent: {}
[636.072916] (-) TimerEvent: {}
[636.173411] (-) TimerEvent: {}
[636.274422] (-) TimerEvent: {}
[636.374996] (-) TimerEvent: {}
[636.476007] (-) TimerEvent: {}
[636.577044] (-) TimerEvent: {}
[636.677504] (-) TimerEvent: {}
[636.778149] (-) TimerEvent: {}
[636.878618] (-) TimerEvent: {}
[636.979032] (-) TimerEvent: {}
[637.079423] (-) TimerEvent: {}
[637.179868] (-) TimerEvent: {}
[637.280855] (-) TimerEvent: {}
[637.381305] (-) TimerEvent: {}
[637.482145] (-) TimerEvent: {}
[637.582684] (-) TimerEvent: {}
[637.683125] (-) TimerEvent: {}
[637.784607] (-) TimerEvent: {}
[637.885195] (-) TimerEvent: {}
[637.986246] (-) TimerEvent: {}
[638.086636] (-) TimerEvent: {}
[638.187074] (-) TimerEvent: {}
[638.287748] (-) TimerEvent: {}
[638.388130] (-) TimerEvent: {}
[638.488788] (-) TimerEvent: {}
[638.589176] (-) TimerEvent: {}
[638.690146] (-) TimerEvent: {}
[638.790719] (-) TimerEvent: {}
[638.891107] (-) TimerEvent: {}
[638.991504] (-) TimerEvent: {}
[639.091895] (-) TimerEvent: {}
[639.192283] (-) TimerEvent: {}
[639.293409] (-) TimerEvent: {}
[639.394684] (-) TimerEvent: {}
[639.495066] (-) TimerEvent: {}
[639.595456] (-) TimerEvent: {}
[639.695917] (-) TimerEvent: {}
[639.796444] (-) TimerEvent: {}
[639.896855] (-) TimerEvent: {}
[639.997317] (-) TimerEvent: {}
[640.098149] (-) TimerEvent: {}
[640.198563] (-) TimerEvent: {}
[640.299003] (-) TimerEvent: {}
[640.399492] (-) TimerEvent: {}
[640.500211] (-) TimerEvent: {}
[640.602293] (-) TimerEvent: {}
[640.703415] (-) TimerEvent: {}
[640.804799] (-) TimerEvent: {}
[640.906528] (-) TimerEvent: {}
[641.006860] (-) TimerEvent: {}
[641.076078] (ros1_bridge) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__Plane__factories.cpp.o\x1b[0m\n'}
[641.106952] (-) TimerEvent: {}
[641.207367] (-) TimerEvent: {}
[641.307957] (-) TimerEvent: {}
[641.408724] (-) TimerEvent: {}
[641.509173] (-) TimerEvent: {}
[641.610352] (-) TimerEvent: {}
[641.711185] (-) TimerEvent: {}
[641.811673] (-) TimerEvent: {}
[641.912168] (-) TimerEvent: {}
[642.014289] (-) TimerEvent: {}
[642.115230] (-) TimerEvent: {}
[642.215705] (-) TimerEvent: {}
[642.317034] (-) TimerEvent: {}
[642.419353] (-) TimerEvent: {}
[642.519688] (-) TimerEvent: {}
[642.619971] (-) TimerEvent: {}
[642.720809] (-) TimerEvent: {}
[642.821480] (-) TimerEvent: {}
[642.922223] (-) TimerEvent: {}
[643.022920] (-) TimerEvent: {}
[643.123288] (-) TimerEvent: {}
[643.223983] (-) TimerEvent: {}
[643.325232] (-) TimerEvent: {}
[643.426141] (-) TimerEvent: {}
[643.526612] (-) TimerEvent: {}
[643.627005] (-) TimerEvent: {}
[643.727566] (-) TimerEvent: {}
[643.828090] (-) TimerEvent: {}
[643.928741] (-) TimerEvent: {}
[644.029336] (-) TimerEvent: {}
[644.130144] (-) TimerEvent: {}
[644.230435] (-) TimerEvent: {}
[644.330786] (-) TimerEvent: {}
[644.431120] (-) TimerEvent: {}
[644.531460] (-) TimerEvent: {}
[644.631785] (-) TimerEvent: {}
[644.732062] (-) TimerEvent: {}
[644.832362] (-) TimerEvent: {}
[644.932782] (-) TimerEvent: {}
[645.033074] (-) TimerEvent: {}
[645.133411] (-) TimerEvent: {}
[645.234480] (-) TimerEvent: {}
[645.335392] (-) TimerEvent: {}
[645.436034] (-) TimerEvent: {}
[645.536793] (-) TimerEvent: {}
[645.637371] (-) TimerEvent: {}
[645.738487] (-) TimerEvent: {}
[645.838837] (-) TimerEvent: {}
[645.939107] (-) TimerEvent: {}
[646.039581] (-) TimerEvent: {}
[646.139878] (-) TimerEvent: {}
[646.240249] (-) TimerEvent: {}
[646.340898] (-) TimerEvent: {}
[646.442137] (-) TimerEvent: {}
[646.542467] (-) TimerEvent: {}
[646.642887] (-) TimerEvent: {}
[646.743837] (-) TimerEvent: {}
[646.845036] (-) TimerEvent: {}
[646.945350] (-) TimerEvent: {}
[647.046177] (-) TimerEvent: {}
[647.146958] (-) TimerEvent: {}
[647.247694] (-) TimerEvent: {}
[647.347964] (-) TimerEvent: {}
[647.448349] (-) TimerEvent: {}
[647.548640] (-) TimerEvent: {}
[647.648952] (-) TimerEvent: {}
[647.749531] (-) TimerEvent: {}
[647.835863] (ros1_bridge) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__SolidPrimitive__factories.cpp.o\x1b[0m\n'}
[647.850262] (-) TimerEvent: {}
[647.951082] (-) TimerEvent: {}
[648.051534] (-) TimerEvent: {}
[648.152275] (-) TimerEvent: {}
[648.252634] (-) TimerEvent: {}
[648.353345] (-) TimerEvent: {}
[648.454237] (-) TimerEvent: {}
[648.554838] (-) TimerEvent: {}
[648.655400] (-) TimerEvent: {}
[648.755670] (-) TimerEvent: {}
[648.855943] (-) TimerEvent: {}
[648.956211] (-) TimerEvent: {}
[649.056480] (-) TimerEvent: {}
[649.156752] (-) TimerEvent: {}
[649.257106] (-) TimerEvent: {}
[649.357440] (-) TimerEvent: {}
[649.458598] (-) TimerEvent: {}
[649.559273] (-) TimerEvent: {}
[649.659595] (-) TimerEvent: {}
[649.760051] (-) TimerEvent: {}
[649.860882] (-) TimerEvent: {}
[649.961256] (-) TimerEvent: {}
[650.061500] (-) TimerEvent: {}
[650.162277] (-) TimerEvent: {}
[650.262792] (-) TimerEvent: {}
[650.363077] (-) TimerEvent: {}
[650.463773] (-) TimerEvent: {}
[650.564600] (-) TimerEvent: {}
[650.665453] (-) TimerEvent: {}
[650.766205] (-) TimerEvent: {}
[650.851493] (ros1_bridge) StdoutLine: {'line': b'[ 72%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs_factories.cpp.o\x1b[0m\n'}
[650.866329] (-) TimerEvent: {}
[650.966706] (-) TimerEvent: {}
[651.067212] (-) TimerEvent: {}
[651.167654] (-) TimerEvent: {}
[651.268111] (-) TimerEvent: {}
[651.368554] (-) TimerEvent: {}
[651.469211] (-) TimerEvent: {}
[651.570143] (-) TimerEvent: {}
[651.670540] (-) TimerEvent: {}
[651.771231] (-) TimerEvent: {}
[651.871664] (-) TimerEvent: {}
[651.972196] (-) TimerEvent: {}
[652.072653] (-) TimerEvent: {}
[652.173528] (-) TimerEvent: {}
[652.274188] (-) TimerEvent: {}
[652.374664] (-) TimerEvent: {}
[652.475110] (-) TimerEvent: {}
[652.575558] (-) TimerEvent: {}
[652.676008] (-) TimerEvent: {}
[652.776456] (-) TimerEvent: {}
[652.877175] (-) TimerEvent: {}
[652.978116] (-) TimerEvent: {}
[653.078568] (-) TimerEvent: {}
[653.179234] (-) TimerEvent: {}
[653.279713] (-) TimerEvent: {}
[653.380170] (-) TimerEvent: {}
[653.480841] (-) TimerEvent: {}
[653.582155] (-) TimerEvent: {}
[653.682568] (-) TimerEvent: {}
[653.782952] (-) TimerEvent: {}
[653.883405] (-) TimerEvent: {}
[653.983836] (-) TimerEvent: {}
[654.084272] (-) TimerEvent: {}
[654.185204] (-) TimerEvent: {}
[654.286175] (-) TimerEvent: {}
[654.386617] (-) TimerEvent: {}
[654.460327] (ros1_bridge) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__MetricsMessage__factories.cpp.o\x1b[0m\n'}
[654.486711] (-) TimerEvent: {}
[654.587118] (-) TimerEvent: {}
[654.687527] (-) TimerEvent: {}
[654.787952] (-) TimerEvent: {}
[654.888359] (-) TimerEvent: {}
[654.988759] (-) TimerEvent: {}
[655.089216] (-) TimerEvent: {}
[655.190177] (-) TimerEvent: {}
[655.290577] (-) TimerEvent: {}
[655.385795] (ros1_bridge) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__StatisticDataPoint__factories.cpp.o\x1b[0m\n'}
[655.391114] (-) TimerEvent: {}
[655.491541] (-) TimerEvent: {}
[655.592323] (-) TimerEvent: {}
[655.692768] (-) TimerEvent: {}
[655.793241] (-) TimerEvent: {}
[655.894218] (-) TimerEvent: {}
[655.995429] (-) TimerEvent: {}
[656.095856] (-) TimerEvent: {}
[656.196304] (-) TimerEvent: {}
[656.296754] (-) TimerEvent: {}
[656.397242] (-) TimerEvent: {}
[656.498167] (-) TimerEvent: {}
[656.598614] (-) TimerEvent: {}
[656.699077] (-) TimerEvent: {}
[656.799444] (-) TimerEvent: {}
[656.899864] (-) TimerEvent: {}
[657.000857] (-) TimerEvent: {}
[657.101307] (-) TimerEvent: {}
[657.202318] (-) TimerEvent: {}
[657.302825] (-) TimerEvent: {}
[657.403308] (-) TimerEvent: {}
[657.503755] (-) TimerEvent: {}
[657.604276] (-) TimerEvent: {}
[657.704753] (-) TimerEvent: {}
[657.805222] (-) TimerEvent: {}
[657.906159] (-) TimerEvent: {}
[658.006687] (-) TimerEvent: {}
[658.092719] (ros1_bridge) StdoutLine: {'line': b'[ 73%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__StatisticDataType__factories.cpp.o\x1b[0m\n'}
[658.107186] (-) TimerEvent: {}
[658.207638] (-) TimerEvent: {}
[658.308095] (-) TimerEvent: {}
[658.409073] (-) TimerEvent: {}
[658.509529] (-) TimerEvent: {}
[658.610201] (-) TimerEvent: {}
[658.710709] (-) TimerEvent: {}
[658.811084] (-) TimerEvent: {}
[658.877621] (ros1_bridge) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs_factories.cpp.o\x1b[0m\n'}
[658.911302] (-) TimerEvent: {}
[659.011733] (-) TimerEvent: {}
[659.112174] (-) TimerEvent: {}
[659.213100] (-) TimerEvent: {}
[659.314204] (-) TimerEvent: {}
[659.414642] (-) TimerEvent: {}
[659.515086] (-) TimerEvent: {}
[659.615537] (-) TimerEvent: {}
[659.715988] (-) TimerEvent: {}
[659.817006] (-) TimerEvent: {}
[659.918152] (-) TimerEvent: {}
[660.018636] (-) TimerEvent: {}
[660.119242] (-) TimerEvent: {}
[660.219705] (-) TimerEvent: {}
[660.320131] (-) TimerEvent: {}
[660.420806] (-) TimerEvent: {}
[660.522156] (-) TimerEvent: {}
[660.626151] (-) TimerEvent: {}
[660.726455] (-) TimerEvent: {}
[660.827018] (-) TimerEvent: {}
[660.927428] (-) TimerEvent: {}
[661.027843] (-) TimerEvent: {}
[661.128255] (-) TimerEvent: {}
[661.229139] (-) TimerEvent: {}
[661.330282] (-) TimerEvent: {}
[661.431037] (-) TimerEvent: {}
[661.531536] (-) TimerEvent: {}
[661.632035] (-) TimerEvent: {}
[661.732450] (-) TimerEvent: {}
[661.809435] (ros1_bridge) StdoutLine: {'line': b'[ 74%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Bool__factories.cpp.o\x1b[0m\n'}
[661.833102] (-) TimerEvent: {}
[661.934176] (-) TimerEvent: {}
[662.034667] (-) TimerEvent: {}
[662.135143] (-) TimerEvent: {}
[662.235757] (-) TimerEvent: {}
[662.337050] (-) TimerEvent: {}
[662.438162] (-) TimerEvent: {}
[662.538513] (-) TimerEvent: {}
[662.639342] (-) TimerEvent: {}
[662.739827] (-) TimerEvent: {}
[662.840128] (-) TimerEvent: {}
[662.898633] (ros1_bridge) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Byte__factories.cpp.o\x1b[0m\n'}
[662.940205] (-) TimerEvent: {}
[663.040555] (-) TimerEvent: {}
[663.140907] (-) TimerEvent: {}
[663.241282] (-) TimerEvent: {}
[663.342213] (-) TimerEvent: {}
[663.442598] (-) TimerEvent: {}
[663.543038] (-) TimerEvent: {}
[663.643470] (-) TimerEvent: {}
[663.743872] (-) TimerEvent: {}
[663.844262] (-) TimerEvent: {}
[663.945116] (-) TimerEvent: {}
[664.045525] (-) TimerEvent: {}
[664.146186] (-) TimerEvent: {}
[664.246630] (-) TimerEvent: {}
[664.347050] (-) TimerEvent: {}
[664.447450] (-) TimerEvent: {}
[664.547847] (-) TimerEvent: {}
[664.648749] (-) TimerEvent: {}
[664.749541] (-) TimerEvent: {}
[664.850164] (-) TimerEvent: {}
[664.950589] (-) TimerEvent: {}
[665.051043] (-) TimerEvent: {}
[665.151454] (-) TimerEvent: {}
[665.251886] (-) TimerEvent: {}
[665.352264] (-) TimerEvent: {}
[665.452698] (-) TimerEvent: {}
[665.553142] (-) TimerEvent: {}
[665.654170] (-) TimerEvent: {}
[665.754568] (-) TimerEvent: {}
[665.855009] (-) TimerEvent: {}
[665.955924] (-) TimerEvent: {}
[666.056334] (-) TimerEvent: {}
[666.157221] (-) TimerEvent: {}
[666.258189] (-) TimerEvent: {}
[666.358664] (-) TimerEvent: {}
[666.459063] (-) TimerEvent: {}
[666.559457] (-) TimerEvent: {}
[666.659835] (-) TimerEvent: {}
[666.760762] (-) TimerEvent: {}
[666.861171] (-) TimerEvent: {}
[666.962145] (-) TimerEvent: {}
[667.062464] (-) TimerEvent: {}
[667.162998] (-) TimerEvent: {}
[667.263948] (-) TimerEvent: {}
[667.364359] (-) TimerEvent: {}
[667.465126] (-) TimerEvent: {}
[667.566151] (-) TimerEvent: {}
[667.666500] (-) TimerEvent: {}
[667.766939] (-) TimerEvent: {}
[667.867342] (-) TimerEvent: {}
[667.967720] (-) TimerEvent: {}
[668.068129] (-) TimerEvent: {}
[668.168770] (-) TimerEvent: {}
[668.269299] (-) TimerEvent: {}
[668.370147] (-) TimerEvent: {}
[668.470499] (-) TimerEvent: {}
[668.571372] (-) TimerEvent: {}
[668.671909] (-) TimerEvent: {}
[668.772789] (-) TimerEvent: {}
[668.874160] (-) TimerEvent: {}
[668.974586] (-) TimerEvent: {}
[669.075031] (-) TimerEvent: {}
[669.175433] (-) TimerEvent: {}
[669.275859] (-) TimerEvent: {}
[669.376223] (-) TimerEvent: {}
[669.476969] (-) TimerEvent: {}
[669.578106] (-) TimerEvent: {}
[669.678443] (-) TimerEvent: {}
[669.778864] (-) TimerEvent: {}
[669.879185] (-) TimerEvent: {}
[669.979601] (-) TimerEvent: {}
[670.079985] (-) TimerEvent: {}
[670.186214] (-) TimerEvent: {}
[670.198262] (ros1_bridge) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__ByteMultiArray__factories.cpp.o\x1b[0m\n'}
[670.290157] (-) TimerEvent: {}
[670.390745] (-) TimerEvent: {}
[670.491103] (-) TimerEvent: {}
[670.591532] (-) TimerEvent: {}
[670.691918] (-) TimerEvent: {}
[670.792468] (-) TimerEvent: {}
[670.892954] (-) TimerEvent: {}
[670.993397] (-) TimerEvent: {}
[671.094192] (-) TimerEvent: {}
[671.194704] (-) TimerEvent: {}
[671.295075] (-) TimerEvent: {}
[671.395603] (-) TimerEvent: {}
[671.495966] (-) TimerEvent: {}
[671.596468] (-) TimerEvent: {}
[671.697095] (-) TimerEvent: {}
[671.797479] (-) TimerEvent: {}
[671.898230] (-) TimerEvent: {}
[671.998645] (-) TimerEvent: {}
[672.099203] (-) TimerEvent: {}
[672.200153] (-) TimerEvent: {}
[672.301139] (-) TimerEvent: {}
[672.401388] (-) TimerEvent: {}
[672.502164] (-) TimerEvent: {}
[672.602420] (-) TimerEvent: {}
[672.702659] (-) TimerEvent: {}
[672.803316] (-) TimerEvent: {}
[672.903658] (-) TimerEvent: {}
[673.004349] (-) TimerEvent: {}
[673.105034] (-) TimerEvent: {}
[673.206445] (-) TimerEvent: {}
[673.307193] (-) TimerEvent: {}
[673.407536] (-) TimerEvent: {}
[673.508079] (-) TimerEvent: {}
[673.609306] (-) TimerEvent: {}
[673.710148] (-) TimerEvent: {}
[673.810461] (-) TimerEvent: {}
[673.911042] (-) TimerEvent: {}
[674.011382] (-) TimerEvent: {}
[674.111659] (-) TimerEvent: {}
[674.212487] (-) TimerEvent: {}
[674.313130] (-) TimerEvent: {}
[674.413511] (-) TimerEvent: {}
[674.514141] (-) TimerEvent: {}
[674.614575] (-) TimerEvent: {}
[674.714850] (-) TimerEvent: {}
[674.815582] (-) TimerEvent: {}
[674.915989] (-) TimerEvent: {}
[675.016882] (-) TimerEvent: {}
[675.117453] (-) TimerEvent: {}
[675.218404] (-) TimerEvent: {}
[675.319170] (-) TimerEvent: {}
[675.419471] (-) TimerEvent: {}
[675.519858] (-) TimerEvent: {}
[675.620639] (-) TimerEvent: {}
[675.721239] (-) TimerEvent: {}
[675.821505] (-) TimerEvent: {}
[675.922196] (-) TimerEvent: {}
[676.022487] (-) TimerEvent: {}
[676.122799] (-) TimerEvent: {}
[676.223397] (-) TimerEvent: {}
[676.323666] (-) TimerEvent: {}
[676.423916] (-) TimerEvent: {}
[676.524231] (-) TimerEvent: {}
[676.624830] (-) TimerEvent: {}
[676.725228] (-) TimerEvent: {}
[676.826125] (-) TimerEvent: {}
[676.926709] (-) TimerEvent: {}
[677.027465] (-) TimerEvent: {}
[677.128098] (-) TimerEvent: {}
[677.229217] (-) TimerEvent: {}
[677.275769] (ros1_bridge) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Char__factories.cpp.o\x1b[0m\n'}
[677.329562] (-) TimerEvent: {}
[677.431203] (-) TimerEvent: {}
[677.531635] (-) TimerEvent: {}
[677.632235] (-) TimerEvent: {}
[677.733137] (-) TimerEvent: {}
[677.833430] (-) TimerEvent: {}
[677.934288] (-) TimerEvent: {}
[678.034869] (-) TimerEvent: {}
[678.135720] (-) TimerEvent: {}
[678.236413] (-) TimerEvent: {}
[678.338188] (-) TimerEvent: {}
[678.438460] (-) TimerEvent: {}
[678.538953] (-) TimerEvent: {}
[678.639711] (-) TimerEvent: {}
[678.739974] (-) TimerEvent: {}
[678.840263] (-) TimerEvent: {}
[678.940567] (-) TimerEvent: {}
[679.040906] (-) TimerEvent: {}
[679.141313] (-) TimerEvent: {}
[679.242195] (-) TimerEvent: {}
[679.342575] (-) TimerEvent: {}
[679.442865] (-) TimerEvent: {}
[679.543332] (-) TimerEvent: {}
[679.644273] (-) TimerEvent: {}
[679.744840] (-) TimerEvent: {}
[679.845564] (-) TimerEvent: {}
[679.946417] (-) TimerEvent: {}
[680.046689] (-) TimerEvent: {}
[680.146969] (-) TimerEvent: {}
[680.247238] (-) TimerEvent: {}
[680.305440] (ros1_bridge) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__ColorRGBA__factories.cpp.o\x1b[0m\n'}
[680.347362] (-) TimerEvent: {}
[680.447885] (-) TimerEvent: {}
[680.548338] (-) TimerEvent: {}
[680.648701] (-) TimerEvent: {}
[680.750147] (-) TimerEvent: {}
[680.850536] (-) TimerEvent: {}
[680.950981] (-) TimerEvent: {}
[681.051592] (-) TimerEvent: {}
[681.152030] (-) TimerEvent: {}
[681.252472] (-) TimerEvent: {}
[681.354152] (-) TimerEvent: {}
[681.454533] (-) TimerEvent: {}
[681.555383] (-) TimerEvent: {}
[681.655779] (-) TimerEvent: {}
[681.756188] (-) TimerEvent: {}
[681.856607] (-) TimerEvent: {}
[681.957041] (-) TimerEvent: {}
[682.057471] (-) TimerEvent: {}
[682.158384] (-) TimerEvent: {}
[682.258840] (-) TimerEvent: {}
[682.359760] (-) TimerEvent: {}
[682.460125] (-) TimerEvent: {}
[682.560459] (-) TimerEvent: {}
[682.661074] (-) TimerEvent: {}
[682.761463] (-) TimerEvent: {}
[682.862158] (-) TimerEvent: {}
[682.962560] (-) TimerEvent: {}
[683.063338] (-) TimerEvent: {}
[683.164354] (-) TimerEvent: {}
[683.265039] (-) TimerEvent: {}
[683.366591] (-) TimerEvent: {}
[683.466977] (-) TimerEvent: {}
[683.567529] (-) TimerEvent: {}
[683.667902] (-) TimerEvent: {}
[683.768223] (-) TimerEvent: {}
[683.868603] (-) TimerEvent: {}
[683.969289] (-) TimerEvent: {}
[684.071229] (-) TimerEvent: {}
[684.171728] (-) TimerEvent: {}
[684.272840] (-) TimerEvent: {}
[684.373234] (-) TimerEvent: {}
[684.474148] (-) TimerEvent: {}
[684.574624] (-) TimerEvent: {}
[684.675036] (-) TimerEvent: {}
[684.719094] (ros1_bridge) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Empty__factories.cpp.o\x1b[0m\n'}
[684.775126] (-) TimerEvent: {}
[684.875628] (-) TimerEvent: {}
[684.976111] (-) TimerEvent: {}
[685.076525] (-) TimerEvent: {}
[685.177091] (-) TimerEvent: {}
[685.278158] (-) TimerEvent: {}
[685.378531] (-) TimerEvent: {}
[685.478958] (-) TimerEvent: {}
[685.579495] (-) TimerEvent: {}
[685.679823] (-) TimerEvent: {}
[685.780209] (-) TimerEvent: {}
[685.880592] (-) TimerEvent: {}
[685.981536] (-) TimerEvent: {}
[686.082493] (-) TimerEvent: {}
[686.182835] (-) TimerEvent: {}
[686.283269] (-) TimerEvent: {}
[686.383659] (-) TimerEvent: {}
[686.484482] (-) TimerEvent: {}
[686.584853] (-) TimerEvent: {}
[686.685214] (-) TimerEvent: {}
[686.786183] (-) TimerEvent: {}
[686.886552] (-) TimerEvent: {}
[686.986916] (-) TimerEvent: {}
[687.087674] (-) TimerEvent: {}
[687.188138] (-) TimerEvent: {}
[687.288777] (-) TimerEvent: {}
[687.390145] (-) TimerEvent: {}
[687.490528] (-) TimerEvent: {}
[687.590897] (-) TimerEvent: {}
[687.691231] (-) TimerEvent: {}
[687.791665] (-) TimerEvent: {}
[687.892016] (-) TimerEvent: {}
[687.992971] (-) TimerEvent: {}
[688.093609] (-) TimerEvent: {}
[688.194172] (-) TimerEvent: {}
[688.294666] (-) TimerEvent: {}
[688.395135] (-) TimerEvent: {}
[688.420236] (ros1_bridge) StdoutLine: {'line': b'[ 76%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float32__factories.cpp.o\x1b[0m\n'}
[688.495678] (-) TimerEvent: {}
[688.596159] (-) TimerEvent: {}
[688.696492] (-) TimerEvent: {}
[688.796893] (-) TimerEvent: {}
[688.897291] (-) TimerEvent: {}
[688.998165] (-) TimerEvent: {}
[689.098555] (-) TimerEvent: {}
[689.198993] (-) TimerEvent: {}
[689.299378] (-) TimerEvent: {}
[689.399722] (-) TimerEvent: {}
[689.500667] (-) TimerEvent: {}
[689.601137] (-) TimerEvent: {}
[689.702205] (-) TimerEvent: {}
[689.802634] (-) TimerEvent: {}
[689.903010] (-) TimerEvent: {}
[690.003415] (-) TimerEvent: {}
[690.103970] (-) TimerEvent: {}
[690.204341] (-) TimerEvent: {}
[690.304860] (-) TimerEvent: {}
[690.406163] (-) TimerEvent: {}
[690.506567] (-) TimerEvent: {}
[690.606982] (-) TimerEvent: {}
[690.707907] (-) TimerEvent: {}
[690.808269] (-) TimerEvent: {}
[690.908676] (-) TimerEvent: {}
[691.009102] (-) TimerEvent: {}
[691.109495] (-) TimerEvent: {}
[691.210155] (-) TimerEvent: {}
[691.310580] (-) TimerEvent: {}
[691.410991] (-) TimerEvent: {}
[691.511799] (-) TimerEvent: {}
[691.612192] (-) TimerEvent: {}
[691.713003] (-) TimerEvent: {}
[691.813409] (-) TimerEvent: {}
[691.914168] (-) TimerEvent: {}
[692.014561] (-) TimerEvent: {}
[692.115383] (-) TimerEvent: {}
[692.215828] (-) TimerEvent: {}
[692.316230] (-) TimerEvent: {}
[692.416831] (-) TimerEvent: {}
[692.518459] (-) TimerEvent: {}
[692.618969] (-) TimerEvent: {}
[692.719962] (-) TimerEvent: {}
[692.820430] (-) TimerEvent: {}
[692.912578] (ros1_bridge) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float32MultiArray__factories.cpp.o\x1b[0m\n'}
[692.920569] (-) TimerEvent: {}
[693.021008] (-) TimerEvent: {}
[693.121402] (-) TimerEvent: {}
[693.222260] (-) TimerEvent: {}
[693.322622] (-) TimerEvent: {}
[693.422998] (-) TimerEvent: {}
[693.523407] (-) TimerEvent: {}
[693.623890] (-) TimerEvent: {}
[693.724280] (-) TimerEvent: {}
[693.824647] (-) TimerEvent: {}
[693.925069] (-) TimerEvent: {}
[694.026163] (-) TimerEvent: {}
[694.126540] (-) TimerEvent: {}
[694.226899] (-) TimerEvent: {}
[694.327390] (-) TimerEvent: {}
[694.427846] (-) TimerEvent: {}
[694.528256] (-) TimerEvent: {}
[694.628663] (-) TimerEvent: {}
[694.729034] (-) TimerEvent: {}
[694.830145] (-) TimerEvent: {}
[694.930509] (-) TimerEvent: {}
[695.030988] (-) TimerEvent: {}
[695.131433] (-) TimerEvent: {}
[695.231853] (-) TimerEvent: {}
[695.332240] (-) TimerEvent: {}
[695.433184] (-) TimerEvent: {}
[695.534178] (-) TimerEvent: {}
[695.634689] (-) TimerEvent: {}
[695.735174] (-) TimerEvent: {}
[695.835597] (-) TimerEvent: {}
[695.935990] (-) TimerEvent: {}
[696.036410] (-) TimerEvent: {}
[696.136820] (-) TimerEvent: {}
[696.237347] (-) TimerEvent: {}
[696.338316] (-) TimerEvent: {}
[696.438789] (-) TimerEvent: {}
[696.539248] (-) TimerEvent: {}
[696.639754] (-) TimerEvent: {}
[696.740318] (-) TimerEvent: {}
[696.840757] (-) TimerEvent: {}
[696.878650] (ros1_bridge) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float64__factories.cpp.o\x1b[0m\n'}
[696.941088] (-) TimerEvent: {}
[697.042238] (-) TimerEvent: {}
[697.142706] (-) TimerEvent: {}
[697.243126] (-) TimerEvent: {}
[697.343510] (-) TimerEvent: {}
[697.444016] (-) TimerEvent: {}
[697.544441] (-) TimerEvent: {}
[697.644837] (-) TimerEvent: {}
[697.745285] (-) TimerEvent: {}
[697.846215] (-) TimerEvent: {}
[697.946727] (-) TimerEvent: {}
[698.047304] (-) TimerEvent: {}
[698.147767] (-) TimerEvent: {}
[698.248242] (-) TimerEvent: {}
[698.348705] (-) TimerEvent: {}
[698.449132] (-) TimerEvent: {}
[698.549488] (-) TimerEvent: {}
[698.650244] (-) TimerEvent: {}
[698.750694] (-) TimerEvent: {}
[698.851133] (-) TimerEvent: {}
[698.951566] (-) TimerEvent: {}
[699.051991] (-) TimerEvent: {}
[699.152468] (-) TimerEvent: {}
[699.253009] (-) TimerEvent: {}
[699.354284] (-) TimerEvent: {}
[699.454761] (-) TimerEvent: {}
[699.555238] (-) TimerEvent: {}
[699.655712] (-) TimerEvent: {}
[699.756224] (-) TimerEvent: {}
[699.856758] (-) TimerEvent: {}
[699.957278] (-) TimerEvent: {}
[700.058251] (-) TimerEvent: {}
[700.158782] (-) TimerEvent: {}
[700.259312] (-) TimerEvent: {}
[700.359798] (-) TimerEvent: {}
[700.460287] (-) TimerEvent: {}
[700.560783] (-) TimerEvent: {}
[700.661237] (-) TimerEvent: {}
[700.762344] (-) TimerEvent: {}
[700.862709] (-) TimerEvent: {}
[700.963107] (-) TimerEvent: {}
[701.063527] (-) TimerEvent: {}
[701.073655] (ros1_bridge) StdoutLine: {'line': b'[ 77%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float64MultiArray__factories.cpp.o\x1b[0m\n'}
[701.163620] (-) TimerEvent: {}
[701.264018] (-) TimerEvent: {}
[701.365002] (-) TimerEvent: {}
[701.465370] (-) TimerEvent: {}
[701.566526] (-) TimerEvent: {}
[701.667552] (-) TimerEvent: {}
[701.767949] (-) TimerEvent: {}
[701.868745] (-) TimerEvent: {}
[701.969129] (-) TimerEvent: {}
[702.070180] (-) TimerEvent: {}
[702.170649] (-) TimerEvent: {}
[702.271320] (-) TimerEvent: {}
[702.371794] (-) TimerEvent: {}
[702.472570] (-) TimerEvent: {}
[702.573283] (-) TimerEvent: {}
[702.674243] (-) TimerEvent: {}
[702.774749] (-) TimerEvent: {}
[702.875371] (-) TimerEvent: {}
[702.975796] (-) TimerEvent: {}
[703.076760] (-) TimerEvent: {}
[703.177196] (-) TimerEvent: {}
[703.278299] (-) TimerEvent: {}
[703.378646] (-) TimerEvent: {}
[703.479019] (-) TimerEvent: {}
[703.579458] (-) TimerEvent: {}
[703.679804] (-) TimerEvent: {}
[703.780287] (-) TimerEvent: {}
[703.880731] (-) TimerEvent: {}
[703.981194] (-) TimerEvent: {}
[704.082226] (-) TimerEvent: {}
[704.182643] (-) TimerEvent: {}
[704.283479] (-) TimerEvent: {}
[704.383890] (-) TimerEvent: {}
[704.484754] (-) TimerEvent: {}
[704.585185] (-) TimerEvent: {}
[704.685573] (-) TimerEvent: {}
[704.786510] (-) TimerEvent: {}
[704.886982] (-) TimerEvent: {}
[704.987346] (-) TimerEvent: {}
[705.087702] (-) TimerEvent: {}
[705.188088] (-) TimerEvent: {}
[705.288463] (-) TimerEvent: {}
[705.293838] (ros1_bridge) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Header__factories.cpp.o\x1b[0m\n'}
[705.388714] (-) TimerEvent: {}
[705.489087] (-) TimerEvent: {}
[705.589486] (-) TimerEvent: {}
[705.690150] (-) TimerEvent: {}
[705.790594] (-) TimerEvent: {}
[705.891031] (-) TimerEvent: {}
[705.991485] (-) TimerEvent: {}
[706.091902] (-) TimerEvent: {}
[706.192510] (-) TimerEvent: {}
[706.292978] (-) TimerEvent: {}
[706.394112] (-) TimerEvent: {}
[706.494629] (-) TimerEvent: {}
[706.595086] (-) TimerEvent: {}
[706.695527] (-) TimerEvent: {}
[706.795990] (-) TimerEvent: {}
[706.896477] (-) TimerEvent: {}
[706.996962] (-) TimerEvent: {}
[707.097415] (-) TimerEvent: {}
[707.198499] (-) TimerEvent: {}
[707.299466] (-) TimerEvent: {}
[707.399879] (-) TimerEvent: {}
[707.500640] (-) TimerEvent: {}
[707.602478] (-) TimerEvent: {}
[707.703079] (-) TimerEvent: {}
[707.803799] (-) TimerEvent: {}
[707.904257] (-) TimerEvent: {}
[708.004820] (-) TimerEvent: {}
[708.105476] (-) TimerEvent: {}
[708.206586] (-) TimerEvent: {}
[708.307295] (-) TimerEvent: {}
[708.407762] (-) TimerEvent: {}
[708.508254] (-) TimerEvent: {}
[708.609214] (-) TimerEvent: {}
[708.710161] (-) TimerEvent: {}
[708.810855] (-) TimerEvent: {}
[708.911511] (-) TimerEvent: {}
[709.011988] (-) TimerEvent: {}
[709.112463] (-) TimerEvent: {}
[709.213112] (-) TimerEvent: {}
[709.314158] (-) TimerEvent: {}
[709.414591] (-) TimerEvent: {}
[709.514945] (-) TimerEvent: {}
[709.615404] (-) TimerEvent: {}
[709.715859] (-) TimerEvent: {}
[709.818151] (-) TimerEvent: {}
[709.922590] (-) TimerEvent: {}
[710.019096] (ros1_bridge) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int16__factories.cpp.o\x1b[0m\n'}
[710.022868] (-) TimerEvent: {}
[710.123224] (-) TimerEvent: {}
[710.223740] (-) TimerEvent: {}
[710.324387] (-) TimerEvent: {}
[710.424745] (-) TimerEvent: {}
[710.525166] (-) TimerEvent: {}
[710.625529] (-) TimerEvent: {}
[710.726158] (-) TimerEvent: {}
[710.826635] (-) TimerEvent: {}
[710.927193] (-) TimerEvent: {}
[711.027714] (-) TimerEvent: {}
[711.128164] (-) TimerEvent: {}
[711.229121] (-) TimerEvent: {}
[711.330201] (-) TimerEvent: {}
[711.430749] (-) TimerEvent: {}
[711.531810] (-) TimerEvent: {}
[711.632834] (-) TimerEvent: {}
[711.733322] (-) TimerEvent: {}
[711.834193] (-) TimerEvent: {}
[711.934839] (-) TimerEvent: {}
[712.035339] (-) TimerEvent: {}
[712.135796] (-) TimerEvent: {}
[712.236259] (-) TimerEvent: {}
[712.336809] (-) TimerEvent: {}
[712.437234] (-) TimerEvent: {}
[712.538167] (-) TimerEvent: {}
[712.638645] (-) TimerEvent: {}
[712.739101] (-) TimerEvent: {}
[712.839558] (-) TimerEvent: {}
[712.939942] (-) TimerEvent: {}
[713.040353] (-) TimerEvent: {}
[713.140812] (-) TimerEvent: {}
[713.242144] (-) TimerEvent: {}
[713.342557] (-) TimerEvent: {}
[713.443002] (-) TimerEvent: {}
[713.543497] (-) TimerEvent: {}
[713.644832] (-) TimerEvent: {}
[713.745589] (-) TimerEvent: {}
[713.820901] (ros1_bridge) StdoutLine: {'line': b'[ 78%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int16MultiArray__factories.cpp.o\x1b[0m\n'}
[713.846141] (-) TimerEvent: {}
[713.946694] (-) TimerEvent: {}
[714.047123] (-) TimerEvent: {}
[714.147692] (-) TimerEvent: {}
[714.248158] (-) TimerEvent: {}
[714.349225] (-) TimerEvent: {}
[714.450254] (-) TimerEvent: {}
[714.550823] (-) TimerEvent: {}
[714.651849] (-) TimerEvent: {}
[714.752313] (-) TimerEvent: {}
[714.852852] (-) TimerEvent: {}
[714.953354] (-) TimerEvent: {}
[715.054149] (-) TimerEvent: {}
[715.154694] (-) TimerEvent: {}
[715.255165] (-) TimerEvent: {}
[715.355716] (-) TimerEvent: {}
[715.456171] (-) TimerEvent: {}
[715.556607] (-) TimerEvent: {}
[715.658156] (-) TimerEvent: {}
[715.758678] (-) TimerEvent: {}
[715.859167] (-) TimerEvent: {}
[715.959733] (-) TimerEvent: {}
[716.060262] (-) TimerEvent: {}
[716.160723] (-) TimerEvent: {}
[716.261200] (-) TimerEvent: {}
[716.362169] (-) TimerEvent: {}
[716.462668] (-) TimerEvent: {}
[716.563122] (-) TimerEvent: {}
[716.663586] (-) TimerEvent: {}
[716.764033] (-) TimerEvent: {}
[716.864896] (-) TimerEvent: {}
[716.965318] (-) TimerEvent: {}
[717.066168] (-) TimerEvent: {}
[717.166612] (-) TimerEvent: {}
[717.267040] (-) TimerEvent: {}
[717.367499] (-) TimerEvent: {}
[717.467866] (-) TimerEvent: {}
[717.568791] (-) TimerEvent: {}
[717.670149] (-) TimerEvent: {}
[717.770586] (-) TimerEvent: {}
[717.870942] (-) TimerEvent: {}
[717.971460] (-) TimerEvent: {}
[718.071881] (-) TimerEvent: {}
[718.172799] (-) TimerEvent: {}
[718.273289] (-) TimerEvent: {}
[718.374724] (-) TimerEvent: {}
[718.475092] (-) TimerEvent: {}
[718.575593] (-) TimerEvent: {}
[718.675974] (-) TimerEvent: {}
[718.776813] (-) TimerEvent: {}
[718.816794] (ros1_bridge) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int32__factories.cpp.o\x1b[0m\n'}
[718.876908] (-) TimerEvent: {}
[718.978200] (-) TimerEvent: {}
[719.078688] (-) TimerEvent: {}
[719.179207] (-) TimerEvent: {}
[719.279805] (-) TimerEvent: {}
[719.380252] (-) TimerEvent: {}
[719.480772] (-) TimerEvent: {}
[719.582165] (-) TimerEvent: {}
[719.682640] (-) TimerEvent: {}
[719.783164] (-) TimerEvent: {}
[719.883641] (-) TimerEvent: {}
[719.984159] (-) TimerEvent: {}
[720.084718] (-) TimerEvent: {}
[720.185177] (-) TimerEvent: {}
[720.286213] (-) TimerEvent: {}
[720.390176] (-) TimerEvent: {}
[720.490520] (-) TimerEvent: {}
[720.590966] (-) TimerEvent: {}
[720.691422] (-) TimerEvent: {}
[720.792006] (-) TimerEvent: {}
[720.892345] (-) TimerEvent: {}
[720.992762] (-) TimerEvent: {}
[721.093217] (-) TimerEvent: {}
[721.194177] (-) TimerEvent: {}
[721.294613] (-) TimerEvent: {}
[721.395492] (-) TimerEvent: {}
[721.495981] (-) TimerEvent: {}
[721.596838] (-) TimerEvent: {}
[721.697284] (-) TimerEvent: {}
[721.798169] (-) TimerEvent: {}
[721.898540] (-) TimerEvent: {}
[721.999184] (-) TimerEvent: {}
[722.099657] (-) TimerEvent: {}
[722.200047] (-) TimerEvent: {}
[722.301015] (-) TimerEvent: {}
[722.401365] (-) TimerEvent: {}
[722.502157] (-) TimerEvent: {}
[722.602518] (-) TimerEvent: {}
[722.703109] (-) TimerEvent: {}
[722.736999] (ros1_bridge) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int32MultiArray__factories.cpp.o\x1b[0m\n'}
[722.803174] (-) TimerEvent: {}
[722.903511] (-) TimerEvent: {}
[723.003982] (-) TimerEvent: {}
[723.104334] (-) TimerEvent: {}
[723.204751] (-) TimerEvent: {}
[723.305239] (-) TimerEvent: {}
[723.406148] (-) TimerEvent: {}
[723.506508] (-) TimerEvent: {}
[723.606907] (-) TimerEvent: {}
[723.707449] (-) TimerEvent: {}
[723.808200] (-) TimerEvent: {}
[723.908949] (-) TimerEvent: {}
[724.009455] (-) TimerEvent: {}
[724.110154] (-) TimerEvent: {}
[724.210548] (-) TimerEvent: {}
[724.311054] (-) TimerEvent: {}
[724.411382] (-) TimerEvent: {}
[724.511783] (-) TimerEvent: {}
[724.612737] (-) TimerEvent: {}
[724.713134] (-) TimerEvent: {}
[724.813509] (-) TimerEvent: {}
[724.914166] (-) TimerEvent: {}
[725.014519] (-) TimerEvent: {}
[725.114912] (-) TimerEvent: {}
[725.215278] (-) TimerEvent: {}
[725.315597] (-) TimerEvent: {}
[725.416016] (-) TimerEvent: {}
[725.516989] (-) TimerEvent: {}
[725.617392] (-) TimerEvent: {}
[725.718320] (-) TimerEvent: {}
[725.819171] (-) TimerEvent: {}
[725.919550] (-) TimerEvent: {}
[726.019972] (-) TimerEvent: {}
[726.120434] (-) TimerEvent: {}
[726.221002] (-) TimerEvent: {}
[726.321376] (-) TimerEvent: {}
[726.422166] (-) TimerEvent: {}
[726.522562] (-) TimerEvent: {}
[726.623300] (-) TimerEvent: {}
[726.723671] (-) TimerEvent: {}
[726.824028] (-) TimerEvent: {}
[726.924480] (-) TimerEvent: {}
[727.025180] (-) TimerEvent: {}
[727.126179] (-) TimerEvent: {}
[727.226540] (-) TimerEvent: {}
[727.326897] (-) TimerEvent: {}
[727.427333] (-) TimerEvent: {}
[727.538623] (-) TimerEvent: {}
[727.639133] (-) TimerEvent: {}
[727.739528] (-) TimerEvent: {}
[727.840348] (-) TimerEvent: {}
[727.942136] (-) TimerEvent: {}
[728.042675] (-) TimerEvent: {}
[728.143531] (-) TimerEvent: {}
[728.243960] (-) TimerEvent: {}
[728.344246] (-) TimerEvent: {}
[728.444715] (-) TimerEvent: {}
[728.545251] (-) TimerEvent: {}
[728.646238] (-) TimerEvent: {}
[728.746708] (-) TimerEvent: {}
[728.847693] (-) TimerEvent: {}
[728.948499] (-) TimerEvent: {}
[729.048973] (-) TimerEvent: {}
[729.149411] (-) TimerEvent: {}
[729.250132] (-) TimerEvent: {}
[729.350387] (-) TimerEvent: {}
[729.450982] (-) TimerEvent: {}
[729.551651] (-) TimerEvent: {}
[729.653000] (-) TimerEvent: {}
[729.753496] (-) TimerEvent: {}
[729.854167] (-) TimerEvent: {}
[729.954609] (-) TimerEvent: {}
[730.055266] (-) TimerEvent: {}
[730.155660] (-) TimerEvent: {}
[730.256122] (-) TimerEvent: {}
[730.356829] (-) TimerEvent: {}
[730.457474] (-) TimerEvent: {}
[730.558155] (-) TimerEvent: {}
[730.605405] (ros1_bridge) StdoutLine: {'line': b'[ 79%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int64__factories.cpp.o\x1b[0m\n'}
[730.658292] (-) TimerEvent: {}
[730.758718] (-) TimerEvent: {}
[730.859223] (-) TimerEvent: {}
[730.906692] (ros1_bridge) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int64MultiArray__factories.cpp.o\x1b[0m\n'}
[730.959336] (-) TimerEvent: {}
[731.060209] (-) TimerEvent: {}
[731.160571] (-) TimerEvent: {}
[731.261218] (-) TimerEvent: {}
[731.362167] (-) TimerEvent: {}
[731.462670] (-) TimerEvent: {}
[731.563115] (-) TimerEvent: {}
[731.663591] (-) TimerEvent: {}
[731.764274] (-) TimerEvent: {}
[731.865298] (-) TimerEvent: {}
[731.966375] (-) TimerEvent: {}
[732.066868] (-) TimerEvent: {}
[732.167476] (-) TimerEvent: {}
[732.268316] (-) TimerEvent: {}
[732.369259] (-) TimerEvent: {}
[732.470150] (-) TimerEvent: {}
[732.570617] (-) TimerEvent: {}
[732.671047] (-) TimerEvent: {}
[732.771485] (-) TimerEvent: {}
[732.871966] (-) TimerEvent: {}
[732.972435] (-) TimerEvent: {}
[733.073014] (-) TimerEvent: {}
[733.174268] (-) TimerEvent: {}
[733.274737] (-) TimerEvent: {}
[733.375183] (-) TimerEvent: {}
[733.475580] (-) TimerEvent: {}
[733.576064] (-) TimerEvent: {}
[733.676697] (-) TimerEvent: {}
[733.778169] (-) TimerEvent: {}
[733.878655] (-) TimerEvent: {}
[733.979508] (-) TimerEvent: {}
[734.080056] (-) TimerEvent: {}
[734.180549] (-) TimerEvent: {}
[734.281022] (-) TimerEvent: {}
[734.382163] (-) TimerEvent: {}
[734.482935] (-) TimerEvent: {}
[734.583971] (-) TimerEvent: {}
[734.684536] (-) TimerEvent: {}
[734.785360] (-) TimerEvent: {}
[734.886162] (-) TimerEvent: {}
[734.986618] (-) TimerEvent: {}
[735.087341] (-) TimerEvent: {}
[735.188091] (-) TimerEvent: {}
[735.288704] (-) TimerEvent: {}
[735.390173] (-) TimerEvent: {}
[735.490663] (-) TimerEvent: {}
[735.591131] (-) TimerEvent: {}
[735.691594] (-) TimerEvent: {}
[735.792039] (-) TimerEvent: {}
[735.892403] (-) TimerEvent: {}
[735.992757] (-) TimerEvent: {}
[736.093130] (-) TimerEvent: {}
[736.194177] (-) TimerEvent: {}
[736.294647] (-) TimerEvent: {}
[736.395172] (-) TimerEvent: {}
[736.495530] (-) TimerEvent: {}
[736.596526] (-) TimerEvent: {}
[736.696924] (-) TimerEvent: {}
[736.797291] (-) TimerEvent: {}
[736.898177] (-) TimerEvent: {}
[736.998537] (-) TimerEvent: {}
[737.098962] (-) TimerEvent: {}
[737.199303] (-) TimerEvent: {}
[737.299650] (-) TimerEvent: {}
[737.400377] (-) TimerEvent: {}
[737.501308] (-) TimerEvent: {}
[737.602168] (-) TimerEvent: {}
[737.702542] (-) TimerEvent: {}
[737.802959] (-) TimerEvent: {}
[737.903324] (-) TimerEvent: {}
[738.003659] (-) TimerEvent: {}
[738.104026] (-) TimerEvent: {}
[738.204463] (-) TimerEvent: {}
[738.304936] (-) TimerEvent: {}
[738.405567] (-) TimerEvent: {}
[738.506169] (-) TimerEvent: {}
[738.604467] (ros1_bridge) StdoutLine: {'line': b'[ 80%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int8__factories.cpp.o\x1b[0m\n'}
[738.606248] (-) TimerEvent: {}
[738.710490] (-) TimerEvent: {}
[738.811698] (-) TimerEvent: {}
[738.912639] (-) TimerEvent: {}
[739.013224] (-) TimerEvent: {}
[739.114183] (-) TimerEvent: {}
[739.214664] (-) TimerEvent: {}
[739.315219] (-) TimerEvent: {}
[739.416069] (-) TimerEvent: {}
[739.417496] (ros1_bridge) StdoutLine: {'line': b'[ 81%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int8MultiArray__factories.cpp.o\x1b[0m\n'}
[739.516206] (-) TimerEvent: {}
[739.616869] (-) TimerEvent: {}
[739.717282] (-) TimerEvent: {}
[739.818183] (-) TimerEvent: {}
[739.918601] (-) TimerEvent: {}
[740.018984] (-) TimerEvent: {}
[740.119410] (-) TimerEvent: {}
[740.219811] (-) TimerEvent: {}
[740.320388] (-) TimerEvent: {}
[740.421036] (-) TimerEvent: {}
[740.522165] (-) TimerEvent: {}
[740.622605] (-) TimerEvent: {}
[740.723016] (-) TimerEvent: {}
[740.823645] (-) TimerEvent: {}
[740.924658] (-) TimerEvent: {}
[741.025095] (-) TimerEvent: {}
[741.125473] (-) TimerEvent: {}
[741.226589] (-) TimerEvent: {}
[741.327032] (-) TimerEvent: {}
[741.427467] (-) TimerEvent: {}
[741.527897] (-) TimerEvent: {}
[741.628673] (-) TimerEvent: {}
[741.729594] (-) TimerEvent: {}
[741.830566] (-) TimerEvent: {}
[741.930955] (-) TimerEvent: {}
[742.031380] (-) TimerEvent: {}
[742.131776] (-) TimerEvent: {}
[742.232683] (-) TimerEvent: {}
[742.333063] (-) TimerEvent: {}
[742.434157] (-) TimerEvent: {}
[742.534639] (-) TimerEvent: {}
[742.635095] (-) TimerEvent: {}
[742.735843] (-) TimerEvent: {}
[742.836596] (-) TimerEvent: {}
[742.937016] (-) TimerEvent: {}
[743.038167] (-) TimerEvent: {}
[743.138629] (-) TimerEvent: {}
[743.239000] (-) TimerEvent: {}
[743.339673] (-) TimerEvent: {}
[743.440061] (-) TimerEvent: {}
[743.540545] (-) TimerEvent: {}
[743.641039] (-) TimerEvent: {}
[743.742154] (-) TimerEvent: {}
[743.842672] (-) TimerEvent: {}
[743.943151] (-) TimerEvent: {}
[744.043810] (-) TimerEvent: {}
[744.144206] (-) TimerEvent: {}
[744.244696] (-) TimerEvent: {}
[744.345095] (-) TimerEvent: {}
[744.445507] (-) TimerEvent: {}
[744.546649] (-) TimerEvent: {}
[744.647075] (-) TimerEvent: {}
[744.747750] (-) TimerEvent: {}
[744.848142] (-) TimerEvent: {}
[744.948662] (-) TimerEvent: {}
[745.049052] (-) TimerEvent: {}
[745.149429] (-) TimerEvent: {}
[745.250175] (-) TimerEvent: {}
[745.350590] (-) TimerEvent: {}
[745.451001] (-) TimerEvent: {}
[745.551395] (-) TimerEvent: {}
[745.651792] (-) TimerEvent: {}
[745.752786] (-) TimerEvent: {}
[745.853165] (-) TimerEvent: {}
[745.954169] (-) TimerEvent: {}
[746.054610] (-) TimerEvent: {}
[746.155053] (-) TimerEvent: {}
[746.255510] (-) TimerEvent: {}
[746.356056] (-) TimerEvent: {}
[746.456605] (-) TimerEvent: {}
[746.557096] (-) TimerEvent: {}
[746.658201] (-) TimerEvent: {}
[746.758681] (-) TimerEvent: {}
[746.859186] (-) TimerEvent: {}
[746.959828] (-) TimerEvent: {}
[747.060892] (-) TimerEvent: {}
[747.161257] (-) TimerEvent: {}
[747.262169] (-) TimerEvent: {}
[747.362609] (-) TimerEvent: {}
[747.391904] (ros1_bridge) StdoutLine: {'line': b'[ 81%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__MultiArrayDimension__factories.cpp.o\x1b[0m\n'}
[747.462818] (-) TimerEvent: {}
[747.563345] (-) TimerEvent: {}
[747.663792] (-) TimerEvent: {}
[747.764213] (-) TimerEvent: {}
[747.864957] (-) TimerEvent: {}
[747.965484] (-) TimerEvent: {}
[748.066173] (-) TimerEvent: {}
[748.166629] (-) TimerEvent: {}
[748.267129] (-) TimerEvent: {}
[748.367989] (-) TimerEvent: {}
[748.410646] (ros1_bridge) StdoutLine: {'line': b'[ 81%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__MultiArrayLayout__factories.cpp.o\x1b[0m\n'}
[748.468099] (-) TimerEvent: {}
[748.568898] (-) TimerEvent: {}
[748.670258] (-) TimerEvent: {}
[748.771177] (-) TimerEvent: {}
[748.871684] (-) TimerEvent: {}
[748.972130] (-) TimerEvent: {}
[749.072582] (-) TimerEvent: {}
[749.173298] (-) TimerEvent: {}
[749.274128] (-) TimerEvent: {}
[749.374501] (-) TimerEvent: {}
[749.474919] (-) TimerEvent: {}
[749.575395] (-) TimerEvent: {}
[749.675885] (-) TimerEvent: {}
[749.776309] (-) TimerEvent: {}
[749.876814] (-) TimerEvent: {}
[749.978145] (-) TimerEvent: {}
[750.078603] (-) TimerEvent: {}
[750.179113] (-) TimerEvent: {}
[750.279601] (-) TimerEvent: {}
[750.380205] (-) TimerEvent: {}
[750.481106] (-) TimerEvent: {}
[750.582177] (-) TimerEvent: {}
[750.682647] (-) TimerEvent: {}
[750.783011] (-) TimerEvent: {}
[750.883500] (-) TimerEvent: {}
[750.984101] (-) TimerEvent: {}
[751.084843] (-) TimerEvent: {}
[751.185313] (-) TimerEvent: {}
[751.286170] (-) TimerEvent: {}
[751.386634] (-) TimerEvent: {}
[751.486966] (-) TimerEvent: {}
[751.587413] (-) TimerEvent: {}
[751.687842] (-) TimerEvent: {}
[751.788247] (-) TimerEvent: {}
[751.889008] (-) TimerEvent: {}
[751.990163] (-) TimerEvent: {}
[752.090586] (-) TimerEvent: {}
[752.191018] (-) TimerEvent: {}
[752.292083] (-) TimerEvent: {}
[752.392422] (-) TimerEvent: {}
[752.492945] (-) TimerEvent: {}
[752.593374] (-) TimerEvent: {}
[752.694168] (-) TimerEvent: {}
[752.794543] (-) TimerEvent: {}
[752.895129] (-) TimerEvent: {}
[752.996161] (-) TimerEvent: {}
[753.097107] (-) TimerEvent: {}
[753.198268] (-) TimerEvent: {}
[753.298766] (-) TimerEvent: {}
[753.399191] (-) TimerEvent: {}
[753.499596] (-) TimerEvent: {}
[753.600250] (-) TimerEvent: {}
[753.701808] (-) TimerEvent: {}
[753.802419] (-) TimerEvent: {}
[753.902830] (-) TimerEvent: {}
[754.003215] (-) TimerEvent: {}
[754.103633] (-) TimerEvent: {}
[754.204041] (-) TimerEvent: {}
[754.304490] (-) TimerEvent: {}
[754.405320] (-) TimerEvent: {}
[754.506164] (-) TimerEvent: {}
[754.606563] (-) TimerEvent: {}
[754.706937] (-) TimerEvent: {}
[754.807303] (-) TimerEvent: {}
[754.907932] (-) TimerEvent: {}
[755.008342] (-) TimerEvent: {}
[755.108798] (-) TimerEvent: {}
[755.209228] (-) TimerEvent: {}
[755.310175] (-) TimerEvent: {}
[755.410627] (-) TimerEvent: {}
[755.510965] (-) TimerEvent: {}
[755.611385] (-) TimerEvent: {}
[755.712196] (-) TimerEvent: {}
[755.813138] (-) TimerEvent: {}
[755.914209] (-) TimerEvent: {}
[756.015197] (-) TimerEvent: {}
[756.115685] (-) TimerEvent: {}
[756.216038] (-) TimerEvent: {}
[756.316472] (-) TimerEvent: {}
[756.416812] (-) TimerEvent: {}
[756.517113] (-) TimerEvent: {}
[756.618154] (-) TimerEvent: {}
[756.718891] (-) TimerEvent: {}
[756.819202] (-) TimerEvent: {}
[756.920003] (-) TimerEvent: {}
[756.940812] (ros1_bridge) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__String__factories.cpp.o\x1b[0m\n'}
[757.020149] (-) TimerEvent: {}
[757.120612] (-) TimerEvent: {}
[757.220935] (-) TimerEvent: {}
[757.321298] (-) TimerEvent: {}
[757.422543] (-) TimerEvent: {}
[757.523786] (-) TimerEvent: {}
[757.624250] (-) TimerEvent: {}
[757.724829] (-) TimerEvent: {}
[757.825285] (-) TimerEvent: {}
[757.926342] (-) TimerEvent: {}
[758.026778] (-) TimerEvent: {}
[758.127455] (-) TimerEvent: {}
[758.227910] (-) TimerEvent: {}
[758.328685] (-) TimerEvent: {}
[758.430155] (-) TimerEvent: {}
[758.530609] (-) TimerEvent: {}
[758.631122] (-) TimerEvent: {}
[758.731816] (-) TimerEvent: {}
[758.832131] (-) TimerEvent: {}
[758.932649] (-) TimerEvent: {}
[759.033426] (-) TimerEvent: {}
[759.134137] (-) TimerEvent: {}
[759.234403] (-) TimerEvent: {}
[759.334728] (-) TimerEvent: {}
[759.435081] (-) TimerEvent: {}
[759.535489] (-) TimerEvent: {}
[759.635857] (-) TimerEvent: {}
[759.736301] (-) TimerEvent: {}
[759.836877] (-) TimerEvent: {}
[759.937529] (-) TimerEvent: {}
[760.038472] (-) TimerEvent: {}
[760.139263] (-) TimerEvent: {}
[760.239609] (-) TimerEvent: {}
[760.339854] (-) TimerEvent: {}
[760.440102] (-) TimerEvent: {}
[760.540366] (-) TimerEvent: {}
[760.640628] (-) TimerEvent: {}
[760.740883] (-) TimerEvent: {}
[760.841176] (-) TimerEvent: {}
[760.942125] (-) TimerEvent: {}
[761.043186] (-) TimerEvent: {}
[761.143989] (-) TimerEvent: {}
[761.244295] (-) TimerEvent: {}
[761.344967] (-) TimerEvent: {}
[761.446144] (-) TimerEvent: {}
[761.546430] (-) TimerEvent: {}
[761.646751] (-) TimerEvent: {}
[761.747092] (-) TimerEvent: {}
[761.847367] (-) TimerEvent: {}
[761.947728] (-) TimerEvent: {}
[762.048028] (-) TimerEvent: {}
[762.148297] (-) TimerEvent: {}
[762.248859] (-) TimerEvent: {}
[762.350166] (-) TimerEvent: {}
[762.450926] (-) TimerEvent: {}
[762.552026] (-) TimerEvent: {}
[762.652315] (-) TimerEvent: {}
[762.752781] (-) TimerEvent: {}
[762.853091] (-) TimerEvent: {}
[762.954158] (-) TimerEvent: {}
[763.054476] (-) TimerEvent: {}
[763.154899] (-) TimerEvent: {}
[763.255602] (-) TimerEvent: {}
[763.356446] (-) TimerEvent: {}
[763.457268] (-) TimerEvent: {}
[763.558184] (-) TimerEvent: {}
[763.658496] (-) TimerEvent: {}
[763.758933] (-) TimerEvent: {}
[763.859343] (-) TimerEvent: {}
[763.959884] (-) TimerEvent: {}
[763.981460] (ros1_bridge) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt16__factories.cpp.o\x1b[0m\n'}
[764.059957] (-) TimerEvent: {}
[764.160348] (-) TimerEvent: {}
[764.260660] (-) TimerEvent: {}
[764.361031] (-) TimerEvent: {}
[764.462163] (-) TimerEvent: {}
[764.562592] (-) TimerEvent: {}
[764.663474] (-) TimerEvent: {}
[764.764891] (-) TimerEvent: {}
[764.866498] (-) TimerEvent: {}
[764.967618] (-) TimerEvent: {}
[765.067964] (-) TimerEvent: {}
[765.168305] (-) TimerEvent: {}
[765.268708] (-) TimerEvent: {}
[765.369456] (-) TimerEvent: {}
[765.470156] (-) TimerEvent: {}
[765.570448] (-) TimerEvent: {}
[765.671535] (-) TimerEvent: {}
[765.771850] (-) TimerEvent: {}
[765.872356] (-) TimerEvent: {}
[765.972886] (-) TimerEvent: {}
[766.073486] (-) TimerEvent: {}
[766.174189] (-) TimerEvent: {}
[766.274570] (-) TimerEvent: {}
[766.374933] (-) TimerEvent: {}
[766.475438] (-) TimerEvent: {}
[766.575891] (-) TimerEvent: {}
[766.676530] (-) TimerEvent: {}
[766.776847] (-) TimerEvent: {}
[766.877143] (-) TimerEvent: {}
[766.977549] (-) TimerEvent: {}
[767.078149] (-) TimerEvent: {}
[767.178499] (-) TimerEvent: {}
[767.278762] (-) TimerEvent: {}
[767.379332] (-) TimerEvent: {}
[767.480288] (-) TimerEvent: {}
[767.581405] (-) TimerEvent: {}
[767.682439] (-) TimerEvent: {}
[767.783256] (-) TimerEvent: {}
[767.883720] (-) TimerEvent: {}
[767.984038] (-) TimerEvent: {}
[768.084465] (-) TimerEvent: {}
[768.185137] (-) TimerEvent: {}
[768.286446] (-) TimerEvent: {}
[768.387208] (-) TimerEvent: {}
[768.487549] (-) TimerEvent: {}
[768.587825] (-) TimerEvent: {}
[768.688665] (-) TimerEvent: {}
[768.789130] (-) TimerEvent: {}
[768.890156] (-) TimerEvent: {}
[768.990939] (-) TimerEvent: {}
[769.091609] (-) TimerEvent: {}
[769.191900] (-) TimerEvent: {}
[769.292361] (-) TimerEvent: {}
[769.393107] (-) TimerEvent: {}
[769.493355] (-) TimerEvent: {}
[769.594226] (-) TimerEvent: {}
[769.694729] (-) TimerEvent: {}
[769.795132] (-) TimerEvent: {}
[769.895662] (-) TimerEvent: {}
[769.995918] (-) TimerEvent: {}
[770.096524] (-) TimerEvent: {}
[770.197428] (-) TimerEvent: {}
[770.299418] (-) TimerEvent: {}
[770.400882] (-) TimerEvent: {}
[770.501655] (-) TimerEvent: {}
[770.603193] (-) TimerEvent: {}
[770.703544] (-) TimerEvent: {}
[770.803890] (-) TimerEvent: {}
[770.904294] (-) TimerEvent: {}
[771.004769] (-) TimerEvent: {}
[771.021879] (ros1_bridge) StdoutLine: {'line': b'[ 82%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt16MultiArray__factories.cpp.o\x1b[0m\n'}
[771.104863] (-) TimerEvent: {}
[771.205456] (-) TimerEvent: {}
[771.306221] (-) TimerEvent: {}
[771.406671] (-) TimerEvent: {}
[771.507437] (-) TimerEvent: {}
[771.609165] (-) TimerEvent: {}
[771.710363] (-) TimerEvent: {}
[771.811470] (-) TimerEvent: {}
[771.911807] (-) TimerEvent: {}
[772.012148] (-) TimerEvent: {}
[772.112563] (-) TimerEvent: {}
[772.212942] (-) TimerEvent: {}
[772.313302] (-) TimerEvent: {}
[772.414146] (-) TimerEvent: {}
[772.514428] (-) TimerEvent: {}
[772.614759] (-) TimerEvent: {}
[772.715086] (-) TimerEvent: {}
[772.815433] (-) TimerEvent: {}
[772.915761] (-) TimerEvent: {}
[773.016393] (-) TimerEvent: {}
[773.118213] (-) TimerEvent: {}
[773.218644] (-) TimerEvent: {}
[773.318910] (-) TimerEvent: {}
[773.419191] (-) TimerEvent: {}
[773.519486] (-) TimerEvent: {}
[773.619927] (-) TimerEvent: {}
[773.720753] (-) TimerEvent: {}
[773.821266] (-) TimerEvent: {}
[773.922124] (-) TimerEvent: {}
[774.022912] (-) TimerEvent: {}
[774.123244] (-) TimerEvent: {}
[774.223854] (-) TimerEvent: {}
[774.325553] (-) TimerEvent: {}
[774.426944] (-) TimerEvent: {}
[774.527208] (-) TimerEvent: {}
[774.627495] (-) TimerEvent: {}
[774.727837] (-) TimerEvent: {}
[774.828223] (-) TimerEvent: {}
[774.929033] (-) TimerEvent: {}
[775.030467] (-) TimerEvent: {}
[775.131548] (-) TimerEvent: {}
[775.232279] (-) TimerEvent: {}
[775.333120] (-) TimerEvent: {}
[775.433474] (-) TimerEvent: {}
[775.534147] (-) TimerEvent: {}
[775.635038] (-) TimerEvent: {}
[775.735751] (-) TimerEvent: {}
[775.836090] (-) TimerEvent: {}
[775.936616] (-) TimerEvent: {}
[776.037361] (-) TimerEvent: {}
[776.040354] (ros1_bridge) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt32__factories.cpp.o\x1b[0m\n'}
[776.137480] (-) TimerEvent: {}
[776.238511] (-) TimerEvent: {}
[776.339251] (-) TimerEvent: {}
[776.439615] (-) TimerEvent: {}
[776.540010] (-) TimerEvent: {}
[776.640892] (-) TimerEvent: {}
[776.741190] (-) TimerEvent: {}
[776.842181] (-) TimerEvent: {}
[776.942572] (-) TimerEvent: {}
[777.042960] (-) TimerEvent: {}
[777.143343] (-) TimerEvent: {}
[777.243768] (-) TimerEvent: {}
[777.344139] (-) TimerEvent: {}
[777.444916] (-) TimerEvent: {}
[777.545302] (-) TimerEvent: {}
[777.646171] (-) TimerEvent: {}
[777.747133] (-) TimerEvent: {}
[777.847726] (-) TimerEvent: {}
[777.948101] (-) TimerEvent: {}
[778.048588] (-) TimerEvent: {}
[778.150188] (-) TimerEvent: {}
[778.250493] (-) TimerEvent: {}
[778.350967] (-) TimerEvent: {}
[778.451403] (-) TimerEvent: {}
[778.551827] (-) TimerEvent: {}
[778.624410] (ros1_bridge) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt32MultiArray__factories.cpp.o\x1b[0m\n'}
[778.651905] (-) TimerEvent: {}
[778.752278] (-) TimerEvent: {}
[778.852699] (-) TimerEvent: {}
[778.953111] (-) TimerEvent: {}
[779.054158] (-) TimerEvent: {}
[779.154549] (-) TimerEvent: {}
[779.254997] (-) TimerEvent: {}
[779.355373] (-) TimerEvent: {}
[779.455807] (-) TimerEvent: {}
[779.556823] (-) TimerEvent: {}
[779.658157] (-) TimerEvent: {}
[779.758644] (-) TimerEvent: {}
[779.859081] (-) TimerEvent: {}
[779.959872] (-) TimerEvent: {}
[780.060915] (-) TimerEvent: {}
[780.162160] (-) TimerEvent: {}
[780.262548] (-) TimerEvent: {}
[780.362946] (-) TimerEvent: {}
[780.463291] (-) TimerEvent: {}
[780.563771] (-) TimerEvent: {}
[780.664264] (-) TimerEvent: {}
[780.765157] (-) TimerEvent: {}
[780.866168] (-) TimerEvent: {}
[780.966690] (-) TimerEvent: {}
[781.067223] (-) TimerEvent: {}
[781.167793] (-) TimerEvent: {}
[781.268743] (-) TimerEvent: {}
[781.370164] (-) TimerEvent: {}
[781.470622] (-) TimerEvent: {}
[781.571064] (-) TimerEvent: {}
[781.671562] (-) TimerEvent: {}
[781.772061] (-) TimerEvent: {}
[781.872527] (-) TimerEvent: {}
[781.973218] (-) TimerEvent: {}
[782.074172] (-) TimerEvent: {}
[782.174645] (-) TimerEvent: {}
[782.275104] (-) TimerEvent: {}
[782.375460] (-) TimerEvent: {}
[782.475949] (-) TimerEvent: {}
[782.576450] (-) TimerEvent: {}
[782.676925] (-) TimerEvent: {}
[782.778131] (-) TimerEvent: {}
[782.878481] (-) TimerEvent: {}
[782.979076] (-) TimerEvent: {}
[783.079417] (-) TimerEvent: {}
[783.179916] (-) TimerEvent: {}
[783.280218] (-) TimerEvent: {}
[783.380576] (-) TimerEvent: {}
[783.481237] (-) TimerEvent: {}
[783.582279] (-) TimerEvent: {}
[783.682644] (-) TimerEvent: {}
[783.783046] (-) TimerEvent: {}
[783.883446] (-) TimerEvent: {}
[783.983842] (-) TimerEvent: {}
[784.084204] (-) TimerEvent: {}
[784.185005] (-) TimerEvent: {}
[784.279432] (ros1_bridge) StdoutLine: {'line': b'[ 83%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt64__factories.cpp.o\x1b[0m\n'}
[784.285077] (-) TimerEvent: {}
[784.386202] (-) TimerEvent: {}
[784.486605] (-) TimerEvent: {}
[784.587361] (-) TimerEvent: {}
[784.687812] (-) TimerEvent: {}
[784.788241] (-) TimerEvent: {}
[784.890281] (-) TimerEvent: {}
[784.990738] (-) TimerEvent: {}
[785.091372] (-) TimerEvent: {}
[785.191774] (-) TimerEvent: {}
[785.292195] (-) TimerEvent: {}
[785.393018] (-) TimerEvent: {}
[785.493420] (-) TimerEvent: {}
[785.594158] (-) TimerEvent: {}
[785.694549] (-) TimerEvent: {}
[785.795005] (-) TimerEvent: {}
[785.895468] (-) TimerEvent: {}
[785.995925] (-) TimerEvent: {}
[786.096271] (-) TimerEvent: {}
[786.196572] (-) TimerEvent: {}
[786.297040] (-) TimerEvent: {}
[786.397423] (-) TimerEvent: {}
[786.498167] (-) TimerEvent: {}
[786.598515] (-) TimerEvent: {}
[786.699103] (-) TimerEvent: {}
[786.799522] (-) TimerEvent: {}
[786.899916] (-) TimerEvent: {}
[786.904009] (ros1_bridge) StdoutLine: {'line': b'[ 84%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt64MultiArray__factories.cpp.o\x1b[0m\n'}
[787.000011] (-) TimerEvent: {}
[787.100580] (-) TimerEvent: {}
[787.201020] (-) TimerEvent: {}
[787.302149] (-) TimerEvent: {}
[787.402691] (-) TimerEvent: {}
[787.503147] (-) TimerEvent: {}
[787.603992] (-) TimerEvent: {}
[787.704937] (-) TimerEvent: {}
[787.805416] (-) TimerEvent: {}
[787.906149] (-) TimerEvent: {}
[788.007188] (-) TimerEvent: {}
[788.107699] (-) TimerEvent: {}
[788.208139] (-) TimerEvent: {}
[788.308491] (-) TimerEvent: {}
[788.409119] (-) TimerEvent: {}
[788.509603] (-) TimerEvent: {}
[788.610164] (-) TimerEvent: {}
[788.710564] (-) TimerEvent: {}
[788.811203] (-) TimerEvent: {}
[788.911672] (-) TimerEvent: {}
[789.012484] (-) TimerEvent: {}
[789.113089] (-) TimerEvent: {}
[789.214157] (-) TimerEvent: {}
[789.314514] (-) TimerEvent: {}
[789.414828] (-) TimerEvent: {}
[789.515143] (-) TimerEvent: {}
[789.615518] (-) TimerEvent: {}
[789.715900] (-) TimerEvent: {}
[789.816575] (-) TimerEvent: {}
[789.918169] (-) TimerEvent: {}
[790.018581] (-) TimerEvent: {}
[790.119003] (-) TimerEvent: {}
[790.219375] (-) TimerEvent: {}
[790.320616] (-) TimerEvent: {}
[790.420982] (-) TimerEvent: {}
[790.521344] (-) TimerEvent: {}
[790.622173] (-) TimerEvent: {}
[790.722560] (-) TimerEvent: {}
[790.823273] (-) TimerEvent: {}
[790.923679] (-) TimerEvent: {}
[791.024036] (-) TimerEvent: {}
[791.124941] (-) TimerEvent: {}
[791.225434] (-) TimerEvent: {}
[791.326421] (-) TimerEvent: {}
[791.426760] (-) TimerEvent: {}
[791.527115] (-) TimerEvent: {}
[791.627454] (-) TimerEvent: {}
[791.727826] (-) TimerEvent: {}
[791.828212] (-) TimerEvent: {}
[791.928591] (-) TimerEvent: {}
[792.028960] (-) TimerEvent: {}
[792.130152] (-) TimerEvent: {}
[792.230541] (-) TimerEvent: {}
[792.330874] (-) TimerEvent: {}
[792.431198] (-) TimerEvent: {}
[792.473196] (ros1_bridge) StdoutLine: {'line': b'[ 84%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt8__factories.cpp.o\x1b[0m\n'}
[792.531295] (-) TimerEvent: {}
[792.631712] (-) TimerEvent: {}
[792.732131] (-) TimerEvent: {}
[792.832893] (-) TimerEvent: {}
[792.933545] (-) TimerEvent: {}
[793.034165] (-) TimerEvent: {}
[793.134538] (-) TimerEvent: {}
[793.235041] (-) TimerEvent: {}
[793.335404] (-) TimerEvent: {}
[793.435791] (-) TimerEvent: {}
[793.536229] (-) TimerEvent: {}
[793.636803] (-) TimerEvent: {}
[793.737400] (-) TimerEvent: {}
[793.838147] (-) TimerEvent: {}
[793.938454] (-) TimerEvent: {}
[794.038858] (-) TimerEvent: {}
[794.139379] (-) TimerEvent: {}
[794.239754] (-) TimerEvent: {}
[794.340354] (-) TimerEvent: {}
[794.440766] (-) TimerEvent: {}
[794.541342] (-) TimerEvent: {}
[794.642150] (-) TimerEvent: {}
[794.743058] (-) TimerEvent: {}
[794.843584] (-) TimerEvent: {}
[794.943993] (-) TimerEvent: {}
[795.044934] (-) TimerEvent: {}
[795.145364] (-) TimerEvent: {}
[795.197559] (ros1_bridge) StdoutLine: {'line': b'[ 84%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt8MultiArray__factories.cpp.o\x1b[0m\n'}
[795.245475] (-) TimerEvent: {}
[795.346354] (-) TimerEvent: {}
[795.446760] (-) TimerEvent: {}
[795.547285] (-) TimerEvent: {}
[795.647775] (-) TimerEvent: {}
[795.748159] (-) TimerEvent: {}
[795.849082] (-) TimerEvent: {}
[795.950178] (-) TimerEvent: {}
[796.050613] (-) TimerEvent: {}
[796.151264] (-) TimerEvent: {}
[796.251817] (-) TimerEvent: {}
[796.352208] (-) TimerEvent: {}
[796.452863] (-) TimerEvent: {}
[796.553277] (-) TimerEvent: {}
[796.654165] (-) TimerEvent: {}
[796.754612] (-) TimerEvent: {}
[796.855232] (-) TimerEvent: {}
[796.955633] (-) TimerEvent: {}
[797.056049] (-) TimerEvent: {}
[797.156426] (-) TimerEvent: {}
[797.256904] (-) TimerEvent: {}
[797.357447] (-) TimerEvent: {}
[797.458155] (-) TimerEvent: {}
[797.558539] (-) TimerEvent: {}
[797.658974] (-) TimerEvent: {}
[797.759310] (-) TimerEvent: {}
[797.859632] (-) TimerEvent: {}
[797.959999] (-) TimerEvent: {}
[798.060833] (-) TimerEvent: {}
[798.161504] (-) TimerEvent: {}
[798.262139] (-) TimerEvent: {}
[798.362498] (-) TimerEvent: {}
[798.462995] (-) TimerEvent: {}
[798.563514] (-) TimerEvent: {}
[798.663951] (-) TimerEvent: {}
[798.764259] (-) TimerEvent: {}
[798.865072] (-) TimerEvent: {}
[798.965521] (-) TimerEvent: {}
[799.066176] (-) TimerEvent: {}
[799.166548] (-) TimerEvent: {}
[799.266956] (-) TimerEvent: {}
[799.367491] (-) TimerEvent: {}
[799.467878] (-) TimerEvent: {}
[799.568242] (-) TimerEvent: {}
[799.668621] (-) TimerEvent: {}
[799.769419] (-) TimerEvent: {}
[799.870158] (-) TimerEvent: {}
[799.970569] (-) TimerEvent: {}
[800.070941] (-) TimerEvent: {}
[800.171416] (-) TimerEvent: {}
[800.271778] (-) TimerEvent: {}
[800.372153] (-) TimerEvent: {}
[800.472808] (-) TimerEvent: {}
[800.590484] (-) TimerEvent: {}
[800.666983] (ros1_bridge) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs_factories.cpp.o\x1b[0m\n'}
[800.690590] (-) TimerEvent: {}
[800.790995] (-) TimerEvent: {}
[800.891395] (-) TimerEvent: {}
[800.991792] (-) TimerEvent: {}
[801.092187] (-) TimerEvent: {}
[801.192722] (-) TimerEvent: {}
[801.293129] (-) TimerEvent: {}
[801.393543] (-) TimerEvent: {}
[801.494155] (-) TimerEvent: {}
[801.594604] (-) TimerEvent: {}
[801.695064] (-) TimerEvent: {}
[801.795573] (-) TimerEvent: {}
[801.896410] (-) TimerEvent: {}
[801.996855] (-) TimerEvent: {}
[802.098173] (-) TimerEvent: {}
[802.198636] (-) TimerEvent: {}
[802.303474] (-) TimerEvent: {}
[802.403875] (-) TimerEvent: {}
[802.504409] (-) TimerEvent: {}
[802.604867] (-) TimerEvent: {}
[802.705361] (-) TimerEvent: {}
[802.806149] (-) TimerEvent: {}
[802.906567] (-) TimerEvent: {}
[803.007097] (-) TimerEvent: {}
[803.107558] (-) TimerEvent: {}
[803.208076] (-) TimerEvent: {}
[803.308872] (-) TimerEvent: {}
[803.410157] (-) TimerEvent: {}
[803.438056] (ros1_bridge) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__Empty__factories.cpp.o\x1b[0m\n'}
[803.510493] (-) TimerEvent: {}
[803.610957] (-) TimerEvent: {}
[803.711453] (-) TimerEvent: {}
[803.811898] (-) TimerEvent: {}
[803.912406] (-) TimerEvent: {}
[804.013050] (-) TimerEvent: {}
[804.113522] (-) TimerEvent: {}
[804.214149] (-) TimerEvent: {}
[804.295752] (ros1_bridge) StdoutLine: {'line': b'[ 85%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__SetBool__factories.cpp.o\x1b[0m\n'}
[804.314348] (-) TimerEvent: {}
[804.414866] (-) TimerEvent: {}
[804.515362] (-) TimerEvent: {}
[804.615884] (-) TimerEvent: {}
[804.716380] (-) TimerEvent: {}
[804.816891] (-) TimerEvent: {}
[804.917358] (-) TimerEvent: {}
[805.018589] (-) TimerEvent: {}
[805.119203] (-) TimerEvent: {}
[805.220211] (-) TimerEvent: {}
[805.320824] (-) TimerEvent: {}
[805.421386] (-) TimerEvent: {}
[805.522152] (-) TimerEvent: {}
[805.622590] (-) TimerEvent: {}
[805.723059] (-) TimerEvent: {}
[805.823623] (-) TimerEvent: {}
[805.924150] (-) TimerEvent: {}
[806.024657] (-) TimerEvent: {}
[806.126146] (-) TimerEvent: {}
[806.226631] (-) TimerEvent: {}
[806.327068] (-) TimerEvent: {}
[806.427523] (-) TimerEvent: {}
[806.527980] (-) TimerEvent: {}
[806.628917] (-) TimerEvent: {}
[806.730161] (-) TimerEvent: {}
[806.830636] (-) TimerEvent: {}
[806.931101] (-) TimerEvent: {}
[807.031672] (-) TimerEvent: {}
[807.132122] (-) TimerEvent: {}
[807.232834] (-) TimerEvent: {}
[807.334161] (-) TimerEvent: {}
[807.434614] (-) TimerEvent: {}
[807.535085] (-) TimerEvent: {}
[807.635523] (-) TimerEvent: {}
[807.736009] (-) TimerEvent: {}
[807.836913] (-) TimerEvent: {}
[807.938180] (-) TimerEvent: {}
[808.038690] (-) TimerEvent: {}
[808.139284] (-) TimerEvent: {}
[808.239733] (-) TimerEvent: {}
[808.340222] (-) TimerEvent: {}
[808.441066] (-) TimerEvent: {}
[808.541477] (-) TimerEvent: {}
[808.642250] (-) TimerEvent: {}
[808.742640] (-) TimerEvent: {}
[808.843419] (-) TimerEvent: {}
[808.943950] (-) TimerEvent: {}
[809.044759] (-) TimerEvent: {}
[809.062281] (ros1_bridge) StdoutLine: {'line': b'[ 86%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__Trigger__factories.cpp.o\x1b[0m\n'}
[809.144856] (-) TimerEvent: {}
[809.245358] (-) TimerEvent: {}
[809.346388] (-) TimerEvent: {}
[809.446905] (-) TimerEvent: {}
[809.547344] (-) TimerEvent: {}
[809.647794] (-) TimerEvent: {}
[809.715311] (ros1_bridge) StdoutLine: {'line': b'[ 86%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/stereo_msgs_factories.cpp.o\x1b[0m\n'}
[809.747887] (-) TimerEvent: {}
[809.848358] (-) TimerEvent: {}
[809.948757] (-) TimerEvent: {}
[810.050188] (-) TimerEvent: {}
[810.150869] (-) TimerEvent: {}
[810.251309] (-) TimerEvent: {}
[810.351985] (-) TimerEvent: {}
[810.452817] (-) TimerEvent: {}
[810.554149] (-) TimerEvent: {}
[810.654607] (-) TimerEvent: {}
[810.755296] (-) TimerEvent: {}
[810.855631] (-) TimerEvent: {}
[810.956070] (-) TimerEvent: {}
[811.056804] (-) TimerEvent: {}
[811.158146] (-) TimerEvent: {}
[811.258619] (-) TimerEvent: {}
[811.359075] (-) TimerEvent: {}
[811.459873] (-) TimerEvent: {}
[811.560356] (-) TimerEvent: {}
[811.660789] (-) TimerEvent: {}
[811.762162] (-) TimerEvent: {}
[811.862627] (-) TimerEvent: {}
[811.963063] (-) TimerEvent: {}
[812.063704] (-) TimerEvent: {}
[812.164177] (-) TimerEvent: {}
[812.264817] (-) TimerEvent: {}
[812.365329] (-) TimerEvent: {}
[812.466157] (-) TimerEvent: {}
[812.566571] (-) TimerEvent: {}
[812.667110] (-) TimerEvent: {}
[812.767626] (-) TimerEvent: {}
[812.868253] (-) TimerEvent: {}
[812.969088] (-) TimerEvent: {}
[813.070196] (-) TimerEvent: {}
[813.170689] (-) TimerEvent: {}
[813.271156] (-) TimerEvent: {}
[813.371683] (-) TimerEvent: {}
[813.472171] (-) TimerEvent: {}
[813.573083] (-) TimerEvent: {}
[813.616665] (ros1_bridge) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/stereo_msgs__msg__DisparityImage__factories.cpp.o\x1b[0m\n'}
[813.673224] (-) TimerEvent: {}
[813.774230] (-) TimerEvent: {}
[813.874656] (-) TimerEvent: {}
[813.975073] (-) TimerEvent: {}
[814.075499] (-) TimerEvent: {}
[814.175933] (-) TimerEvent: {}
[814.276893] (-) TimerEvent: {}
[814.377491] (-) TimerEvent: {}
[814.478156] (-) TimerEvent: {}
[814.578613] (-) TimerEvent: {}
[814.679118] (-) TimerEvent: {}
[814.738593] (ros1_bridge) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs_factories.cpp.o\x1b[0m\n'}
[814.779194] (-) TimerEvent: {}
[814.879623] (-) TimerEvent: {}
[814.980019] (-) TimerEvent: {}
[815.081152] (-) TimerEvent: {}
[815.182164] (-) TimerEvent: {}
[815.282622] (-) TimerEvent: {}
[815.383424] (-) TimerEvent: {}
[815.484046] (-) TimerEvent: {}
[815.584859] (-) TimerEvent: {}
[815.685314] (-) TimerEvent: {}
[815.786211] (-) TimerEvent: {}
[815.886660] (-) TimerEvent: {}
[815.987134] (-) TimerEvent: {}
[816.087713] (-) TimerEvent: {}
[816.188219] (-) TimerEvent: {}
[816.288920] (-) TimerEvent: {}
[816.390153] (-) TimerEvent: {}
[816.490564] (-) TimerEvent: {}
[816.591105] (-) TimerEvent: {}
[816.691955] (-) TimerEvent: {}
[816.793125] (-) TimerEvent: {}
[816.894269] (-) TimerEvent: {}
[816.994760] (-) TimerEvent: {}
[817.095323] (-) TimerEvent: {}
[817.195954] (-) TimerEvent: {}
[817.296531] (-) TimerEvent: {}
[817.397071] (-) TimerEvent: {}
[817.497596] (-) TimerEvent: {}
[817.598678] (-) TimerEvent: {}
[817.699560] (-) TimerEvent: {}
[817.800517] (-) TimerEvent: {}
[817.900985] (-) TimerEvent: {}
[818.002153] (-) TimerEvent: {}
[818.102639] (-) TimerEvent: {}
[818.203448] (-) TimerEvent: {}
[818.304025] (-) TimerEvent: {}
[818.404409] (-) TimerEvent: {}
[818.504941] (-) TimerEvent: {}
[818.606222] (-) TimerEvent: {}
[818.706834] (-) TimerEvent: {}
[818.807314] (-) TimerEvent: {}
[818.875350] (ros1_bridge) StdoutLine: {'line': b'[ 87%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__msg__TF2Error__factories.cpp.o\x1b[0m\n'}
[818.907395] (-) TimerEvent: {}
[819.007803] (-) TimerEvent: {}
[819.108520] (-) TimerEvent: {}
[819.209157] (-) TimerEvent: {}
[819.310201] (-) TimerEvent: {}
[819.410654] (-) TimerEvent: {}
[819.511054] (-) TimerEvent: {}
[819.611532] (-) TimerEvent: {}
[819.712077] (-) TimerEvent: {}
[819.812763] (-) TimerEvent: {}
[819.913205] (-) TimerEvent: {}
[820.014163] (-) TimerEvent: {}
[820.114598] (-) TimerEvent: {}
[820.215095] (-) TimerEvent: {}
[820.315637] (-) TimerEvent: {}
[820.416128] (-) TimerEvent: {}
[820.516955] (-) TimerEvent: {}
[820.618191] (-) TimerEvent: {}
[820.718729] (-) TimerEvent: {}
[820.819574] (-) TimerEvent: {}
[820.920067] (-) TimerEvent: {}
[821.020974] (-) TimerEvent: {}
[821.122159] (-) TimerEvent: {}
[821.222727] (-) TimerEvent: {}
[821.323198] (-) TimerEvent: {}
[821.423693] (-) TimerEvent: {}
[821.524136] (-) TimerEvent: {}
[821.625014] (-) TimerEvent: {}
[821.725357] (-) TimerEvent: {}
[821.826153] (-) TimerEvent: {}
[821.926546] (-) TimerEvent: {}
[821.972679] (ros1_bridge) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__msg__TFMessage__factories.cpp.o\x1b[0m\n'}
[822.026643] (-) TimerEvent: {}
[822.127099] (-) TimerEvent: {}
[822.227763] (-) TimerEvent: {}
[822.328204] (-) TimerEvent: {}
[822.429112] (-) TimerEvent: {}
[822.530193] (-) TimerEvent: {}
[822.630748] (-) TimerEvent: {}
[822.731267] (-) TimerEvent: {}
[822.831793] (-) TimerEvent: {}
[822.932250] (-) TimerEvent: {}
[823.032691] (-) TimerEvent: {}
[823.133154] (-) TimerEvent: {}
[823.234171] (-) TimerEvent: {}
[823.334643] (-) TimerEvent: {}
[823.435142] (-) TimerEvent: {}
[823.535613] (-) TimerEvent: {}
[823.636215] (-) TimerEvent: {}
[823.737120] (-) TimerEvent: {}
[823.837553] (-) TimerEvent: {}
[823.938169] (-) TimerEvent: {}
[824.038636] (-) TimerEvent: {}
[824.139285] (-) TimerEvent: {}
[824.239772] (-) TimerEvent: {}
[824.340276] (-) TimerEvent: {}
[824.440992] (-) TimerEvent: {}
[824.542191] (-) TimerEvent: {}
[824.642695] (-) TimerEvent: {}
[824.743126] (-) TimerEvent: {}
[824.843615] (-) TimerEvent: {}
[824.944076] (-) TimerEvent: {}
[825.044518] (-) TimerEvent: {}
[825.145317] (-) TimerEvent: {}
[825.246187] (-) TimerEvent: {}
[825.346722] (-) TimerEvent: {}
[825.447353] (-) TimerEvent: {}
[825.547899] (-) TimerEvent: {}
[825.648612] (-) TimerEvent: {}
[825.749194] (-) TimerEvent: {}
[825.850157] (-) TimerEvent: {}
[825.950639] (-) TimerEvent: {}
[826.051207] (-) TimerEvent: {}
[826.151680] (-) TimerEvent: {}
[826.252164] (-) TimerEvent: {}
[826.353167] (-) TimerEvent: {}
[826.454169] (-) TimerEvent: {}
[826.554582] (-) TimerEvent: {}
[826.655161] (-) TimerEvent: {}
[826.755624] (-) TimerEvent: {}
[826.856072] (-) TimerEvent: {}
[826.956529] (-) TimerEvent: {}
[827.057480] (-) TimerEvent: {}
[827.158472] (-) TimerEvent: {}
[827.211607] (ros1_bridge) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__srv__FrameGraph__factories.cpp.o\x1b[0m\n'}
[827.258550] (-) TimerEvent: {}
[827.359175] (-) TimerEvent: {}
[827.460054] (-) TimerEvent: {}
[827.560602] (-) TimerEvent: {}
[827.661035] (-) TimerEvent: {}
[827.761504] (-) TimerEvent: {}
[827.862206] (-) TimerEvent: {}
[827.962627] (-) TimerEvent: {}
[828.063530] (-) TimerEvent: {}
[828.164009] (-) TimerEvent: {}
[828.264456] (-) TimerEvent: {}
[828.364861] (-) TimerEvent: {}
[828.465338] (-) TimerEvent: {}
[828.566154] (-) TimerEvent: {}
[828.666912] (-) TimerEvent: {}
[828.767402] (-) TimerEvent: {}
[828.867880] (-) TimerEvent: {}
[828.968938] (-) TimerEvent: {}
[829.069522] (-) TimerEvent: {}
[829.170240] (-) TimerEvent: {}
[829.270728] (-) TimerEvent: {}
[829.371334] (-) TimerEvent: {}
[829.471779] (-) TimerEvent: {}
[829.572227] (-) TimerEvent: {}
[829.672814] (-) TimerEvent: {}
[829.774167] (-) TimerEvent: {}
[829.874639] (-) TimerEvent: {}
[829.975110] (-) TimerEvent: {}
[830.075543] (-) TimerEvent: {}
[830.175997] (-) TimerEvent: {}
[830.276438] (-) TimerEvent: {}
[830.377407] (-) TimerEvent: {}
[830.478161] (-) TimerEvent: {}
[830.578557] (-) TimerEvent: {}
[830.678911] (-) TimerEvent: {}
[830.729519] (ros1_bridge) StdoutLine: {'line': b'[ 88%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs_factories.cpp.o\x1b[0m\n'}
[830.779029] (-) TimerEvent: {}
[830.879861] (-) TimerEvent: {}
[830.980272] (-) TimerEvent: {}
[831.080890] (-) TimerEvent: {}
[831.181296] (-) TimerEvent: {}
[831.282161] (-) TimerEvent: {}
[831.382608] (-) TimerEvent: {}
[831.483035] (-) TimerEvent: {}
[831.583448] (-) TimerEvent: {}
[831.683881] (-) TimerEvent: {}
[831.784365] (-) TimerEvent: {}
[831.885014] (-) TimerEvent: {}
[831.986172] (-) TimerEvent: {}
[832.086653] (-) TimerEvent: {}
[832.187106] (-) TimerEvent: {}
[832.288018] (-) TimerEvent: {}
[832.388475] (-) TimerEvent: {}
[832.488948] (-) TimerEvent: {}
[832.589440] (-) TimerEvent: {}
[832.690182] (-) TimerEvent: {}
[832.790760] (-) TimerEvent: {}
[832.863894] (ros1_bridge) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__JointTrajectory__factories.cpp.o\x1b[0m\n'}
[832.890872] (-) TimerEvent: {}
[832.991469] (-) TimerEvent: {}
[833.091891] (-) TimerEvent: {}
[833.192961] (-) TimerEvent: {}
[833.294157] (-) TimerEvent: {}
[833.394632] (-) TimerEvent: {}
[833.495070] (-) TimerEvent: {}
[833.595514] (-) TimerEvent: {}
[833.695931] (-) TimerEvent: {}
[833.796363] (-) TimerEvent: {}
[833.896819] (-) TimerEvent: {}
[833.997286] (-) TimerEvent: {}
[834.098276] (-) TimerEvent: {}
[834.198742] (-) TimerEvent: {}
[834.299721] (-) TimerEvent: {}
[834.400290] (-) TimerEvent: {}
[834.500945] (-) TimerEvent: {}
[834.575169] (ros1_bridge) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__JointTrajectoryPoint__factories.cpp.o\x1b[0m\n'}
[834.601034] (-) TimerEvent: {}
[834.701572] (-) TimerEvent: {}
[834.802160] (-) TimerEvent: {}
[834.902608] (-) TimerEvent: {}
[835.003403] (-) TimerEvent: {}
[835.103875] (-) TimerEvent: {}
[835.204778] (-) TimerEvent: {}
[835.305461] (-) TimerEvent: {}
[835.406510] (-) TimerEvent: {}
[835.506942] (-) TimerEvent: {}
[835.607571] (-) TimerEvent: {}
[835.708103] (-) TimerEvent: {}
[835.808743] (-) TimerEvent: {}
[835.909231] (-) TimerEvent: {}
[836.010157] (-) TimerEvent: {}
[836.110660] (-) TimerEvent: {}
[836.211419] (-) TimerEvent: {}
[836.311963] (-) TimerEvent: {}
[836.412961] (-) TimerEvent: {}
[836.513454] (-) TimerEvent: {}
[836.614163] (-) TimerEvent: {}
[836.714703] (-) TimerEvent: {}
[836.815244] (-) TimerEvent: {}
[836.915760] (-) TimerEvent: {}
[837.016725] (-) TimerEvent: {}
[837.117263] (-) TimerEvent: {}
[837.218173] (-) TimerEvent: {}
[837.318914] (-) TimerEvent: {}
[837.419411] (-) TimerEvent: {}
[837.519856] (-) TimerEvent: {}
[837.620295] (-) TimerEvent: {}
[837.720752] (-) TimerEvent: {}
[837.822198] (-) TimerEvent: {}
[837.922773] (-) TimerEvent: {}
[838.023307] (-) TimerEvent: {}
[838.124380] (-) TimerEvent: {}
[838.224837] (-) TimerEvent: {}
[838.326165] (-) TimerEvent: {}
[838.426611] (-) TimerEvent: {}
[838.527062] (-) TimerEvent: {}
[838.627630] (-) TimerEvent: {}
[838.728141] (-) TimerEvent: {}
[838.828616] (-) TimerEvent: {}
[838.929459] (-) TimerEvent: {}
[839.030160] (-) TimerEvent: {}
[839.130611] (-) TimerEvent: {}
[839.231078] (-) TimerEvent: {}
[839.331608] (-) TimerEvent: {}
[839.432115] (-) TimerEvent: {}
[839.532598] (-) TimerEvent: {}
[839.633131] (-) TimerEvent: {}
[839.734189] (-) TimerEvent: {}
[839.835084] (-) TimerEvent: {}
[839.935632] (-) TimerEvent: {}
[840.036045] (-) TimerEvent: {}
[840.136887] (-) TimerEvent: {}
[840.237506] (-) TimerEvent: {}
[840.338190] (-) TimerEvent: {}
[840.438739] (-) TimerEvent: {}
[840.539174] (-) TimerEvent: {}
[840.639680] (-) TimerEvent: {}
[840.740282] (-) TimerEvent: {}
[840.840668] (-) TimerEvent: {}
[840.941024] (-) TimerEvent: {}
[841.041431] (-) TimerEvent: {}
[841.142240] (-) TimerEvent: {}
[841.242611] (-) TimerEvent: {}
[841.343014] (-) TimerEvent: {}
[841.377624] (ros1_bridge) StdoutLine: {'line': b'[ 89%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__MultiDOFJointTrajectory__factories.cpp.o\x1b[0m\n'}
[841.443568] (-) TimerEvent: {}
[841.543929] (-) TimerEvent: {}
[841.644704] (-) TimerEvent: {}
[841.745100] (-) TimerEvent: {}
[841.846171] (-) TimerEvent: {}
[841.946642] (-) TimerEvent: {}
[842.047080] (-) TimerEvent: {}
[842.147714] (-) TimerEvent: {}
[842.248171] (-) TimerEvent: {}
[842.348608] (-) TimerEvent: {}
[842.449077] (-) TimerEvent: {}
[842.550142] (-) TimerEvent: {}
[842.650644] (-) TimerEvent: {}
[842.751075] (-) TimerEvent: {}
[842.851595] (-) TimerEvent: {}
[842.952031] (-) TimerEvent: {}
[843.053283] (-) TimerEvent: {}
[843.149452] (ros1_bridge) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__MultiDOFJointTrajectoryPoint__factories.cpp.o\x1b[0m\n'}
[843.153365] (-) TimerEvent: {}
[843.254192] (-) TimerEvent: {}
[843.354671] (-) TimerEvent: {}
[843.455225] (-) TimerEvent: {}
[843.555566] (-) TimerEvent: {}
[843.655961] (-) TimerEvent: {}
[843.756661] (-) TimerEvent: {}
[843.857059] (-) TimerEvent: {}
[843.958190] (-) TimerEvent: {}
[844.058605] (-) TimerEvent: {}
[844.159101] (-) TimerEvent: {}
[844.259537] (-) TimerEvent: {}
[844.359980] (-) TimerEvent: {}
[844.460536] (-) TimerEvent: {}
[844.562104] (-) TimerEvent: {}
[844.662641] (-) TimerEvent: {}
[844.763114] (-) TimerEvent: {}
[844.863586] (-) TimerEvent: {}
[844.964215] (-) TimerEvent: {}
[845.064735] (-) TimerEvent: {}
[845.165237] (-) TimerEvent: {}
[845.266167] (-) TimerEvent: {}
[845.366700] (-) TimerEvent: {}
[845.467232] (-) TimerEvent: {}
[845.567749] (-) TimerEvent: {}
[845.668709] (-) TimerEvent: {}
[845.769157] (-) TimerEvent: {}
[845.870150] (-) TimerEvent: {}
[845.970677] (-) TimerEvent: {}
[846.071193] (-) TimerEvent: {}
[846.171746] (-) TimerEvent: {}
[846.272230] (-) TimerEvent: {}
[846.372977] (-) TimerEvent: {}
[846.474215] (-) TimerEvent: {}
[846.574864] (-) TimerEvent: {}
[846.675351] (-) TimerEvent: {}
[846.775813] (-) TimerEvent: {}
[846.876289] (-) TimerEvent: {}
[846.977029] (-) TimerEvent: {}
[847.077516] (-) TimerEvent: {}
[847.178501] (-) TimerEvent: {}
[847.279056] (-) TimerEvent: {}
[847.380984] (-) TimerEvent: {}
[847.482168] (-) TimerEvent: {}
[847.582663] (-) TimerEvent: {}
[847.683036] (-) TimerEvent: {}
[847.783537] (-) TimerEvent: {}
[847.883966] (-) TimerEvent: {}
[847.985012] (-) TimerEvent: {}
[848.086208] (-) TimerEvent: {}
[848.186774] (-) TimerEvent: {}
[848.287224] (-) TimerEvent: {}
[848.387770] (-) TimerEvent: {}
[848.488341] (-) TimerEvent: {}
[848.590705] (-) TimerEvent: {}
[848.691329] (-) TimerEvent: {}
[848.791795] (-) TimerEvent: {}
[848.892264] (-) TimerEvent: {}
[848.992943] (-) TimerEvent: {}
[849.093588] (-) TimerEvent: {}
[849.194165] (-) TimerEvent: {}
[849.294563] (-) TimerEvent: {}
[849.394953] (-) TimerEvent: {}
[849.495622] (-) TimerEvent: {}
[849.596018] (-) TimerEvent: {}
[849.696843] (-) TimerEvent: {}
[849.798140] (-) TimerEvent: {}
[849.898522] (-) TimerEvent: {}
[849.998897] (-) TimerEvent: {}
[850.099282] (-) TimerEvent: {}
[850.199670] (-) TimerEvent: {}
[850.300079] (-) TimerEvent: {}
[850.400764] (-) TimerEvent: {}
[850.501641] (-) TimerEvent: {}
[850.602541] (-) TimerEvent: {}
[850.703152] (-) TimerEvent: {}
[850.803582] (-) TimerEvent: {}
[850.904008] (-) TimerEvent: {}
[851.004792] (-) TimerEvent: {}
[851.106256] (-) TimerEvent: {}
[851.206650] (-) TimerEvent: {}
[851.260797] (ros1_bridge) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim_factories.cpp.o\x1b[0m\n'}
[851.306784] (-) TimerEvent: {}
[851.407237] (-) TimerEvent: {}
[851.508188] (-) TimerEvent: {}
[851.609000] (-) TimerEvent: {}
[851.709605] (-) TimerEvent: {}
[851.810172] (-) TimerEvent: {}
[851.910862] (-) TimerEvent: {}
[852.011331] (-) TimerEvent: {}
[852.111962] (-) TimerEvent: {}
[852.212821] (-) TimerEvent: {}
[852.293608] (ros1_bridge) StdoutLine: {'line': b'[ 90%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__msg__Color__factories.cpp.o\x1b[0m\n'}
[852.313016] (-) TimerEvent: {}
[852.413451] (-) TimerEvent: {}
[852.514203] (-) TimerEvent: {}
[852.614737] (-) TimerEvent: {}
[852.715249] (-) TimerEvent: {}
[852.815706] (-) TimerEvent: {}
[852.916193] (-) TimerEvent: {}
[853.017088] (-) TimerEvent: {}
[853.118292] (-) TimerEvent: {}
[853.218898] (-) TimerEvent: {}
[853.319445] (-) TimerEvent: {}
[853.420000] (-) TimerEvent: {}
[853.520609] (-) TimerEvent: {}
[853.621427] (-) TimerEvent: {}
[853.722476] (-) TimerEvent: {}
[853.823084] (-) TimerEvent: {}
[853.923506] (-) TimerEvent: {}
[854.024056] (-) TimerEvent: {}
[854.125029] (-) TimerEvent: {}
[854.226341] (-) TimerEvent: {}
[854.327241] (-) TimerEvent: {}
[854.427780] (-) TimerEvent: {}
[854.528214] (-) TimerEvent: {}
[854.629280] (-) TimerEvent: {}
[854.730602] (-) TimerEvent: {}
[854.831258] (-) TimerEvent: {}
[854.931614] (-) TimerEvent: {}
[855.031980] (-) TimerEvent: {}
[855.132435] (-) TimerEvent: {}
[855.233154] (-) TimerEvent: {}
[855.334168] (-) TimerEvent: {}
[855.434649] (-) TimerEvent: {}
[855.535176] (-) TimerEvent: {}
[855.635645] (-) TimerEvent: {}
[855.656499] (ros1_bridge) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__msg__Pose__factories.cpp.o\x1b[0m\n'}
[855.735771] (-) TimerEvent: {}
[855.836465] (-) TimerEvent: {}
[855.937115] (-) TimerEvent: {}
[856.038160] (-) TimerEvent: {}
[856.138718] (-) TimerEvent: {}
[856.239338] (-) TimerEvent: {}
[856.339920] (-) TimerEvent: {}
[856.430548] (ros1_bridge) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__Kill__factories.cpp.o\x1b[0m\n'}
[856.440019] (-) TimerEvent: {}
[856.540953] (-) TimerEvent: {}
[856.641388] (-) TimerEvent: {}
[856.742156] (-) TimerEvent: {}
[856.842636] (-) TimerEvent: {}
[856.943078] (-) TimerEvent: {}
[857.044047] (-) TimerEvent: {}
[857.144708] (-) TimerEvent: {}
[857.245125] (-) TimerEvent: {}
[857.345547] (-) TimerEvent: {}
[857.446153] (-) TimerEvent: {}
[857.546568] (-) TimerEvent: {}
[857.646956] (-) TimerEvent: {}
[857.747353] (-) TimerEvent: {}
[857.847782] (-) TimerEvent: {}
[857.948211] (-) TimerEvent: {}
[858.048691] (-) TimerEvent: {}
[858.149152] (-) TimerEvent: {}
[858.250166] (-) TimerEvent: {}
[858.350573] (-) TimerEvent: {}
[858.451046] (-) TimerEvent: {}
[858.551691] (-) TimerEvent: {}
[858.652129] (-) TimerEvent: {}
[858.752732] (-) TimerEvent: {}
[858.853286] (-) TimerEvent: {}
[858.954166] (-) TimerEvent: {}
[859.054677] (-) TimerEvent: {}
[859.155208] (-) TimerEvent: {}
[859.255646] (-) TimerEvent: {}
[859.356001] (-) TimerEvent: {}
[859.456375] (-) TimerEvent: {}
[859.556805] (-) TimerEvent: {}
[859.657223] (-) TimerEvent: {}
[859.758370] (-) TimerEvent: {}
[859.859745] (-) TimerEvent: {}
[859.904699] (ros1_bridge) StdoutLine: {'line': b'[ 91%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__SetPen__factories.cpp.o\x1b[0m\n'}
[859.959841] (-) TimerEvent: {}
[860.060241] (-) TimerEvent: {}
[860.161140] (-) TimerEvent: {}
[860.261560] (-) TimerEvent: {}
[860.362161] (-) TimerEvent: {}
[860.462643] (-) TimerEvent: {}
[860.563070] (-) TimerEvent: {}
[860.663457] (-) TimerEvent: {}
[860.763907] (-) TimerEvent: {}
[860.864461] (-) TimerEvent: {}
[860.965636] (-) TimerEvent: {}
[861.067159] (-) TimerEvent: {}
[861.167683] (-) TimerEvent: {}
[861.268133] (-) TimerEvent: {}
[861.368670] (-) TimerEvent: {}
[861.469201] (-) TimerEvent: {}
[861.570675] (-) TimerEvent: {}
[861.671974] (-) TimerEvent: {}
[861.740250] (ros1_bridge) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__Spawn__factories.cpp.o\x1b[0m\n'}
[861.772046] (-) TimerEvent: {}
[861.872486] (-) TimerEvent: {}
[861.973050] (-) TimerEvent: {}
[862.073506] (-) TimerEvent: {}
[862.174178] (-) TimerEvent: {}
[862.274656] (-) TimerEvent: {}
[862.375143] (-) TimerEvent: {}
[862.476028] (-) TimerEvent: {}
[862.577140] (-) TimerEvent: {}
[862.678202] (-) TimerEvent: {}
[862.778727] (-) TimerEvent: {}
[862.879358] (-) TimerEvent: {}
[862.979833] (-) TimerEvent: {}
[863.080277] (-) TimerEvent: {}
[863.180744] (-) TimerEvent: {}
[863.281229] (-) TimerEvent: {}
[863.382136] (-) TimerEvent: {}
[863.482602] (-) TimerEvent: {}
[863.583128] (-) TimerEvent: {}
[863.683668] (-) TimerEvent: {}
[863.784385] (-) TimerEvent: {}
[863.884955] (-) TimerEvent: {}
[863.985451] (-) TimerEvent: {}
[864.086167] (-) TimerEvent: {}
[864.186703] (-) TimerEvent: {}
[864.287151] (-) TimerEvent: {}
[864.387710] (-) TimerEvent: {}
[864.488217] (-) TimerEvent: {}
[864.588717] (-) TimerEvent: {}
[864.689159] (-) TimerEvent: {}
[864.790152] (-) TimerEvent: {}
[864.890695] (-) TimerEvent: {}
[864.991149] (-) TimerEvent: {}
[865.092016] (-) TimerEvent: {}
[865.192547] (-) TimerEvent: {}
[865.293103] (-) TimerEvent: {}
[865.394163] (-) TimerEvent: {}
[865.494578] (-) TimerEvent: {}
[865.508425] (ros1_bridge) StdoutLine: {'line': b'[ 92%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__TeleportAbsolute__factories.cpp.o\x1b[0m\n'}
[865.594683] (-) TimerEvent: {}
[865.695115] (-) TimerEvent: {}
[865.795528] (-) TimerEvent: {}
[865.895941] (-) TimerEvent: {}
[865.996516] (-) TimerEvent: {}
[866.097228] (-) TimerEvent: {}
[866.198157] (-) TimerEvent: {}
[866.299285] (-) TimerEvent: {}
[866.399704] (-) TimerEvent: {}
[866.500197] (-) TimerEvent: {}
[866.600668] (-) TimerEvent: {}
[866.701400] (-) TimerEvent: {}
[866.802134] (-) TimerEvent: {}
[866.902495] (-) TimerEvent: {}
[867.002892] (-) TimerEvent: {}
[867.103726] (-) TimerEvent: {}
[867.204121] (-) TimerEvent: {}
[867.304924] (-) TimerEvent: {}
[867.307762] (ros1_bridge) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__TeleportRelative__factories.cpp.o\x1b[0m\n'}
[867.405417] (-) TimerEvent: {}
[867.506295] (-) TimerEvent: {}
[867.606704] (-) TimerEvent: {}
[867.707108] (-) TimerEvent: {}
[867.807507] (-) TimerEvent: {}
[867.907890] (-) TimerEvent: {}
[868.008266] (-) TimerEvent: {}
[868.108606] (-) TimerEvent: {}
[868.208915] (-) TimerEvent: {}
[868.309349] (-) TimerEvent: {}
[868.410174] (-) TimerEvent: {}
[868.510622] (-) TimerEvent: {}
[868.611078] (-) TimerEvent: {}
[868.711936] (-) TimerEvent: {}
[868.812398] (-) TimerEvent: {}
[868.912816] (-) TimerEvent: {}
[869.013228] (-) TimerEvent: {}
[869.114177] (-) TimerEvent: {}
[869.214651] (-) TimerEvent: {}
[869.315142] (-) TimerEvent: {}
[869.415584] (-) TimerEvent: {}
[869.516510] (-) TimerEvent: {}
[869.617152] (-) TimerEvent: {}
[869.718161] (-) TimerEvent: {}
[869.818686] (-) TimerEvent: {}
[869.919197] (-) TimerEvent: {}
[870.019682] (-) TimerEvent: {}
[870.120181] (-) TimerEvent: {}
[870.220688] (-) TimerEvent: {}
[870.321249] (-) TimerEvent: {}
[870.422170] (-) TimerEvent: {}
[870.522622] (-) TimerEvent: {}
[870.623088] (-) TimerEvent: {}
[870.723760] (-) TimerEvent: {}
[870.824266] (-) TimerEvent: {}
[870.924932] (-) TimerEvent: {}
[871.025414] (-) TimerEvent: {}
[871.050550] (ros1_bridge) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/unique_identifier_msgs_factories.cpp.o\x1b[0m\n'}
[871.125530] (-) TimerEvent: {}
[871.226185] (-) TimerEvent: {}
[871.327339] (-) TimerEvent: {}
[871.427769] (-) TimerEvent: {}
[871.528333] (-) TimerEvent: {}
[871.629135] (-) TimerEvent: {}
[871.729518] (-) TimerEvent: {}
[871.830983] (-) TimerEvent: {}
[871.931319] (-) TimerEvent: {}
[872.032049] (-) TimerEvent: {}
[872.132778] (-) TimerEvent: {}
[872.234150] (-) TimerEvent: {}
[872.334545] (-) TimerEvent: {}
[872.434951] (-) TimerEvent: {}
[872.535376] (-) TimerEvent: {}
[872.571906] (ros1_bridge) StdoutLine: {'line': b'[ 93%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/unique_identifier_msgs__msg__UUID__factories.cpp.o\x1b[0m\n'}
[872.635478] (-) TimerEvent: {}
[872.735865] (-) TimerEvent: {}
[872.836201] (-) TimerEvent: {}
[872.936723] (-) TimerEvent: {}
[873.037195] (-) TimerEvent: {}
[873.138163] (-) TimerEvent: {}
[873.238674] (-) TimerEvent: {}
[873.339176] (-) TimerEvent: {}
[873.439647] (-) TimerEvent: {}
[873.540187] (-) TimerEvent: {}
[873.640878] (-) TimerEvent: {}
[873.741406] (-) TimerEvent: {}
[873.842186] (-) TimerEvent: {}
[873.942601] (-) TimerEvent: {}
[874.043092] (-) TimerEvent: {}
[874.143653] (-) TimerEvent: {}
[874.244204] (-) TimerEvent: {}
[874.344676] (-) TimerEvent: {}
[874.445124] (-) TimerEvent: {}
[874.546189] (-) TimerEvent: {}
[874.646647] (-) TimerEvent: {}
[874.747232] (-) TimerEvent: {}
[874.847633] (-) TimerEvent: {}
[874.948052] (-) TimerEvent: {}
[875.030981] (ros1_bridge) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs_factories.cpp.o\x1b[0m\n'}
[875.048260] (-) TimerEvent: {}
[875.149131] (-) TimerEvent: {}
[875.250172] (-) TimerEvent: {}
[875.350536] (-) TimerEvent: {}
[875.451144] (-) TimerEvent: {}
[875.551535] (-) TimerEvent: {}
[875.652311] (-) TimerEvent: {}
[875.753244] (-) TimerEvent: {}
[875.854181] (-) TimerEvent: {}
[875.954541] (-) TimerEvent: {}
[876.054941] (-) TimerEvent: {}
[876.155388] (-) TimerEvent: {}
[876.255773] (-) TimerEvent: {}
[876.289347] (ros1_bridge) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__ImageMarker__factories.cpp.o\x1b[0m\n'}
[876.355863] (-) TimerEvent: {}
[876.456713] (-) TimerEvent: {}
[876.558152] (-) TimerEvent: {}
[876.658728] (-) TimerEvent: {}
[876.759113] (-) TimerEvent: {}
[876.859590] (-) TimerEvent: {}
[876.960053] (-) TimerEvent: {}
[877.060523] (-) TimerEvent: {}
[877.162131] (-) TimerEvent: {}
[877.262522] (-) TimerEvent: {}
[877.362925] (-) TimerEvent: {}
[877.463330] (-) TimerEvent: {}
[877.563818] (-) TimerEvent: {}
[877.664302] (-) TimerEvent: {}
[877.764764] (-) TimerEvent: {}
[877.865228] (-) TimerEvent: {}
[877.966147] (-) TimerEvent: {}
[878.066547] (-) TimerEvent: {}
[878.167393] (-) TimerEvent: {}
[878.267932] (-) TimerEvent: {}
[878.368695] (-) TimerEvent: {}
[878.469324] (-) TimerEvent: {}
[878.570205] (-) TimerEvent: {}
[878.670692] (-) TimerEvent: {}
[878.771491] (-) TimerEvent: {}
[878.872351] (-) TimerEvent: {}
[878.972822] (-) TimerEvent: {}
[879.073293] (-) TimerEvent: {}
[879.174188] (-) TimerEvent: {}
[879.274671] (-) TimerEvent: {}
[879.372476] (ros1_bridge) StdoutLine: {'line': b'[ 94%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarker__factories.cpp.o\x1b[0m\n'}
[879.374730] (-) TimerEvent: {}
[879.475627] (-) TimerEvent: {}
[879.576035] (-) TimerEvent: {}
[879.676432] (-) TimerEvent: {}
[879.776984] (-) TimerEvent: {}
[879.877376] (-) TimerEvent: {}
[879.978167] (-) TimerEvent: {}
[880.078573] (-) TimerEvent: {}
[880.178992] (-) TimerEvent: {}
[880.279536] (-) TimerEvent: {}
[880.379949] (-) TimerEvent: {}
[880.480351] (-) TimerEvent: {}
[880.581149] (-) TimerEvent: {}
[880.682150] (-) TimerEvent: {}
[880.782508] (-) TimerEvent: {}
[880.882917] (-) TimerEvent: {}
[880.983537] (-) TimerEvent: {}
[881.083902] (-) TimerEvent: {}
[881.184324] (-) TimerEvent: {}
[881.284791] (-) TimerEvent: {}
[881.386569] (-) TimerEvent: {}
[881.486989] (-) TimerEvent: {}
[881.587389] (-) TimerEvent: {}
[881.687755] (-) TimerEvent: {}
[881.788159] (-) TimerEvent: {}
[881.888669] (-) TimerEvent: {}
[881.989067] (-) TimerEvent: {}
[882.089485] (-) TimerEvent: {}
[882.190187] (-) TimerEvent: {}
[882.290636] (-) TimerEvent: {}
[882.391059] (-) TimerEvent: {}
[882.491571] (-) TimerEvent: {}
[882.591954] (-) TimerEvent: {}
[882.692323] (-) TimerEvent: {}
[882.792860] (-) TimerEvent: {}
[882.893337] (-) TimerEvent: {}
[882.994208] (-) TimerEvent: {}
[883.094611] (-) TimerEvent: {}
[883.195418] (-) TimerEvent: {}
[883.295859] (-) TimerEvent: {}
[883.396271] (-) TimerEvent: {}
[883.496903] (-) TimerEvent: {}
[883.597344] (-) TimerEvent: {}
[883.698158] (-) TimerEvent: {}
[883.798584] (-) TimerEvent: {}
[883.899045] (-) TimerEvent: {}
[883.999418] (-) TimerEvent: {}
[884.099778] (-) TimerEvent: {}
[884.200136] (-) TimerEvent: {}
[884.300664] (-) TimerEvent: {}
[884.401077] (-) TimerEvent: {}
[884.502150] (-) TimerEvent: {}
[884.602559] (-) TimerEvent: {}
[884.703131] (-) TimerEvent: {}
[884.803633] (-) TimerEvent: {}
[884.904057] (-) TimerEvent: {}
[885.004830] (-) TimerEvent: {}
[885.106171] (-) TimerEvent: {}
[885.170839] (ros1_bridge) StdoutLine: {'line': b'[ 95%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerControl__factories.cpp.o\x1b[0m\n'}
[885.206306] (-) TimerEvent: {}
[885.306733] (-) TimerEvent: {}
[885.407090] (-) TimerEvent: {}
[885.507463] (-) TimerEvent: {}
[885.607918] (-) TimerEvent: {}
[885.708409] (-) TimerEvent: {}
[885.809129] (-) TimerEvent: {}
[885.909548] (-) TimerEvent: {}
[886.010625] (-) TimerEvent: {}
[886.111808] (-) TimerEvent: {}
[886.212573] (-) TimerEvent: {}
[886.313314] (-) TimerEvent: {}
[886.414281] (-) TimerEvent: {}
[886.514814] (-) TimerEvent: {}
[886.615333] (-) TimerEvent: {}
[886.715937] (-) TimerEvent: {}
[886.816299] (-) TimerEvent: {}
[886.916792] (-) TimerEvent: {}
[887.018151] (-) TimerEvent: {}
[887.118565] (-) TimerEvent: {}
[887.219003] (-) TimerEvent: {}
[887.319470] (-) TimerEvent: {}
[887.419902] (-) TimerEvent: {}
[887.520317] (-) TimerEvent: {}
[887.620734] (-) TimerEvent: {}
[887.721551] (-) TimerEvent: {}
[887.822149] (-) TimerEvent: {}
[887.922558] (-) TimerEvent: {}
[888.022943] (-) TimerEvent: {}
[888.123357] (-) TimerEvent: {}
[888.223768] (-) TimerEvent: {}
[888.324169] (-) TimerEvent: {}
[888.424561] (-) TimerEvent: {}
[888.524973] (-) TimerEvent: {}
[888.625534] (-) TimerEvent: {}
[888.726165] (-) TimerEvent: {}
[888.826553] (-) TimerEvent: {}
[888.926928] (-) TimerEvent: {}
[888.929090] (ros1_bridge) StdoutLine: {'line': b'[ 95%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerFeedback__factories.cpp.o\x1b[0m\n'}
[889.027048] (-) TimerEvent: {}
[889.127488] (-) TimerEvent: {}
[889.227962] (-) TimerEvent: {}
[889.328406] (-) TimerEvent: {}
[889.428830] (-) TimerEvent: {}
[889.530194] (-) TimerEvent: {}
[889.630679] (-) TimerEvent: {}
[889.731196] (-) TimerEvent: {}
[889.831638] (-) TimerEvent: {}
[889.932088] (-) TimerEvent: {}
[890.032732] (-) TimerEvent: {}
[890.134160] (-) TimerEvent: {}
[890.234723] (-) TimerEvent: {}
[890.335278] (-) TimerEvent: {}
[890.435804] (-) TimerEvent: {}
[890.536324] (-) TimerEvent: {}
[890.636832] (-) TimerEvent: {}
[890.738179] (-) TimerEvent: {}
[890.838745] (-) TimerEvent: {}
[890.939303] (-) TimerEvent: {}
[891.039757] (-) TimerEvent: {}
[891.140261] (-) TimerEvent: {}
[891.240723] (-) TimerEvent: {}
[891.341129] (-) TimerEvent: {}
[891.442146] (-) TimerEvent: {}
[891.542581] (-) TimerEvent: {}
[891.643054] (-) TimerEvent: {}
[891.743512] (-) TimerEvent: {}
[891.843985] (-) TimerEvent: {}
[891.944396] (-) TimerEvent: {}
[892.044863] (-) TimerEvent: {}
[892.145212] (-) TimerEvent: {}
[892.246215] (-) TimerEvent: {}
[892.346736] (-) TimerEvent: {}
[892.447181] (-) TimerEvent: {}
[892.547608] (-) TimerEvent: {}
[892.648045] (-) TimerEvent: {}
[892.748713] (-) TimerEvent: {}
[892.849169] (-) TimerEvent: {}
[892.950177] (-) TimerEvent: {}
[893.050695] (-) TimerEvent: {}
[893.151125] (-) TimerEvent: {}
[893.251608] (-) TimerEvent: {}
[893.351977] (-) TimerEvent: {}
[893.452460] (-) TimerEvent: {}
[893.552900] (-) TimerEvent: {}
[893.654153] (-) TimerEvent: {}
[893.754685] (-) TimerEvent: {}
[893.855179] (-) TimerEvent: {}
[893.960658] (-) TimerEvent: {}
[894.061197] (-) TimerEvent: {}
[894.162164] (-) TimerEvent: {}
[894.262838] (-) TimerEvent: {}
[894.363401] (-) TimerEvent: {}
[894.466600] (-) TimerEvent: {}
[894.557665] (ros1_bridge) StdoutLine: {'line': b'[ 95%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerInit__factories.cpp.o\x1b[0m\n'}
[894.566673] (-) TimerEvent: {}
[894.667252] (-) TimerEvent: {}
[894.767768] (-) TimerEvent: {}
[894.868268] (-) TimerEvent: {}
[894.968791] (-) TimerEvent: {}
[895.069303] (-) TimerEvent: {}
[895.170200] (-) TimerEvent: {}
[895.270701] (-) TimerEvent: {}
[895.371204] (-) TimerEvent: {}
[895.471709] (-) TimerEvent: {}
[895.572156] (-) TimerEvent: {}
[895.672513] (-) TimerEvent: {}
[895.772983] (-) TimerEvent: {}
[895.874193] (-) TimerEvent: {}
[895.974720] (-) TimerEvent: {}
[896.075295] (-) TimerEvent: {}
[896.175839] (-) TimerEvent: {}
[896.276370] (-) TimerEvent: {}
[896.376980] (-) TimerEvent: {}
[896.478242] (-) TimerEvent: {}
[896.578770] (-) TimerEvent: {}
[896.679242] (-) TimerEvent: {}
[896.779713] (-) TimerEvent: {}
[896.880139] (-) TimerEvent: {}
[896.980627] (-) TimerEvent: {}
[897.081142] (-) TimerEvent: {}
[897.182173] (-) TimerEvent: {}
[897.282654] (-) TimerEvent: {}
[897.383067] (-) TimerEvent: {}
[897.483519] (-) TimerEvent: {}
[897.584021] (-) TimerEvent: {}
[897.684992] (-) TimerEvent: {}
[897.785436] (-) TimerEvent: {}
[897.886521] (-) TimerEvent: {}
[897.987260] (-) TimerEvent: {}
[898.087863] (-) TimerEvent: {}
[898.188458] (-) TimerEvent: {}
[898.289075] (-) TimerEvent: {}
[898.390174] (-) TimerEvent: {}
[898.490860] (-) TimerEvent: {}
[898.591831] (-) TimerEvent: {}
[898.692793] (-) TimerEvent: {}
[898.793312] (-) TimerEvent: {}
[898.894372] (-) TimerEvent: {}
[898.994933] (-) TimerEvent: {}
[899.095355] (-) TimerEvent: {}
[899.195834] (-) TimerEvent: {}
[899.296242] (-) TimerEvent: {}
[899.396820] (-) TimerEvent: {}
[899.498199] (-) TimerEvent: {}
[899.598945] (-) TimerEvent: {}
[899.699332] (-) TimerEvent: {}
[899.799804] (-) TimerEvent: {}
[899.900368] (-) TimerEvent: {}
[900.001000] (-) TimerEvent: {}
[900.102219] (-) TimerEvent: {}
[900.202786] (-) TimerEvent: {}
[900.303304] (-) TimerEvent: {}
[900.403797] (-) TimerEvent: {}
[900.504207] (-) TimerEvent: {}
[900.604617] (-) TimerEvent: {}
[900.705101] (-) TimerEvent: {}
[900.713598] (ros1_bridge) StdoutLine: {'line': b'[ 96%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerPose__factories.cpp.o\x1b[0m\n'}
[900.805277] (-) TimerEvent: {}
[900.906602] (-) TimerEvent: {}
[901.007078] (-) TimerEvent: {}
[901.107550] (-) TimerEvent: {}
[901.208146] (-) TimerEvent: {}
[901.309230] (-) TimerEvent: {}
[901.411599] (-) TimerEvent: {}
[901.512001] (-) TimerEvent: {}
[901.612492] (-) TimerEvent: {}
[901.713047] (-) TimerEvent: {}
[901.814172] (-) TimerEvent: {}
[901.914772] (-) TimerEvent: {}
[902.015707] (-) TimerEvent: {}
[902.116643] (-) TimerEvent: {}
[902.218216] (-) TimerEvent: {}
[902.319299] (-) TimerEvent: {}
[902.420016] (-) TimerEvent: {}
[902.520763] (-) TimerEvent: {}
[902.621387] (-) TimerEvent: {}
[902.722285] (-) TimerEvent: {}
[902.823003] (-) TimerEvent: {}
[902.923622] (-) TimerEvent: {}
[903.024217] (-) TimerEvent: {}
[903.124847] (-) TimerEvent: {}
[903.225517] (-) TimerEvent: {}
[903.326305] (-) TimerEvent: {}
[903.426872] (-) TimerEvent: {}
[903.527442] (-) TimerEvent: {}
[903.627976] (-) TimerEvent: {}
[903.728928] (-) TimerEvent: {}
[903.829408] (-) TimerEvent: {}
[903.930256] (-) TimerEvent: {}
[904.030937] (-) TimerEvent: {}
[904.131548] (-) TimerEvent: {}
[904.231906] (-) TimerEvent: {}
[904.332332] (-) TimerEvent: {}
[904.433206] (-) TimerEvent: {}
[904.534179] (-) TimerEvent: {}
[904.634642] (-) TimerEvent: {}
[904.735139] (-) TimerEvent: {}
[904.835739] (-) TimerEvent: {}
[904.936501] (-) TimerEvent: {}
[905.036950] (-) TimerEvent: {}
[905.138144] (-) TimerEvent: {}
[905.238502] (-) TimerEvent: {}
[905.338962] (-) TimerEvent: {}
[905.439479] (-) TimerEvent: {}
[905.540016] (-) TimerEvent: {}
[905.640457] (-) TimerEvent: {}
[905.741133] (-) TimerEvent: {}
[905.842174] (-) TimerEvent: {}
[905.942650] (-) TimerEvent: {}
[906.043198] (-) TimerEvent: {}
[906.143734] (-) TimerEvent: {}
[906.244351] (-) TimerEvent: {}
[906.345034] (-) TimerEvent: {}
[906.450340] (-) TimerEvent: {}
[906.554247] (-) TimerEvent: {}
[906.654773] (-) TimerEvent: {}
[906.755281] (-) TimerEvent: {}
[906.855981] (-) TimerEvent: {}
[906.956412] (-) TimerEvent: {}
[907.024713] (ros1_bridge) StdoutLine: {'line': b'[ 96%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerUpdate__factories.cpp.o\x1b[0m\n'}
[907.056576] (-) TimerEvent: {}
[907.157180] (-) TimerEvent: {}
[907.258203] (-) TimerEvent: {}
[907.358739] (-) TimerEvent: {}
[907.459255] (-) TimerEvent: {}
[907.559666] (-) TimerEvent: {}
[907.660109] (-) TimerEvent: {}
[907.760561] (-) TimerEvent: {}
[907.861023] (-) TimerEvent: {}
[907.961386] (-) TimerEvent: {}
[908.062150] (-) TimerEvent: {}
[908.162602] (-) TimerEvent: {}
[908.263226] (-) TimerEvent: {}
[908.363644] (-) TimerEvent: {}
[908.464036] (-) TimerEvent: {}
[908.564456] (-) TimerEvent: {}
[908.665337] (-) TimerEvent: {}
[908.766173] (-) TimerEvent: {}
[908.866587] (-) TimerEvent: {}
[908.967052] (-) TimerEvent: {}
[909.067523] (-) TimerEvent: {}
[909.167946] (-) TimerEvent: {}
[909.268377] (-) TimerEvent: {}
[909.368932] (-) TimerEvent: {}
[909.470176] (-) TimerEvent: {}
[909.570624] (-) TimerEvent: {}
[909.671330] (-) TimerEvent: {}
[909.772256] (-) TimerEvent: {}
[909.872873] (-) TimerEvent: {}
[909.973312] (-) TimerEvent: {}
[910.074157] (-) TimerEvent: {}
[910.174621] (-) TimerEvent: {}
[910.275067] (-) TimerEvent: {}
[910.375565] (-) TimerEvent: {}
[910.476184] (-) TimerEvent: {}
[910.576793] (-) TimerEvent: {}
[910.677455] (-) TimerEvent: {}
[910.778249] (-) TimerEvent: {}
[910.878804] (-) TimerEvent: {}
[910.979384] (-) TimerEvent: {}
[911.079927] (-) TimerEvent: {}
[911.180272] (-) TimerEvent: {}
[911.281149] (-) TimerEvent: {}
[911.382221] (-) TimerEvent: {}
[911.482734] (-) TimerEvent: {}
[911.583236] (-) TimerEvent: {}
[911.683861] (-) TimerEvent: {}
[911.784336] (-) TimerEvent: {}
[911.814435] (ros1_bridge) StdoutLine: {'line': b'[ 96%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__Marker__factories.cpp.o\x1b[0m\n'}
[911.884858] (-) TimerEvent: {}
[911.986187] (-) TimerEvent: {}
[912.086689] (-) TimerEvent: {}
[912.187157] (-) TimerEvent: {}
[912.287693] (-) TimerEvent: {}
[912.388262] (-) TimerEvent: {}
[912.488776] (-) TimerEvent: {}
[912.589249] (-) TimerEvent: {}
[912.690242] (-) TimerEvent: {}
[912.790759] (-) TimerEvent: {}
[912.891200] (-) TimerEvent: {}
[912.991730] (-) TimerEvent: {}
[913.092338] (-) TimerEvent: {}
[913.193223] (-) TimerEvent: {}
[913.294270] (-) TimerEvent: {}
[913.394869] (-) TimerEvent: {}
[913.495441] (-) TimerEvent: {}
[913.596077] (-) TimerEvent: {}
[913.696993] (-) TimerEvent: {}
[913.798252] (-) TimerEvent: {}
[913.899207] (-) TimerEvent: {}
[913.999893] (-) TimerEvent: {}
[914.100640] (-) TimerEvent: {}
[914.201517] (-) TimerEvent: {}
[914.302185] (-) TimerEvent: {}
[914.402789] (-) TimerEvent: {}
[914.503425] (-) TimerEvent: {}
[914.603937] (-) TimerEvent: {}
[914.705083] (-) TimerEvent: {}
[914.806233] (-) TimerEvent: {}
[914.907032] (-) TimerEvent: {}
[915.007719] (-) TimerEvent: {}
[915.108329] (-) TimerEvent: {}
[915.208755] (-) TimerEvent: {}
[915.309165] (-) TimerEvent: {}
[915.410321] (-) TimerEvent: {}
[915.510787] (-) TimerEvent: {}
[915.611430] (-) TimerEvent: {}
[915.711956] (-) TimerEvent: {}
[915.813160] (-) TimerEvent: {}
[915.914373] (-) TimerEvent: {}
[916.015128] (-) TimerEvent: {}
[916.115786] (-) TimerEvent: {}
[916.216596] (-) TimerEvent: {}
[916.317404] (-) TimerEvent: {}
[916.418429] (-) TimerEvent: {}
[916.519251] (-) TimerEvent: {}
[916.620362] (-) TimerEvent: {}
[916.721134] (-) TimerEvent: {}
[916.822255] (-) TimerEvent: {}
[916.922843] (-) TimerEvent: {}
[917.023460] (-) TimerEvent: {}
[917.124445] (-) TimerEvent: {}
[917.225049] (-) TimerEvent: {}
[917.326172] (-) TimerEvent: {}
[917.426705] (-) TimerEvent: {}
[917.527247] (-) TimerEvent: {}
[917.627721] (-) TimerEvent: {}
[917.728215] (-) TimerEvent: {}
[917.829173] (-) TimerEvent: {}
[917.930186] (-) TimerEvent: {}
[918.030684] (-) TimerEvent: {}
[918.131150] (-) TimerEvent: {}
[918.231674] (-) TimerEvent: {}
[918.332114] (-) TimerEvent: {}
[918.432597] (-) TimerEvent: {}
[918.533137] (-) TimerEvent: {}
[918.634196] (-) TimerEvent: {}
[918.734570] (-) TimerEvent: {}
[918.835171] (-) TimerEvent: {}
[918.935933] (-) TimerEvent: {}
[919.036781] (-) TimerEvent: {}
[919.137352] (-) TimerEvent: {}
[919.238172] (-) TimerEvent: {}
[919.338726] (-) TimerEvent: {}
[919.439291] (-) TimerEvent: {}
[919.539980] (-) TimerEvent: {}
[919.640987] (-) TimerEvent: {}
[919.742188] (-) TimerEvent: {}
[919.842682] (-) TimerEvent: {}
[919.943204] (-) TimerEvent: {}
[920.043652] (-) TimerEvent: {}
[920.144105] (-) TimerEvent: {}
[920.244733] (-) TimerEvent: {}
[920.345164] (-) TimerEvent: {}
[920.446163] (-) TimerEvent: {}
[920.546497] (-) TimerEvent: {}
[920.647078] (-) TimerEvent: {}
[920.747489] (-) TimerEvent: {}
[920.848200] (-) TimerEvent: {}
[920.948849] (-) TimerEvent: {}
[921.049152] (-) TimerEvent: {}
[921.150173] (-) TimerEvent: {}
[921.250685] (-) TimerEvent: {}
[921.351274] (-) TimerEvent: {}
[921.452182] (-) TimerEvent: {}
[921.553256] (-) TimerEvent: {}
[921.654456] (-) TimerEvent: {}
[921.755417] (-) TimerEvent: {}
[921.855876] (-) TimerEvent: {}
[921.956553] (-) TimerEvent: {}
[922.057056] (-) TimerEvent: {}
[922.158154] (-) TimerEvent: {}
[922.258502] (-) TimerEvent: {}
[922.358986] (-) TimerEvent: {}
[922.459622] (-) TimerEvent: {}
[922.559930] (-) TimerEvent: {}
[922.660558] (-) TimerEvent: {}
[922.761479] (-) TimerEvent: {}
[922.862837] (-) TimerEvent: {}
[922.963547] (-) TimerEvent: {}
[923.064355] (-) TimerEvent: {}
[923.165126] (-) TimerEvent: {}
[923.265547] (-) TimerEvent: {}
[923.367221] (-) TimerEvent: {}
[923.467866] (-) TimerEvent: {}
[923.568276] (-) TimerEvent: {}
[923.668966] (-) TimerEvent: {}
[923.688187] (ros1_bridge) StdoutLine: {'line': b'[ 97%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__MarkerArray__factories.cpp.o\x1b[0m\n'}
[923.769115] (-) TimerEvent: {}
[923.870165] (-) TimerEvent: {}
[923.970869] (-) TimerEvent: {}
[924.071742] (-) TimerEvent: {}
[924.172165] (-) TimerEvent: {}
[924.272543] (-) TimerEvent: {}
[924.373039] (-) TimerEvent: {}
[924.474143] (-) TimerEvent: {}
[924.574600] (-) TimerEvent: {}
[924.675201] (-) TimerEvent: {}
[924.775981] (-) TimerEvent: {}
[924.876311] (-) TimerEvent: {}
[924.976680] (-) TimerEvent: {}
[925.077005] (-) TimerEvent: {}
[925.177291] (-) TimerEvent: {}
[925.278462] (-) TimerEvent: {}
[925.379183] (-) TimerEvent: {}
[925.479705] (-) TimerEvent: {}
[925.579992] (-) TimerEvent: {}
[925.680283] (-) TimerEvent: {}
[925.700399] (ros1_bridge) StdoutLine: {'line': b'[ 97%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__MenuEntry__factories.cpp.o\x1b[0m\n'}
[925.780386] (-) TimerEvent: {}
[925.880845] (-) TimerEvent: {}
[925.982234] (-) TimerEvent: {}
[926.082710] (-) TimerEvent: {}
[926.183191] (-) TimerEvent: {}
[926.284028] (-) TimerEvent: {}
[926.384902] (-) TimerEvent: {}
[926.485378] (-) TimerEvent: {}
[926.586807] (-) TimerEvent: {}
[926.687260] (-) TimerEvent: {}
[926.787735] (-) TimerEvent: {}
[926.888189] (-) TimerEvent: {}
[926.988816] (-) TimerEvent: {}
[927.089303] (-) TimerEvent: {}
[927.190162] (-) TimerEvent: {}
[927.290633] (-) TimerEvent: {}
[927.391120] (-) TimerEvent: {}
[927.491646] (-) TimerEvent: {}
[927.592175] (-) TimerEvent: {}
[927.692714] (-) TimerEvent: {}
[927.793259] (-) TimerEvent: {}
[927.894176] (-) TimerEvent: {}
[927.994622] (-) TimerEvent: {}
[928.095075] (-) TimerEvent: {}
[928.195688] (-) TimerEvent: {}
[928.296093] (-) TimerEvent: {}
[928.396913] (-) TimerEvent: {}
[928.498167] (-) TimerEvent: {}
[928.598639] (-) TimerEvent: {}
[928.699128] (-) TimerEvent: {}
[928.799972] (-) TimerEvent: {}
[928.900820] (-) TimerEvent: {}
[929.001288] (-) TimerEvent: {}
[929.102148] (-) TimerEvent: {}
[929.202596] (-) TimerEvent: {}
[929.303298] (-) TimerEvent: {}
[929.403796] (-) TimerEvent: {}
[929.504695] (-) TimerEvent: {}
[929.605468] (-) TimerEvent: {}
[929.706168] (-) TimerEvent: {}
[929.806636] (-) TimerEvent: {}
[929.907139] (-) TimerEvent: {}
[930.007601] (-) TimerEvent: {}
[930.108068] (-) TimerEvent: {}
[930.208602] (-) TimerEvent: {}
[930.309031] (-) TimerEvent: {}
[930.410144] (-) TimerEvent: {}
[930.510591] (-) TimerEvent: {}
[930.611044] (-) TimerEvent: {}
[930.711647] (-) TimerEvent: {}
[930.812084] (-) TimerEvent: {}
[930.913013] (-) TimerEvent: {}
[931.013369] (-) TimerEvent: {}
[931.114150] (-) TimerEvent: {}
[931.214672] (-) TimerEvent: {}
[931.315233] (-) TimerEvent: {}
[931.415692] (-) TimerEvent: {}
[931.516912] (-) TimerEvent: {}
[931.618156] (-) TimerEvent: {}
[931.718587] (-) TimerEvent: {}
[931.819026] (-) TimerEvent: {}
[931.919853] (-) TimerEvent: {}
[932.020237] (-) TimerEvent: {}
[932.120570] (-) TimerEvent: {}
[932.220979] (-) TimerEvent: {}
[932.321394] (-) TimerEvent: {}
[932.422249] (-) TimerEvent: {}
[932.522574] (-) TimerEvent: {}
[932.623101] (-) TimerEvent: {}
[932.723384] (-) TimerEvent: {}
[932.824312] (-) TimerEvent: {}
[932.924990] (-) TimerEvent: {}
[933.025352] (-) TimerEvent: {}
[933.126451] (-) TimerEvent: {}
[933.227148] (-) TimerEvent: {}
[933.327418] (-) TimerEvent: {}
[933.427742] (-) TimerEvent: {}
[933.528065] (-) TimerEvent: {}
[933.628386] (-) TimerEvent: {}
[933.728861] (-) TimerEvent: {}
[933.829248] (-) TimerEvent: {}
[933.930284] (-) TimerEvent: {}
[934.031861] (-) TimerEvent: {}
[934.132838] (-) TimerEvent: {}
[934.233269] (-) TimerEvent: {}
[934.251759] (ros1_bridge) StdoutLine: {'line': b'[ 97%] \x1b[32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__srv__GetInteractiveMarkers__factories.cpp.o\x1b[0m\n'}
[934.334148] (-) TimerEvent: {}
[934.434584] (-) TimerEvent: {}
[934.535009] (-) TimerEvent: {}
[934.635420] (-) TimerEvent: {}
[934.735816] (-) TimerEvent: {}
[934.836194] (-) TimerEvent: {}
[934.937106] (-) TimerEvent: {}
[935.037516] (-) TimerEvent: {}
[935.138190] (-) TimerEvent: {}
[935.238555] (-) TimerEvent: {}
[935.338953] (-) TimerEvent: {}
[935.439317] (-) TimerEvent: {}
[935.539745] (-) TimerEvent: {}
[935.640450] (-) TimerEvent: {}
[935.740932] (-) TimerEvent: {}
[935.841370] (-) TimerEvent: {}
[935.942373] (-) TimerEvent: {}
[936.042973] (-) TimerEvent: {}
[936.143911] (-) TimerEvent: {}
[936.244687] (-) TimerEvent: {}
[936.344986] (-) TimerEvent: {}
[936.445379] (-) TimerEvent: {}
[936.546174] (-) TimerEvent: {}
[936.646791] (-) TimerEvent: {}
[936.747586] (-) TimerEvent: {}
[936.848247] (-) TimerEvent: {}
[936.949256] (-) TimerEvent: {}
[937.050147] (-) TimerEvent: {}
[937.150730] (-) TimerEvent: {}
[937.251431] (-) TimerEvent: {}
[937.352216] (-) TimerEvent: {}
[937.453042] (-) TimerEvent: {}
[937.553405] (-) TimerEvent: {}
[937.654199] (-) TimerEvent: {}
[937.754820] (-) TimerEvent: {}
[937.855143] (-) TimerEvent: {}
[937.942649] (ros1_bridge) StdoutLine: {'line': b'[ 98%] \x1b[32m\x1b[1mLinking CXX shared library libros1_bridge.so\x1b[0m\n'}
[937.955234] (-) TimerEvent: {}
[938.056414] (-) TimerEvent: {}
[938.158233] (-) TimerEvent: {}
[938.258528] (-) TimerEvent: {}
[938.359376] (-) TimerEvent: {}
[938.460064] (-) TimerEvent: {}
[938.560487] (-) TimerEvent: {}
[938.661082] (-) TimerEvent: {}
[938.762166] (-) TimerEvent: {}
[938.862512] (-) TimerEvent: {}
[938.962937] (-) TimerEvent: {}
[939.063816] (-) TimerEvent: {}
[939.164288] (-) TimerEvent: {}
[939.265021] (-) TimerEvent: {}
[939.367176] (-) TimerEvent: {}
[939.467978] (-) TimerEvent: {}
[939.568433] (-) TimerEvent: {}
[939.669388] (-) TimerEvent: {}
[939.770146] (-) TimerEvent: {}
[939.870495] (-) TimerEvent: {}
[939.970910] (-) TimerEvent: {}
[940.071800] (-) TimerEvent: {}
[940.172288] (-) TimerEvent: {}
[940.272686] (-) TimerEvent: {}
[940.373267] (-) TimerEvent: {}
[940.474542] (-) TimerEvent: {}
[940.574967] (-) TimerEvent: {}
[940.675501] (-) TimerEvent: {}
[940.776405] (-) TimerEvent: {}
[940.877164] (-) TimerEvent: {}
[940.978158] (-) TimerEvent: {}
[941.078428] (-) TimerEvent: {}
[941.179358] (-) TimerEvent: {}
[941.280236] (-) TimerEvent: {}
[941.380812] (-) TimerEvent: {}
[941.481349] (-) TimerEvent: {}
[941.583132] (-) TimerEvent: {}
[941.684651] (-) TimerEvent: {}
[941.786445] (-) TimerEvent: {}
[941.886909] (-) TimerEvent: {}
[941.987818] (-) TimerEvent: {}
[942.088110] (-) TimerEvent: {}
[942.188396] (-) TimerEvent: {}
[942.288678] (-) TimerEvent: {}
[942.388963] (-) TimerEvent: {}
[942.489415] (-) TimerEvent: {}
[942.590487] (-) TimerEvent: {}
[942.690965] (-) TimerEvent: {}
[942.791656] (-) TimerEvent: {}
[942.892275] (-) TimerEvent: {}
[942.992971] (-) TimerEvent: {}
[943.093315] (-) TimerEvent: {}
[943.194191] (-) TimerEvent: {}
[943.294686] (-) TimerEvent: {}
[943.395568] (-) TimerEvent: {}
[943.496200] (-) TimerEvent: {}
[943.597055] (-) TimerEvent: {}
[943.698468] (-) TimerEvent: {}
[943.799563] (-) TimerEvent: {}
[943.899958] (-) TimerEvent: {}
[944.000428] (-) TimerEvent: {}
[944.100719] (-) TimerEvent: {}
[944.201019] (-) TimerEvent: {}
[944.301541] (-) TimerEvent: {}
[944.402234] (-) TimerEvent: {}
[944.503415] (-) TimerEvent: {}
[944.604003] (-) TimerEvent: {}
[944.704312] (-) TimerEvent: {}
[944.804644] (-) TimerEvent: {}
[944.904969] (-) TimerEvent: {}
[945.005400] (-) TimerEvent: {}
[945.107339] (-) TimerEvent: {}
[945.208008] (-) TimerEvent: {}
[945.308585] (-) TimerEvent: {}
[945.409004] (-) TimerEvent: {}
[945.509413] (-) TimerEvent: {}
[945.610244] (-) TimerEvent: {}
[945.713981] (-) TimerEvent: {}
[945.814449] (-) TimerEvent: {}
[945.914977] (-) TimerEvent: {}
[946.015840] (-) TimerEvent: {}
[946.116376] (-) TimerEvent: {}
[946.216864] (-) TimerEvent: {}
[946.317276] (-) TimerEvent: {}
[946.418201] (-) TimerEvent: {}
[946.518558] (-) TimerEvent: {}
[946.619138] (-) TimerEvent: {}
[946.720005] (-) TimerEvent: {}
[946.820398] (-) TimerEvent: {}
[946.921212] (-) TimerEvent: {}
[947.023250] (-) TimerEvent: {}
[947.123681] (-) TimerEvent: {}
[947.224310] (-) TimerEvent: {}
[947.334154] (-) TimerEvent: {}
[947.434506] (-) TimerEvent: {}
[947.535233] (-) TimerEvent: {}
[947.635718] (-) TimerEvent: {}
[947.736045] (-) TimerEvent: {}
[947.836788] (-) TimerEvent: {}
[947.937296] (-) TimerEvent: {}
[948.038180] (-) TimerEvent: {}
[948.138646] (-) TimerEvent: {}
[948.239253] (-) TimerEvent: {}
[948.339972] (-) TimerEvent: {}
[948.441476] (-) TimerEvent: {}
[948.542480] (-) TimerEvent: {}
[948.643160] (-) TimerEvent: {}
[948.743672] (-) TimerEvent: {}
[948.844340] (-) TimerEvent: {}
[948.946155] (-) TimerEvent: {}
[949.046827] (-) TimerEvent: {}
[949.147680] (-) TimerEvent: {}
[949.248061] (-) TimerEvent: {}
[949.348375] (-) TimerEvent: {}
[949.448835] (-) TimerEvent: {}
[949.549405] (-) TimerEvent: {}
[949.650185] (-) TimerEvent: {}
[949.750506] (-) TimerEvent: {}
[949.851256] (-) TimerEvent: {}
[949.952634] (-) TimerEvent: {}
[950.052994] (-) TimerEvent: {}
[950.153484] (-) TimerEvent: {}
[950.254149] (-) TimerEvent: {}
[950.354449] (-) TimerEvent: {}
[950.455125] (-) TimerEvent: {}
[950.555788] (-) TimerEvent: {}
[950.656371] (-) TimerEvent: {}
[950.756803] (-) TimerEvent: {}
[950.857327] (-) TimerEvent: {}
[950.959000] (-) TimerEvent: {}
[951.059990] (-) TimerEvent: {}
[951.162132] (-) TimerEvent: {}
[951.262593] (-) TimerEvent: {}
[951.362942] (-) TimerEvent: {}
[951.463262] (-) TimerEvent: {}
[951.563900] (-) TimerEvent: {}
[951.664718] (-) TimerEvent: {}
[951.765310] (-) TimerEvent: {}
[951.866358] (-) TimerEvent: {}
[951.967401] (-) TimerEvent: {}
[952.068130] (-) TimerEvent: {}
[952.168912] (-) TimerEvent: {}
[952.269236] (-) TimerEvent: {}
[952.370203] (-) TimerEvent: {}
[952.470586] (-) TimerEvent: {}
[952.570894] (-) TimerEvent: {}
[952.671250] (-) TimerEvent: {}
[952.771676] (-) TimerEvent: {}
[952.872268] (-) TimerEvent: {}
[952.972576] (-) TimerEvent: {}
[953.072920] (-) TimerEvent: {}
[953.173587] (-) TimerEvent: {}
[953.275203] (-) TimerEvent: {}
[953.375680] (-) TimerEvent: {}
[953.476129] (-) TimerEvent: {}
[953.576964] (-) TimerEvent: {}
[953.677409] (-) TimerEvent: {}
[953.778213] (-) TimerEvent: {}
[953.878811] (-) TimerEvent: {}
[953.980103] (-) TimerEvent: {}
[954.080753] (-) TimerEvent: {}
[954.181079] (-) TimerEvent: {}
[954.281455] (-) TimerEvent: {}
[954.382367] (-) TimerEvent: {}
[954.483304] (-) TimerEvent: {}
[954.584030] (-) TimerEvent: {}
[954.684669] (-) TimerEvent: {}
[954.785041] (-) TimerEvent: {}
[954.885378] (-) TimerEvent: {}
[954.986196] (-) TimerEvent: {}
[955.086843] (-) TimerEvent: {}
[955.187265] (-) TimerEvent: {}
[955.287550] (-) TimerEvent: {}
[955.387877] (-) TimerEvent: {}
[955.488292] (-) TimerEvent: {}
[955.589082] (-) TimerEvent: {}
[955.690253] (-) TimerEvent: {}
[955.790863] (-) TimerEvent: {}
[955.891697] (-) TimerEvent: {}
[955.992067] (-) TimerEvent: {}
[956.093308] (-) TimerEvent: {}
[956.195169] (-) TimerEvent: {}
[956.295690] (-) TimerEvent: {}
[956.396179] (-) TimerEvent: {}
[956.496840] (-) TimerEvent: {}
[956.598388] (-) TimerEvent: {}
[956.698960] (-) TimerEvent: {}
[956.799437] (-) TimerEvent: {}
[956.899929] (-) TimerEvent: {}
[957.000328] (-) TimerEvent: {}
[957.101392] (-) TimerEvent: {}
[957.202477] (-) TimerEvent: {}
[957.302819] (-) TimerEvent: {}
[957.403246] (-) TimerEvent: {}
[957.503976] (-) TimerEvent: {}
[957.604368] (-) TimerEvent: {}
[957.706629] (-) TimerEvent: {}
[957.807585] (-) TimerEvent: {}
[957.908278] (-) TimerEvent: {}
[958.009113] (-) TimerEvent: {}
[958.110217] (-) TimerEvent: {}
[958.210600] (-) TimerEvent: {}
[958.311389] (-) TimerEvent: {}
[958.412210] (-) TimerEvent: {}
[958.512565] (-) TimerEvent: {}
[958.613122] (-) TimerEvent: {}
[958.714257] (-) TimerEvent: {}
[958.815245] (-) TimerEvent: {}
[958.916041] (-) TimerEvent: {}
[959.016345] (-) TimerEvent: {}
[959.116827] (-) TimerEvent: {}
[959.217281] (-) TimerEvent: {}
[959.318147] (-) TimerEvent: {}
[959.418544] (-) TimerEvent: {}
[959.518837] (-) TimerEvent: {}
[959.620274] (-) TimerEvent: {}
[959.720586] (-) TimerEvent: {}
[959.821038] (-) TimerEvent: {}
[959.922376] (-) TimerEvent: {}
[960.023684] (-) TimerEvent: {}
[960.124842] (-) TimerEvent: {}
[960.225202] (-) TimerEvent: {}
[960.326175] (-) TimerEvent: {}
[960.426528] (-) TimerEvent: {}
[960.527131] (-) TimerEvent: {}
[960.627498] (-) TimerEvent: {}
[960.727783] (-) TimerEvent: {}
[960.828152] (-) TimerEvent: {}
[960.928506] (-) TimerEvent: {}
[961.028783] (-) TimerEvent: {}
[961.129485] (-) TimerEvent: {}
[961.230236] (-) TimerEvent: {}
[961.331612] (-) TimerEvent: {}
[961.433067] (-) TimerEvent: {}
[961.534201] (-) TimerEvent: {}
[961.634633] (-) TimerEvent: {}
[961.735174] (-) TimerEvent: {}
[961.835642] (-) TimerEvent: {}
[961.936487] (-) TimerEvent: {}
[962.037250] (-) TimerEvent: {}
[962.138685] (-) TimerEvent: {}
[962.239300] (-) TimerEvent: {}
[962.339803] (-) TimerEvent: {}
[962.440592] (-) TimerEvent: {}
[962.540986] (-) TimerEvent: {}
[962.642471] (-) TimerEvent: {}
[962.743068] (-) TimerEvent: {}
[962.843417] (-) TimerEvent: {}
[962.944319] (-) TimerEvent: {}
[963.044985] (-) TimerEvent: {}
[963.146340] (-) TimerEvent: {}
[963.246939] (-) TimerEvent: {}
[963.347669] (-) TimerEvent: {}
[963.448379] (-) TimerEvent: {}
[963.548754] (-) TimerEvent: {}
[963.649058] (-) TimerEvent: {}
[963.749418] (-) TimerEvent: {}
[963.850900] (-) TimerEvent: {}
[963.952592] (-) TimerEvent: {}
[964.053204] (-) TimerEvent: {}
[964.153542] (-) TimerEvent: {}
[964.254313] (-) TimerEvent: {}
[964.354929] (-) TimerEvent: {}
[964.455634] (-) TimerEvent: {}
[964.556380] (-) TimerEvent: {}
[964.657023] (-) TimerEvent: {}
[964.757335] (-) TimerEvent: {}
[964.858196] (-) TimerEvent: {}
[964.958762] (-) TimerEvent: {}
[965.059547] (-) TimerEvent: {}
[965.160027] (-) TimerEvent: {}
[965.260897] (-) TimerEvent: {}
[965.361219] (-) TimerEvent: {}
[965.462165] (-) TimerEvent: {}
[965.562476] (-) TimerEvent: {}
[965.662756] (-) TimerEvent: {}
[965.763240] (-) TimerEvent: {}
[965.863723] (-) TimerEvent: {}
[965.964425] (-) TimerEvent: {}
[966.064969] (-) TimerEvent: {}
[966.166442] (-) TimerEvent: {}
[966.267321] (-) TimerEvent: {}
[966.367597] (-) TimerEvent: {}
[966.467969] (-) TimerEvent: {}
[966.568412] (-) TimerEvent: {}
[966.668968] (-) TimerEvent: {}
[966.769309] (-) TimerEvent: {}
[966.870169] (-) TimerEvent: {}
[966.970470] (-) TimerEvent: {}
[967.070784] (-) TimerEvent: {}
[967.171159] (-) TimerEvent: {}
[967.271470] (-) TimerEvent: {}
[967.372069] (-) TimerEvent: {}
[967.472363] (-) TimerEvent: {}
[967.573152] (-) TimerEvent: {}
[967.673452] (-) TimerEvent: {}
[967.774155] (-) TimerEvent: {}
[967.874512] (-) TimerEvent: {}
[967.975027] (-) TimerEvent: {}
[968.075803] (-) TimerEvent: {}
[968.176814] (-) TimerEvent: {}
[968.278189] (-) TimerEvent: {}
[968.379294] (-) TimerEvent: {}
[968.479826] (-) TimerEvent: {}
[968.580181] (-) TimerEvent: {}
[968.680499] (-) TimerEvent: {}
[968.781512] (-) TimerEvent: {}
[968.882416] (-) TimerEvent: {}
[968.982817] (-) TimerEvent: {}
[969.083555] (-) TimerEvent: {}
[969.184488] (-) TimerEvent: {}
[969.285221] (-) TimerEvent: {}
[969.386324] (-) TimerEvent: {}
[969.486636] (-) TimerEvent: {}
[969.587118] (-) TimerEvent: {}
[969.687417] (-) TimerEvent: {}
[969.787871] (-) TimerEvent: {}
[969.889009] (-) TimerEvent: {}
[969.990952] (-) TimerEvent: {}
[970.093008] (-) TimerEvent: {}
[970.197088] (-) TimerEvent: {}
[970.298203] (-) TimerEvent: {}
[970.398719] (-) TimerEvent: {}
[970.499077] (-) TimerEvent: {}
[970.599786] (-) TimerEvent: {}
[970.701051] (-) TimerEvent: {}
[970.802419] (-) TimerEvent: {}
[970.903690] (-) TimerEvent: {}
[971.004224] (-) TimerEvent: {}
[971.104631] (-) TimerEvent: {}
[971.205008] (-) TimerEvent: {}
[971.305298] (-) TimerEvent: {}
[971.406168] (-) TimerEvent: {}
[971.506887] (-) TimerEvent: {}
[971.607194] (-) TimerEvent: {}
[971.707668] (-) TimerEvent: {}
[971.808013] (-) TimerEvent: {}
[971.908624] (-) TimerEvent: {}
[972.010421] (-) TimerEvent: {}
[972.111707] (-) TimerEvent: {}
[972.212269] (-) TimerEvent: {}
[972.314426] (-) TimerEvent: {}
[972.415665] (-) TimerEvent: {}
[972.515956] (-) TimerEvent: {}
[972.616276] (-) TimerEvent: {}
[972.717032] (-) TimerEvent: {}
[972.818175] (-) TimerEvent: {}
[972.918581] (-) TimerEvent: {}
[973.018899] (-) TimerEvent: {}
[973.119241] (-) TimerEvent: {}
[973.219837] (-) TimerEvent: {}
[973.320231] (-) TimerEvent: {}
[973.420568] (-) TimerEvent: {}
[973.521267] (-) TimerEvent: {}
[973.622184] (-) TimerEvent: {}
[973.722687] (-) TimerEvent: {}
[973.823223] (-) TimerEvent: {}
[973.923592] (-) TimerEvent: {}
[974.024112] (-) TimerEvent: {}
[974.124670] (-) TimerEvent: {}
[974.225005] (-) TimerEvent: {}
[974.326137] (-) TimerEvent: {}
[974.426493] (-) TimerEvent: {}
[974.527212] (-) TimerEvent: {}
[974.628118] (-) TimerEvent: {}
[974.728691] (-) TimerEvent: {}
[974.829588] (-) TimerEvent: {}
[974.930861] (-) TimerEvent: {}
[975.031494] (-) TimerEvent: {}
[975.131872] (-) TimerEvent: {}
[975.232228] (-) TimerEvent: {}
[975.332669] (-) TimerEvent: {}
[975.433104] (-) TimerEvent: {}
[975.534173] (-) TimerEvent: {}
[975.634505] (-) TimerEvent: {}
[975.734924] (-) TimerEvent: {}
[975.835854] (-) TimerEvent: {}
[975.936425] (-) TimerEvent: {}
[976.036781] (-) TimerEvent: {}
[976.137161] (-) TimerEvent: {}
[976.238362] (-) TimerEvent: {}
[976.339179] (-) TimerEvent: {}
[976.439787] (-) TimerEvent: {}
[976.540498] (-) TimerEvent: {}
[976.640967] (-) TimerEvent: {}
[976.742149] (-) TimerEvent: {}
[976.842786] (-) TimerEvent: {}
[976.944034] (-) TimerEvent: {}
[977.044877] (-) TimerEvent: {}
[977.145386] (-) TimerEvent: {}
[977.246285] (-) TimerEvent: {}
[977.347020] (-) TimerEvent: {}
[977.447825] (-) TimerEvent: {}
[977.548753] (-) TimerEvent: {}
[977.649564] (-) TimerEvent: {}
[977.750906] (-) TimerEvent: {}
[977.851876] (-) TimerEvent: {}
[977.952190] (-) TimerEvent: {}
[978.052676] (-) TimerEvent: {}
[978.153141] (-) TimerEvent: {}
[978.254122] (-) TimerEvent: {}
[978.354424] (-) TimerEvent: {}
[978.454767] (-) TimerEvent: {}
[978.555339] (-) TimerEvent: {}
[978.655950] (-) TimerEvent: {}
[978.756348] (-) TimerEvent: {}
[978.856638] (-) TimerEvent: {}
[978.957593] (-) TimerEvent: {}
[979.058200] (-) TimerEvent: {}
[979.158877] (-) TimerEvent: {}
[979.259699] (-) TimerEvent: {}
[979.360107] (-) TimerEvent: {}
[979.460478] (-) TimerEvent: {}
[979.561029] (-) TimerEvent: {}
[979.661272] (-) TimerEvent: {}
[979.762282] (-) TimerEvent: {}
[979.862788] (-) TimerEvent: {}
[979.963057] (-) TimerEvent: {}
[980.063393] (-) TimerEvent: {}
[980.163694] (-) TimerEvent: {}
[980.264298] (-) TimerEvent: {}
[980.364965] (-) TimerEvent: {}
[980.465322] (-) TimerEvent: {}
[980.566332] (-) TimerEvent: {}
[980.667149] (-) TimerEvent: {}
[980.768166] (-) TimerEvent: {}
[980.869059] (-) TimerEvent: {}
[980.970132] (-) TimerEvent: {}
[981.070600] (-) TimerEvent: {}
[981.171393] (-) TimerEvent: {}
[981.272129] (-) TimerEvent: {}
[981.372861] (-) TimerEvent: {}
[981.474146] (-) TimerEvent: {}
[981.574494] (-) TimerEvent: {}
[981.674800] (-) TimerEvent: {}
[981.775146] (-) TimerEvent: {}
[981.875469] (-) TimerEvent: {}
[981.975747] (-) TimerEvent: {}
[982.076034] (-) TimerEvent: {}
[982.176418] (-) TimerEvent: {}
[982.276944] (-) TimerEvent: {}
[982.378364] (-) TimerEvent: {}
[982.478880] (-) TimerEvent: {}
[982.579615] (-) TimerEvent: {}
[982.679911] (-) TimerEvent: {}
[982.780430] (-) TimerEvent: {}
[982.881142] (-) TimerEvent: {}
[982.982565] (-) TimerEvent: {}
[983.083229] (-) TimerEvent: {}
[983.183531] (-) TimerEvent: {}
[983.283879] (-) TimerEvent: {}
[983.384175] (-) TimerEvent: {}
[983.484659] (-) TimerEvent: {}
[983.585622] (-) TimerEvent: {}
[983.687679] (-) TimerEvent: {}
[983.788671] (-) TimerEvent: {}
[983.889076] (-) TimerEvent: {}
[983.989463] (-) TimerEvent: {}
[984.090366] (-) TimerEvent: {}
[984.190752] (-) TimerEvent: {}
[984.291461] (-) TimerEvent: {}
[984.391719] (-) TimerEvent: {}
[984.492303] (-) TimerEvent: {}
[984.593357] (-) TimerEvent: {}
[984.694170] (-) TimerEvent: {}
[984.794585] (-) TimerEvent: {}
[984.894969] (-) TimerEvent: {}
[984.995302] (-) TimerEvent: {}
[985.095843] (-) TimerEvent: {}
[985.196678] (-) TimerEvent: {}
[985.297004] (-) TimerEvent: {}
[985.398137] (-) TimerEvent: {}
[985.498418] (-) TimerEvent: {}
[985.598968] (-) TimerEvent: {}
[985.699585] (-) TimerEvent: {}
[985.799898] (-) TimerEvent: {}
[985.900211] (-) TimerEvent: {}
[986.000688] (-) TimerEvent: {}
[986.100973] (-) TimerEvent: {}
[986.201265] (-) TimerEvent: {}
[986.301521] (-) TimerEvent: {}
[986.402189] (-) TimerEvent: {}
[986.502576] (-) TimerEvent: {}
[986.602932] (-) TimerEvent: {}
[986.703207] (-) TimerEvent: {}
[986.803481] (-) TimerEvent: {}
[986.904363] (-) TimerEvent: {}
[987.005264] (-) TimerEvent: {}
[987.106418] (-) TimerEvent: {}
[987.207849] (-) TimerEvent: {}
[987.308239] (-) TimerEvent: {}
[987.409146] (-) TimerEvent: {}
[987.510225] (-) TimerEvent: {}
[987.610778] (-) TimerEvent: {}
[987.711508] (-) TimerEvent: {}
[987.812025] (-) TimerEvent: {}
[987.912634] (-) TimerEvent: {}
[988.013329] (-) TimerEvent: {}
[988.114190] (-) TimerEvent: {}
[988.214968] (-) TimerEvent: {}
[988.315701] (-) TimerEvent: {}
[988.416055] (-) TimerEvent: {}
[988.516735] (-) TimerEvent: {}
[988.617278] (-) TimerEvent: {}
[988.718164] (-) TimerEvent: {}
[988.818564] (-) TimerEvent: {}
[988.918884] (-) TimerEvent: {}
[989.019203] (-) TimerEvent: {}
[989.119807] (-) TimerEvent: {}
[989.220770] (-) TimerEvent: {}
[989.321407] (-) TimerEvent: {}
[989.422804] (-) TimerEvent: {}
[989.523534] (-) TimerEvent: {}
[989.623968] (-) TimerEvent: {}
[989.724243] (-) TimerEvent: {}
[989.824984] (-) TimerEvent: {}
[989.925341] (-) TimerEvent: {}
[990.026159] (-) TimerEvent: {}
[990.126482] (-) TimerEvent: {}
[990.226865] (-) TimerEvent: {}
[990.327576] (-) TimerEvent: {}
[990.427888] (-) TimerEvent: {}
[990.528270] (-) TimerEvent: {}
[990.629175] (-) TimerEvent: {}
[990.730173] (-) TimerEvent: {}
[990.831077] (-) TimerEvent: {}
[990.934235] (-) TimerEvent: {}
[991.037316] (-) TimerEvent: {}
[991.138164] (-) TimerEvent: {}
[991.238615] (-) TimerEvent: {}
[991.339050] (-) TimerEvent: {}
[991.439338] (-) TimerEvent: {}
[991.539606] (-) TimerEvent: {}
[991.639876] (-) TimerEvent: {}
[991.740230] (-) TimerEvent: {}
[991.841155] (-) TimerEvent: {}
[991.942446] (-) TimerEvent: {}
[992.043705] (-) TimerEvent: {}
[992.144688] (-) TimerEvent: {}
[992.246335] (-) TimerEvent: {}
[992.347104] (-) TimerEvent: {}
[992.447647] (-) TimerEvent: {}
[992.548346] (-) TimerEvent: {}
[992.648630] (-) TimerEvent: {}
[992.749337] (-) TimerEvent: {}
[992.850186] (-) TimerEvent: {}
[992.950546] (-) TimerEvent: {}
[993.051402] (-) TimerEvent: {}
[993.152107] (-) TimerEvent: {}
[993.252539] (-) TimerEvent: {}
[993.353015] (-) TimerEvent: {}
[993.453325] (-) TimerEvent: {}
[993.554153] (-) TimerEvent: {}
[993.654479] (-) TimerEvent: {}
[993.755133] (-) TimerEvent: {}
[993.855606] (-) TimerEvent: {}
[993.955881] (-) TimerEvent: {}
[994.056204] (-) TimerEvent: {}
[994.156855] (-) TimerEvent: {}
[994.257587] (-) TimerEvent: {}
[994.359180] (-) TimerEvent: {}
[994.460141] (-) TimerEvent: {}
[994.561196] (-) TimerEvent: {}
[994.662218] (-) TimerEvent: {}
[994.762903] (-) TimerEvent: {}
[994.863778] (-) TimerEvent: {}
[994.964458] (-) TimerEvent: {}
[995.065001] (-) TimerEvent: {}
[995.165327] (-) TimerEvent: {}
[995.266259] (-) TimerEvent: {}
[995.366905] (-) TimerEvent: {}
[995.467303] (-) TimerEvent: {}
[995.568043] (-) TimerEvent: {}
[995.669010] (-) TimerEvent: {}
[995.770738] (-) TimerEvent: {}
[995.871931] (-) TimerEvent: {}
[995.972876] (-) TimerEvent: {}
[996.074772] (-) TimerEvent: {}
[996.176182] (-) TimerEvent: {}
[996.276837] (-) TimerEvent: {}
[996.377409] (-) TimerEvent: {}
[996.478224] (-) TimerEvent: {}
[996.579102] (-) TimerEvent: {}
[996.680034] (-) TimerEvent: {}
[996.780590] (-) TimerEvent: {}
[996.881000] (-) TimerEvent: {}
[996.981723] (-) TimerEvent: {}
[997.082503] (-) TimerEvent: {}
[997.182822] (-) TimerEvent: {}
[997.283352] (-) TimerEvent: {}
[997.384073] (-) TimerEvent: {}
[997.484627] (-) TimerEvent: {}
[997.585338] (-) TimerEvent: {}
[997.686257] (-) TimerEvent: {}
[997.786730] (-) TimerEvent: {}
[997.887418] (-) TimerEvent: {}
[997.988748] (-) TimerEvent: {}
[998.090204] (-) TimerEvent: {}
[998.190657] (-) TimerEvent: {}
[998.291658] (-) TimerEvent: {}
[998.392850] (-) TimerEvent: {}
[998.494146] (-) TimerEvent: {}
[998.594404] (-) TimerEvent: {}
[998.695063] (-) TimerEvent: {}
[998.795350] (-) TimerEvent: {}
[998.895658] (-) TimerEvent: {}
[998.996272] (-) TimerEvent: {}
[999.096873] (-) TimerEvent: {}
[999.197163] (-) TimerEvent: {}
[999.298143] (-) TimerEvent: {}
[999.398483] (-) TimerEvent: {}
[999.498822] (-) TimerEvent: {}
[999.599231] (-) TimerEvent: {}
[999.699707] (-) TimerEvent: {}
[999.800464] (-) TimerEvent: {}
[999.901217] (-) TimerEvent: {}
[1000.002165] (-) TimerEvent: {}
[1000.102516] (-) TimerEvent: {}
[1000.203292] (-) TimerEvent: {}
[1000.304592] (-) TimerEvent: {}
[1000.405411] (-) TimerEvent: {}
[1000.506390] (-) TimerEvent: {}
[1000.606811] (-) TimerEvent: {}
[1000.707265] (-) TimerEvent: {}
[1000.807647] (-) TimerEvent: {}
[1000.908181] (-) TimerEvent: {}
[1001.008654] (-) TimerEvent: {}
[1001.110871] (-) TimerEvent: {}
[1001.211367] (-) TimerEvent: {}
[1001.312085] (-) TimerEvent: {}
[1001.412596] (-) TimerEvent: {}
[1001.513145] (-) TimerEvent: {}
[1001.613707] (-) TimerEvent: {}
[1001.714273] (-) TimerEvent: {}
[1001.814672] (-) TimerEvent: {}
[1001.836472] (ros1_bridge) StdoutLine: {'line': b'[ 98%] Built target ros1_bridge\n'}
[1001.869084] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target static_bridge\x1b[0m\n'}
[1001.869250] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target parameter_bridge\x1b[0m\n'}
[1001.914815] (-) TimerEvent: {}
[1002.015244] (-) TimerEvent: {}
[1002.115715] (-) TimerEvent: {}
[1002.216218] (-) TimerEvent: {}
[1002.223424] (ros1_bridge) StdoutLine: {'line': b'[ 99%] \x1b[32mBuilding CXX object CMakeFiles/parameter_bridge.dir/src/parameter_bridge.cpp.o\x1b[0m\n'}
[1002.224540] (ros1_bridge) StdoutLine: {'line': b'[ 99%] \x1b[32mBuilding CXX object CMakeFiles/static_bridge.dir/src/static_bridge.cpp.o\x1b[0m\n'}
[1002.316468] (-) TimerEvent: {}
[1002.417301] (-) TimerEvent: {}
[1002.519464] (-) TimerEvent: {}
[1002.619864] (-) TimerEvent: {}
[1002.720262] (-) TimerEvent: {}
[1002.821042] (-) TimerEvent: {}
[1002.922480] (-) TimerEvent: {}
[1003.023665] (-) TimerEvent: {}
[1003.124692] (-) TimerEvent: {}
[1003.225168] (-) TimerEvent: {}
[1003.326167] (-) TimerEvent: {}
[1003.426729] (-) TimerEvent: {}
[1003.527172] (-) TimerEvent: {}
[1003.627709] (-) TimerEvent: {}
[1003.728249] (-) TimerEvent: {}
[1003.828863] (-) TimerEvent: {}
[1003.929234] (-) TimerEvent: {}
[1004.030428] (-) TimerEvent: {}
[1004.130930] (-) TimerEvent: {}
[1004.231403] (-) TimerEvent: {}
[1004.331888] (-) TimerEvent: {}
[1004.432356] (-) TimerEvent: {}
[1004.532788] (-) TimerEvent: {}
[1004.633466] (-) TimerEvent: {}
[1004.734415] (-) TimerEvent: {}
[1004.834912] (-) TimerEvent: {}
[1004.935555] (-) TimerEvent: {}
[1005.036015] (-) TimerEvent: {}
[1005.136550] (-) TimerEvent: {}
[1005.237298] (-) TimerEvent: {}
[1005.338177] (-) TimerEvent: {}
[1005.438672] (-) TimerEvent: {}
[1005.539135] (-) TimerEvent: {}
[1005.639501] (-) TimerEvent: {}
[1005.739926] (-) TimerEvent: {}
[1005.840344] (-) TimerEvent: {}
[1005.941228] (-) TimerEvent: {}
[1006.042150] (-) TimerEvent: {}
[1006.142586] (-) TimerEvent: {}
[1006.243093] (-) TimerEvent: {}
[1006.343670] (-) TimerEvent: {}
[1006.444092] (-) TimerEvent: {}
[1006.548735] (-) TimerEvent: {}
[1006.649143] (-) TimerEvent: {}
[1006.750183] (-) TimerEvent: {}
[1006.850861] (-) TimerEvent: {}
[1006.951973] (-) TimerEvent: {}
[1007.052592] (-) TimerEvent: {}
[1007.153113] (-) TimerEvent: {}
[1007.254192] (-) TimerEvent: {}
[1007.354741] (-) TimerEvent: {}
[1007.455323] (-) TimerEvent: {}
[1007.519048] (ros1_bridge) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable static_bridge\x1b[0m\n'}
[1007.555520] (-) TimerEvent: {}
[1007.655960] (-) TimerEvent: {}
[1007.698469] (ros1_bridge) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable parameter_bridge\x1b[0m\n'}
[1007.756118] (-) TimerEvent: {}
[1007.856513] (-) TimerEvent: {}
[1007.956979] (-) TimerEvent: {}
[1008.057440] (-) TimerEvent: {}
[1008.158381] (-) TimerEvent: {}
[1008.258755] (-) TimerEvent: {}
[1008.359308] (-) TimerEvent: {}
[1008.459834] (-) TimerEvent: {}
[1008.560415] (-) TimerEvent: {}
[1008.662218] (-) TimerEvent: {}
[1008.762788] (-) TimerEvent: {}
[1008.863632] (-) TimerEvent: {}
[1008.964231] (-) TimerEvent: {}
[1009.065055] (-) TimerEvent: {}
[1009.166165] (-) TimerEvent: {}
[1009.266654] (-) TimerEvent: {}
[1009.367377] (-) TimerEvent: {}
[1009.467817] (-) TimerEvent: {}
[1009.568245] (-) TimerEvent: {}
[1009.668683] (-) TimerEvent: {}
[1009.769326] (-) TimerEvent: {}
[1009.870162] (-) TimerEvent: {}
[1009.970577] (-) TimerEvent: {}
[1010.071010] (-) TimerEvent: {}
[1010.171383] (-) TimerEvent: {}
[1010.271896] (-) TimerEvent: {}
[1010.372722] (-) TimerEvent: {}
[1010.473228] (-) TimerEvent: {}
[1010.574182] (-) TimerEvent: {}
[1010.674567] (-) TimerEvent: {}
[1010.774958] (-) TimerEvent: {}
[1010.875986] (-) TimerEvent: {}
[1010.976418] (-) TimerEvent: {}
[1011.076725] (-) TimerEvent: {}
[1011.177163] (-) TimerEvent: {}
[1011.278215] (-) TimerEvent: {}
[1011.378643] (-) TimerEvent: {}
[1011.479172] (-) TimerEvent: {}
[1011.579642] (-) TimerEvent: {}
[1011.680044] (-) TimerEvent: {}
[1011.780658] (-) TimerEvent: {}
[1011.881016] (-) TimerEvent: {}
[1011.982108] (-) TimerEvent: {}
[1012.082669] (-) TimerEvent: {}
[1012.183327] (-) TimerEvent: {}
[1012.283773] (-) TimerEvent: {}
[1012.384457] (-) TimerEvent: {}
[1012.485096] (-) TimerEvent: {}
[1012.586183] (-) TimerEvent: {}
[1012.686640] (-) TimerEvent: {}
[1012.787061] (-) TimerEvent: {}
[1012.887486] (-) TimerEvent: {}
[1012.987952] (-) TimerEvent: {}
[1013.088961] (-) TimerEvent: {}
[1013.190161] (-) TimerEvent: {}
[1013.290595] (-) TimerEvent: {}
[1013.391451] (-) TimerEvent: {}
[1013.492021] (-) TimerEvent: {}
[1013.592788] (-) TimerEvent: {}
[1013.694224] (-) TimerEvent: {}
[1013.794707] (-) TimerEvent: {}
[1013.895336] (-) TimerEvent: {}
[1013.996033] (-) TimerEvent: {}
[1014.096494] (-) TimerEvent: {}
[1014.196942] (-) TimerEvent: {}
[1014.297390] (-) TimerEvent: {}
[1014.398431] (-) TimerEvent: {}
[1014.498979] (-) TimerEvent: {}
[1014.599484] (-) TimerEvent: {}
[1014.699906] (-) TimerEvent: {}
[1014.800702] (-) TimerEvent: {}
[1014.901184] (-) TimerEvent: {}
[1015.002170] (-) TimerEvent: {}
[1015.102555] (-) TimerEvent: {}
[1015.202920] (-) TimerEvent: {}
[1015.303382] (-) TimerEvent: {}
[1015.403791] (-) TimerEvent: {}
[1015.504193] (-) TimerEvent: {}
[1015.604952] (-) TimerEvent: {}
[1015.705383] (-) TimerEvent: {}
[1015.806198] (-) TimerEvent: {}
[1015.906584] (-) TimerEvent: {}
[1016.007250] (-) TimerEvent: {}
[1016.107686] (-) TimerEvent: {}
[1016.208134] (-) TimerEvent: {}
[1016.308861] (-) TimerEvent: {}
[1016.409302] (-) TimerEvent: {}
[1016.510957] (-) TimerEvent: {}
[1016.611394] (-) TimerEvent: {}
[1016.711856] (-) TimerEvent: {}
[1016.814842] (-) TimerEvent: {}
[1016.915374] (-) TimerEvent: {}
[1017.015788] (-) TimerEvent: {}
[1017.116222] (-) TimerEvent: {}
[1017.217086] (-) TimerEvent: {}
[1017.319098] (-) TimerEvent: {}
[1017.419618] (-) TimerEvent: {}
[1017.519979] (-) TimerEvent: {}
[1017.620926] (-) TimerEvent: {}
[1017.721378] (-) TimerEvent: {}
[1017.822990] (-) TimerEvent: {}
[1017.923347] (-) TimerEvent: {}
[1018.023730] (-) TimerEvent: {}
[1018.124148] (-) TimerEvent: {}
[1018.224846] (-) TimerEvent: {}
[1018.325218] (-) TimerEvent: {}
[1018.426142] (-) TimerEvent: {}
[1018.526567] (-) TimerEvent: {}
[1018.627547] (-) TimerEvent: {}
[1018.728042] (-) TimerEvent: {}
[1018.828519] (-) TimerEvent: {}
[1018.929129] (-) TimerEvent: {}
[1019.029551] (-) TimerEvent: {}
[1019.130241] (-) TimerEvent: {}
[1019.230627] (-) TimerEvent: {}
[1019.331088] (-) TimerEvent: {}
[1019.431600] (-) TimerEvent: {}
[1019.532389] (-) TimerEvent: {}
[1019.632887] (-) TimerEvent: {}
[1019.733396] (-) TimerEvent: {}
[1019.834412] (-) TimerEvent: {}
[1019.934914] (-) TimerEvent: {}
[1020.035420] (-) TimerEvent: {}
[1020.135855] (-) TimerEvent: {}
[1020.236330] (-) TimerEvent: {}
[1020.336833] (-) TimerEvent: {}
[1020.437332] (-) TimerEvent: {}
[1020.538174] (-) TimerEvent: {}
[1020.638686] (-) TimerEvent: {}
[1020.739115] (-) TimerEvent: {}
[1020.840156] (-) TimerEvent: {}
[1020.941321] (-) TimerEvent: {}
[1021.043847] (-) TimerEvent: {}
[1021.144712] (-) TimerEvent: {}
[1021.245100] (-) TimerEvent: {}
[1021.345477] (-) TimerEvent: {}
[1021.446148] (-) TimerEvent: {}
[1021.547306] (-) TimerEvent: {}
[1021.647708] (-) TimerEvent: {}
[1021.748180] (-) TimerEvent: {}
[1021.854291] (-) TimerEvent: {}
[1021.954794] (-) TimerEvent: {}
[1022.055153] (-) TimerEvent: {}
[1022.155564] (-) TimerEvent: {}
[1022.256003] (-) TimerEvent: {}
[1022.356355] (-) TimerEvent: {}
[1022.456781] (-) TimerEvent: {}
[1022.557163] (-) TimerEvent: {}
[1022.657544] (-) TimerEvent: {}
[1022.758296] (-) TimerEvent: {}
[1022.858995] (-) TimerEvent: {}
[1022.959659] (-) TimerEvent: {}
[1023.060061] (-) TimerEvent: {}
[1023.160416] (-) TimerEvent: {}
[1023.260828] (-) TimerEvent: {}
[1023.361323] (-) TimerEvent: {}
[1023.462209] (-) TimerEvent: {}
[1023.562614] (-) TimerEvent: {}
[1023.662990] (-) TimerEvent: {}
[1023.763362] (-) TimerEvent: {}
[1023.864807] (-) TimerEvent: {}
[1023.965370] (-) TimerEvent: {}
[1024.066170] (-) TimerEvent: {}
[1024.166657] (-) TimerEvent: {}
[1024.267098] (-) TimerEvent: {}
[1024.367448] (-) TimerEvent: {}
[1024.468119] (-) TimerEvent: {}
[1024.568492] (-) TimerEvent: {}
[1024.668997] (-) TimerEvent: {}
[1024.769409] (-) TimerEvent: {}
[1024.870298] (-) TimerEvent: {}
[1024.970663] (-) TimerEvent: {}
[1025.071266] (-) TimerEvent: {}
[1025.171616] (-) TimerEvent: {}
[1025.272014] (-) TimerEvent: {}
[1025.372423] (-) TimerEvent: {}
[1025.472932] (-) TimerEvent: {}
[1025.574155] (-) TimerEvent: {}
[1025.674580] (-) TimerEvent: {}
[1025.775038] (-) TimerEvent: {}
[1025.875619] (-) TimerEvent: {}
[1025.976037] (-) TimerEvent: {}
[1026.076421] (-) TimerEvent: {}
[1026.177044] (-) TimerEvent: {}
[1026.277411] (-) TimerEvent: {}
[1026.378416] (-) TimerEvent: {}
[1026.479456] (-) TimerEvent: {}
[1026.579821] (-) TimerEvent: {}
[1026.681864] (-) TimerEvent: {}
[1026.782353] (-) TimerEvent: {}
[1026.882668] (-) TimerEvent: {}
[1026.983041] (-) TimerEvent: {}
[1027.083329] (-) TimerEvent: {}
[1027.183670] (-) TimerEvent: {}
[1027.284101] (-) TimerEvent: {}
[1027.384494] (-) TimerEvent: {}
[1027.484896] (-) TimerEvent: {}
[1027.585320] (-) TimerEvent: {}
[1027.686168] (-) TimerEvent: {}
[1027.786964] (-) TimerEvent: {}
[1027.887341] (-) TimerEvent: {}
[1027.987709] (-) TimerEvent: {}
[1028.088100] (-) TimerEvent: {}
[1028.188936] (-) TimerEvent: {}
[1028.289370] (-) TimerEvent: {}
[1028.390170] (-) TimerEvent: {}
[1028.490595] (-) TimerEvent: {}
[1028.591106] (-) TimerEvent: {}
[1028.691503] (-) TimerEvent: {}
[1028.792237] (-) TimerEvent: {}
[1028.893168] (-) TimerEvent: {}
[1028.994154] (-) TimerEvent: {}
[1029.094549] (-) TimerEvent: {}
[1029.194934] (-) TimerEvent: {}
[1029.295585] (-) TimerEvent: {}
[1029.396025] (-) TimerEvent: {}
[1029.496404] (-) TimerEvent: {}
[1029.597251] (-) TimerEvent: {}
[1029.698176] (-) TimerEvent: {}
[1029.798610] (-) TimerEvent: {}
[1029.899008] (-) TimerEvent: {}
[1029.999385] (-) TimerEvent: {}
[1030.099778] (-) TimerEvent: {}
[1030.200174] (-) TimerEvent: {}
[1030.300562] (-) TimerEvent: {}
[1030.401349] (-) TimerEvent: {}
[1030.502148] (-) TimerEvent: {}
[1030.602627] (-) TimerEvent: {}
[1030.703114] (-) TimerEvent: {}
[1030.803484] (-) TimerEvent: {}
[1030.903885] (-) TimerEvent: {}
[1031.004799] (-) TimerEvent: {}
[1031.105369] (-) TimerEvent: {}
[1031.206179] (-) TimerEvent: {}
[1031.306552] (-) TimerEvent: {}
[1031.406993] (-) TimerEvent: {}
[1031.507413] (-) TimerEvent: {}
[1031.607865] (-) TimerEvent: {}
[1031.708296] (-) TimerEvent: {}
[1031.808810] (-) TimerEvent: {}
[1031.910182] (-) TimerEvent: {}
[1032.010591] (-) TimerEvent: {}
[1032.111117] (-) TimerEvent: {}
[1032.211506] (-) TimerEvent: {}
[1032.312025] (-) TimerEvent: {}
[1032.412539] (-) TimerEvent: {}
[1032.512908] (-) TimerEvent: {}
[1032.614504] (-) TimerEvent: {}
[1032.715084] (-) TimerEvent: {}
[1032.815402] (-) TimerEvent: {}
[1032.915720] (-) TimerEvent: {}
[1033.016103] (-) TimerEvent: {}
[1033.116748] (-) TimerEvent: {}
[1033.217147] (-) TimerEvent: {}
[1033.318156] (-) TimerEvent: {}
[1033.418625] (-) TimerEvent: {}
[1033.519146] (-) TimerEvent: {}
[1033.619705] (-) TimerEvent: {}
[1033.720167] (-) TimerEvent: {}
[1033.820749] (-) TimerEvent: {}
[1033.922154] (-) TimerEvent: {}
[1034.022592] (-) TimerEvent: {}
[1034.123293] (-) TimerEvent: {}
[1034.223784] (-) TimerEvent: {}
[1034.324175] (-) TimerEvent: {}
[1034.424868] (-) TimerEvent: {}
[1034.525375] (-) TimerEvent: {}
[1034.626286] (-) TimerEvent: {}
[1034.726689] (-) TimerEvent: {}
[1034.827471] (-) TimerEvent: {}
[1034.928470] (-) TimerEvent: {}
[1035.028978] (-) TimerEvent: {}
[1035.129492] (-) TimerEvent: {}
[1035.230182] (-) TimerEvent: {}
[1035.330721] (-) TimerEvent: {}
[1035.431235] (-) TimerEvent: {}
[1035.531928] (-) TimerEvent: {}
[1035.632766] (-) TimerEvent: {}
[1035.733533] (-) TimerEvent: {}
[1035.834374] (-) TimerEvent: {}
[1035.934957] (-) TimerEvent: {}
[1036.035452] (-) TimerEvent: {}
[1036.135977] (-) TimerEvent: {}
[1036.236400] (-) TimerEvent: {}
[1036.336917] (-) TimerEvent: {}
[1036.438159] (-) TimerEvent: {}
[1036.538542] (-) TimerEvent: {}
[1036.639145] (-) TimerEvent: {}
[1036.739562] (-) TimerEvent: {}
[1036.839935] (-) TimerEvent: {}
[1036.940345] (-) TimerEvent: {}
[1037.040761] (-) TimerEvent: {}
[1037.141170] (-) TimerEvent: {}
[1037.242171] (-) TimerEvent: {}
[1037.342594] (-) TimerEvent: {}
[1037.443045] (-) TimerEvent: {}
[1037.543544] (-) TimerEvent: {}
[1037.643907] (-) TimerEvent: {}
[1037.744359] (-) TimerEvent: {}
[1037.844884] (-) TimerEvent: {}
[1037.946147] (-) TimerEvent: {}
[1038.046540] (-) TimerEvent: {}
[1038.149104] (-) TimerEvent: {}
[1038.250126] (-) TimerEvent: {}
[1038.350518] (-) TimerEvent: {}
[1038.450940] (-) TimerEvent: {}
[1038.551383] (-) TimerEvent: {}
[1038.651792] (-) TimerEvent: {}
[1038.752143] (-) TimerEvent: {}
[1038.852920] (-) TimerEvent: {}
[1038.954402] (-) TimerEvent: {}
[1039.054763] (-) TimerEvent: {}
[1039.155217] (-) TimerEvent: {}
[1039.255649] (-) TimerEvent: {}
[1039.356022] (-) TimerEvent: {}
[1039.456974] (-) TimerEvent: {}
[1039.558167] (-) TimerEvent: {}
[1039.658652] (-) TimerEvent: {}
[1039.759832] (-) TimerEvent: {}
[1039.860262] (-) TimerEvent: {}
[1039.960854] (-) TimerEvent: {}
[1040.061245] (-) TimerEvent: {}
[1040.162160] (-) TimerEvent: {}
[1040.262562] (-) TimerEvent: {}
[1040.362981] (-) TimerEvent: {}
[1040.463355] (-) TimerEvent: {}
[1040.563748] (-) TimerEvent: {}
[1040.664198] (-) TimerEvent: {}
[1040.765208] (-) TimerEvent: {}
[1040.866156] (-) TimerEvent: {}
[1040.966533] (-) TimerEvent: {}
[1041.066941] (-) TimerEvent: {}
[1041.167464] (-) TimerEvent: {}
[1041.267948] (-) TimerEvent: {}
[1041.369146] (-) TimerEvent: {}
[1041.469506] (-) TimerEvent: {}
[1041.570144] (-) TimerEvent: {}
[1041.670568] (-) TimerEvent: {}
[1041.770979] (-) TimerEvent: {}
[1041.871687] (-) TimerEvent: {}
[1041.972160] (-) TimerEvent: {}
[1042.072503] (-) TimerEvent: {}
[1042.173193] (-) TimerEvent: {}
[1042.274143] (-) TimerEvent: {}
[1042.374532] (-) TimerEvent: {}
[1042.416759] (ros1_bridge) StdoutLine: {'line': b'[100%] Built target static_bridge\n'}
[1042.450872] (ros1_bridge) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target dynamic_bridge\x1b[0m\n'}
[1042.474654] (-) TimerEvent: {}
[1042.535318] (ros1_bridge) StdoutLine: {'line': b'[100%] \x1b[32mBuilding CXX object CMakeFiles/dynamic_bridge.dir/src/dynamic_bridge.cpp.o\x1b[0m\n'}
[1042.574769] (-) TimerEvent: {}
[1042.675180] (-) TimerEvent: {}
[1042.775560] (-) TimerEvent: {}
[1042.807290] (ros1_bridge) StdoutLine: {'line': b'[100%] Built target parameter_bridge\n'}
[1042.875723] (-) TimerEvent: {}
[1042.976212] (-) TimerEvent: {}
[1043.076635] (-) TimerEvent: {}
[1043.177285] (-) TimerEvent: {}
[1043.278275] (-) TimerEvent: {}
[1043.378575] (-) TimerEvent: {}
[1043.479086] (-) TimerEvent: {}
[1043.579853] (-) TimerEvent: {}
[1043.680603] (-) TimerEvent: {}
[1043.781123] (-) TimerEvent: {}
[1043.882166] (-) TimerEvent: {}
[1043.982512] (-) TimerEvent: {}
[1044.082787] (-) TimerEvent: {}
[1044.183077] (-) TimerEvent: {}
[1044.283391] (-) TimerEvent: {}
[1044.383663] (-) TimerEvent: {}
[1044.484192] (-) TimerEvent: {}
[1044.584512] (-) TimerEvent: {}
[1044.684799] (-) TimerEvent: {}
[1044.785070] (-) TimerEvent: {}
[1044.886361] (-) TimerEvent: {}
[1044.987241] (-) TimerEvent: {}
[1045.088041] (-) TimerEvent: {}
[1045.188913] (-) TimerEvent: {}
[1045.289380] (-) TimerEvent: {}
[1045.390534] (-) TimerEvent: {}
[1045.491158] (-) TimerEvent: {}
[1045.592028] (-) TimerEvent: {}
[1045.692736] (-) TimerEvent: {}
[1045.793198] (-) TimerEvent: {}
[1045.894172] (-) TimerEvent: {}
[1045.994443] (-) TimerEvent: {}
[1046.094843] (-) TimerEvent: {}
[1046.195380] (-) TimerEvent: {}
[1046.295698] (-) TimerEvent: {}
[1046.395991] (-) TimerEvent: {}
[1046.496866] (-) TimerEvent: {}
[1046.597275] (-) TimerEvent: {}
[1046.698193] (-) TimerEvent: {}
[1046.798585] (-) TimerEvent: {}
[1046.898923] (-) TimerEvent: {}
[1046.999274] (-) TimerEvent: {}
[1047.099665] (-) TimerEvent: {}
[1047.200190] (-) TimerEvent: {}
[1047.240385] (ros1_bridge) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable dynamic_bridge\x1b[0m\n'}
[1047.300454] (-) TimerEvent: {}
[1047.401271] (-) TimerEvent: {}
[1047.502331] (-) TimerEvent: {}
[1047.602753] (-) TimerEvent: {}
[1047.703145] (-) TimerEvent: {}
[1047.803723] (-) TimerEvent: {}
[1047.905222] (-) TimerEvent: {}
[1048.006781] (-) TimerEvent: {}
[1048.108232] (-) TimerEvent: {}
[1048.209446] (-) TimerEvent: {}
[1048.310823] (-) TimerEvent: {}
[1048.411213] (-) TimerEvent: {}
[1048.511593] (-) TimerEvent: {}
[1048.612202] (-) TimerEvent: {}
[1048.713181] (-) TimerEvent: {}
[1048.814153] (-) TimerEvent: {}
[1048.914549] (-) TimerEvent: {}
[1049.014825] (-) TimerEvent: {}
[1049.115169] (-) TimerEvent: {}
[1049.215793] (-) TimerEvent: {}
[1049.316085] (-) TimerEvent: {}
[1049.416558] (-) TimerEvent: {}
[1049.517161] (-) TimerEvent: {}
[1049.618360] (-) TimerEvent: {}
[1049.719468] (-) TimerEvent: {}
[1049.820462] (-) TimerEvent: {}
[1049.922440] (-) TimerEvent: {}
[1050.023040] (-) TimerEvent: {}
[1050.123548] (-) TimerEvent: {}
[1050.223940] (-) TimerEvent: {}
[1050.324284] (-) TimerEvent: {}
[1050.424959] (-) TimerEvent: {}
[1050.525543] (-) TimerEvent: {}
[1050.626212] (-) TimerEvent: {}
[1050.726672] (-) TimerEvent: {}
[1050.827586] (-) TimerEvent: {}
[1050.928323] (-) TimerEvent: {}
[1051.028639] (-) TimerEvent: {}
[1051.129406] (-) TimerEvent: {}
[1051.230206] (-) TimerEvent: {}
[1051.330883] (-) TimerEvent: {}
[1051.431955] (-) TimerEvent: {}
[1051.532286] (-) TimerEvent: {}
[1051.633117] (-) TimerEvent: {}
[1051.734208] (-) TimerEvent: {}
[1051.834983] (-) TimerEvent: {}
[1051.935871] (-) TimerEvent: {}
[1052.036302] (-) TimerEvent: {}
[1052.136754] (-) TimerEvent: {}
[1052.238140] (-) TimerEvent: {}
[1052.338577] (-) TimerEvent: {}
[1052.439209] (-) TimerEvent: {}
[1052.540060] (-) TimerEvent: {}
[1052.640492] (-) TimerEvent: {}
[1052.741585] (-) TimerEvent: {}
[1052.843474] (-) TimerEvent: {}
[1052.944005] (-) TimerEvent: {}
[1053.045263] (-) TimerEvent: {}
[1053.147431] (-) TimerEvent: {}
[1053.249005] (-) TimerEvent: {}
[1053.350162] (-) TimerEvent: {}
[1053.450482] (-) TimerEvent: {}
[1053.550802] (-) TimerEvent: {}
[1053.651326] (-) TimerEvent: {}
[1053.751696] (-) TimerEvent: {}
[1053.852429] (-) TimerEvent: {}
[1053.953150] (-) TimerEvent: {}
[1054.054253] (-) TimerEvent: {}
[1054.154786] (-) TimerEvent: {}
[1054.255358] (-) TimerEvent: {}
[1054.356178] (-) TimerEvent: {}
[1054.456928] (-) TimerEvent: {}
[1054.557325] (-) TimerEvent: {}
[1054.658548] (-) TimerEvent: {}
[1054.759404] (-) TimerEvent: {}
[1054.859994] (-) TimerEvent: {}
[1054.960777] (-) TimerEvent: {}
[1055.061471] (-) TimerEvent: {}
[1055.162164] (-) TimerEvent: {}
[1055.263109] (-) TimerEvent: {}
[1055.363556] (-) TimerEvent: {}
[1055.464112] (-) TimerEvent: {}
[1055.564959] (-) TimerEvent: {}
[1055.665342] (-) TimerEvent: {}
[1055.766434] (-) TimerEvent: {}
[1055.867450] (-) TimerEvent: {}
[1055.968347] (-) TimerEvent: {}
[1056.069013] (-) TimerEvent: {}
[1056.170155] (-) TimerEvent: {}
[1056.270453] (-) TimerEvent: {}
[1056.371074] (-) TimerEvent: {}
[1056.471703] (-) TimerEvent: {}
[1056.572444] (-) TimerEvent: {}
[1056.672970] (-) TimerEvent: {}
[1056.773402] (-) TimerEvent: {}
[1056.874154] (-) TimerEvent: {}
[1056.974865] (-) TimerEvent: {}
[1057.075235] (-) TimerEvent: {}
[1057.175525] (-) TimerEvent: {}
[1057.275838] (-) TimerEvent: {}
[1057.376468] (-) TimerEvent: {}
[1057.478159] (-) TimerEvent: {}
[1057.578482] (-) TimerEvent: {}
[1057.678850] (-) TimerEvent: {}
[1057.779289] (-) TimerEvent: {}
[1057.879786] (-) TimerEvent: {}
[1057.980754] (-) TimerEvent: {}
[1058.081110] (-) TimerEvent: {}
[1058.182189] (-) TimerEvent: {}
[1058.282578] (-) TimerEvent: {}
[1058.382985] (-) TimerEvent: {}
[1058.483392] (-) TimerEvent: {}
[1058.583801] (-) TimerEvent: {}
[1058.684126] (-) TimerEvent: {}
[1058.784844] (-) TimerEvent: {}
[1058.885637] (-) TimerEvent: {}
[1058.987640] (-) TimerEvent: {}
[1059.088038] (-) TimerEvent: {}
[1059.189223] (-) TimerEvent: {}
[1059.291926] (-) TimerEvent: {}
[1059.395095] (-) TimerEvent: {}
[1059.496207] (-) TimerEvent: {}
[1059.596819] (-) TimerEvent: {}
[1059.697283] (-) TimerEvent: {}
[1059.798603] (-) TimerEvent: {}
[1059.899471] (-) TimerEvent: {}
[1060.000129] (-) TimerEvent: {}
[1060.100824] (-) TimerEvent: {}
[1060.201319] (-) TimerEvent: {}
[1060.302243] (-) TimerEvent: {}
[1060.402931] (-) TimerEvent: {}
[1060.503397] (-) TimerEvent: {}
[1060.604068] (-) TimerEvent: {}
[1060.704447] (-) TimerEvent: {}
[1060.804832] (-) TimerEvent: {}
[1060.905362] (-) TimerEvent: {}
[1061.006177] (-) TimerEvent: {}
[1061.106544] (-) TimerEvent: {}
[1061.207018] (-) TimerEvent: {}
[1061.307381] (-) TimerEvent: {}
[1061.408026] (-) TimerEvent: {}
[1061.508386] (-) TimerEvent: {}
[1061.608714] (-) TimerEvent: {}
[1061.709410] (-) TimerEvent: {}
[1061.810185] (-) TimerEvent: {}
[1061.911117] (-) TimerEvent: {}
[1062.011585] (-) TimerEvent: {}
[1062.112179] (-) TimerEvent: {}
[1062.212566] (-) TimerEvent: {}
[1062.313158] (-) TimerEvent: {}
[1062.414268] (-) TimerEvent: {}
[1062.515281] (-) TimerEvent: {}
[1062.616101] (-) TimerEvent: {}
[1062.716760] (-) TimerEvent: {}
[1062.818173] (-) TimerEvent: {}
[1062.918554] (-) TimerEvent: {}
[1063.019082] (-) TimerEvent: {}
[1063.119669] (-) TimerEvent: {}
[1063.220082] (-) TimerEvent: {}
[1063.320395] (-) TimerEvent: {}
[1063.421088] (-) TimerEvent: {}
[1063.521384] (-) TimerEvent: {}
[1063.622147] (-) TimerEvent: {}
[1063.722472] (-) TimerEvent: {}
[1063.823118] (-) TimerEvent: {}
[1063.923495] (-) TimerEvent: {}
[1064.024126] (-) TimerEvent: {}
[1064.124413] (-) TimerEvent: {}
[1064.225011] (-) TimerEvent: {}
[1064.326195] (-) TimerEvent: {}
[1064.427199] (-) TimerEvent: {}
[1064.527977] (-) TimerEvent: {}
[1064.628427] (-) TimerEvent: {}
[1064.728865] (-) TimerEvent: {}
[1064.829481] (-) TimerEvent: {}
[1064.930555] (-) TimerEvent: {}
[1065.031194] (-) TimerEvent: {}
[1065.131827] (-) TimerEvent: {}
[1065.232731] (-) TimerEvent: {}
[1065.334168] (-) TimerEvent: {}
[1065.434522] (-) TimerEvent: {}
[1065.534863] (-) TimerEvent: {}
[1065.635398] (-) TimerEvent: {}
[1065.736095] (-) TimerEvent: {}
[1065.836953] (-) TimerEvent: {}
[1065.938153] (-) TimerEvent: {}
[1066.038767] (-) TimerEvent: {}
[1066.139985] (-) TimerEvent: {}
[1066.240389] (-) TimerEvent: {}
[1066.341105] (-) TimerEvent: {}
[1066.442795] (-) TimerEvent: {}
[1066.543434] (-) TimerEvent: {}
[1066.643733] (-) TimerEvent: {}
[1066.744035] (-) TimerEvent: {}
[1066.844726] (-) TimerEvent: {}
[1066.945127] (-) TimerEvent: {}
[1067.046167] (-) TimerEvent: {}
[1067.146533] (-) TimerEvent: {}
[1067.246869] (-) TimerEvent: {}
[1067.347504] (-) TimerEvent: {}
[1067.447804] (-) TimerEvent: {}
[1067.548443] (-) TimerEvent: {}
[1067.649478] (-) TimerEvent: {}
[1067.751078] (-) TimerEvent: {}
[1067.852339] (-) TimerEvent: {}
[1067.953202] (-) TimerEvent: {}
[1068.054982] (-) TimerEvent: {}
[1068.155401] (-) TimerEvent: {}
[1068.256149] (-) TimerEvent: {}
[1068.357334] (-) TimerEvent: {}
[1068.458365] (-) TimerEvent: {}
[1068.559529] (-) TimerEvent: {}
[1068.660112] (-) TimerEvent: {}
[1068.761123] (-) TimerEvent: {}
[1068.862435] (-) TimerEvent: {}
[1068.963172] (-) TimerEvent: {}
[1069.063535] (-) TimerEvent: {}
[1069.163857] (-) TimerEvent: {}
[1069.264166] (-) TimerEvent: {}
[1069.364667] (-) TimerEvent: {}
[1069.465180] (-) TimerEvent: {}
[1069.566317] (-) TimerEvent: {}
[1069.666721] (-) TimerEvent: {}
[1069.767400] (-) TimerEvent: {}
[1069.868462] (-) TimerEvent: {}
[1069.971294] (-) TimerEvent: {}
[1070.072007] (-) TimerEvent: {}
[1070.172383] (-) TimerEvent: {}
[1070.272797] (-) TimerEvent: {}
[1070.373217] (-) TimerEvent: {}
[1070.474170] (-) TimerEvent: {}
[1070.574640] (-) TimerEvent: {}
[1070.675217] (-) TimerEvent: {}
[1070.775601] (-) TimerEvent: {}
[1070.875922] (-) TimerEvent: {}
[1070.976513] (-) TimerEvent: {}
[1071.078518] (-) TimerEvent: {}
[1071.179142] (-) TimerEvent: {}
[1071.279728] (-) TimerEvent: {}
[1071.380056] (-) TimerEvent: {}
[1071.480859] (-) TimerEvent: {}
[1071.582239] (-) TimerEvent: {}
[1071.682925] (-) TimerEvent: {}
[1071.783913] (-) TimerEvent: {}
[1071.885313] (-) TimerEvent: {}
[1071.986169] (-) TimerEvent: {}
[1072.086544] (-) TimerEvent: {}
[1072.186990] (-) TimerEvent: {}
[1072.287445] (-) TimerEvent: {}
[1072.387792] (-) TimerEvent: {}
[1072.488081] (-) TimerEvent: {}
[1072.588442] (-) TimerEvent: {}
[1072.688807] (-) TimerEvent: {}
[1072.789495] (-) TimerEvent: {}
[1072.890399] (-) TimerEvent: {}
[1072.991115] (-) TimerEvent: {}
[1073.091436] (-) TimerEvent: {}
[1073.191717] (-) TimerEvent: {}
[1073.292009] (-) TimerEvent: {}
[1073.392684] (-) TimerEvent: {}
[1073.493339] (-) TimerEvent: {}
[1073.594649] (-) TimerEvent: {}
[1073.695034] (-) TimerEvent: {}
[1073.795385] (-) TimerEvent: {}
[1073.895943] (-) TimerEvent: {}
[1073.996758] (-) TimerEvent: {}
[1074.097141] (-) TimerEvent: {}
[1074.198159] (-) TimerEvent: {}
[1074.298947] (-) TimerEvent: {}
[1074.399839] (-) TimerEvent: {}
[1074.500677] (-) TimerEvent: {}
[1074.601491] (-) TimerEvent: {}
[1074.702180] (-) TimerEvent: {}
[1074.802877] (-) TimerEvent: {}
[1074.839237] (ros1_bridge) StdoutLine: {'line': b'[100%] Built target dynamic_bridge\n'}
[1074.907723] (-) TimerEvent: {}
[1074.915177] (ros1_bridge) CommandEnded: {'returncode': 0}
[1075.007834] (-) TimerEvent: {}
[1075.108243] (-) TimerEvent: {}
[1075.208662] (-) TimerEvent: {}
[1075.308927] (-) TimerEvent: {}
[1075.409268] (-) TimerEvent: {}
[1075.510119] (-) TimerEvent: {}
[1075.610484] (-) TimerEvent: {}
[1075.710773] (-) TimerEvent: {}
[1075.811051] (-) TimerEvent: {}
[1075.911297] (-) TimerEvent: {}
[1076.011748] (-) TimerEvent: {}
[1076.111999] (-) TimerEvent: {}
[1076.187760] (ros1_bridge) JobProgress: {'identifier': 'ros1_bridge', 'progress': 'install'}
[1076.204530] (ros1_bridge) Command: {'cmd': ['/usr/bin/cmake', '--install', '/home/<USER>/bridge_ws/build/ros1_bridge'], 'cwd': '/home/<USER>/bridge_ws/build/ros1_bridge', 'env': OrderedDict([('GJS_DEBUG_TOPICS', 'JS ERROR;JS LOG'), ('LESSOPEN', '| /usr/bin/lesspipe %s'), ('USER', 'mehdi'), ('SSH_AGENT_PID', '1593'), ('ROS_PACKAGE_PATH', '/opt/ros/noetic/share'), ('XDG_SESSION_TYPE', 'x11'), ('ROS_ETC_DIR', '/opt/ros/noetic/etc/ros'), ('SHLVL', '1'), ('LD_LIBRARY_PATH', '/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/x86_64-linux-gnu:/opt/ros/foxy/lib:/opt/ros/noetic/lib'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/bridge_ws/src'), ('DESKTOP_SESSION', 'ubuntu'), ('ROS_PYTHON_VERSION', '3'), ('GNOME_SHELL_SESSION_MODE', 'ubuntu'), ('GTK_MODULES', 'gail:atk-bridge'), ('MANAGERPID', '1495'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/1000/bus'), ('COLORTERM', 'truecolor'), ('IM_CONFIG_PHASE', '1'), ('ROS_DISTRO', 'foxy'), ('LOGNAME', 'mehdi'), ('JOURNAL_STREAM', '8:55802'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/ros/noetic/lib/pkgconfig'), ('XDG_SESSION_CLASS', 'user'), ('USERNAME', 'mehdi'), ('TERM', 'xterm-256color'), ('GNOME_DESKTOP_SESSION_ID', 'this-is-deprecated'), ('ROS_LOCALHOST_ONLY', '0'), ('WINDOWPATH', '2'), ('PATH', '/opt/ros/foxy/bin:/opt/ros/noetic/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin'), ('SESSION_MANAGER', 'local/ubuntu:@/tmp/.ICE-unix/1628,unix/ubuntu:/tmp/.ICE-unix/1628'), ('INVOCATION_ID', '9c03585a47054d89b97b34bc48f12474'), ('XDG_MENU_PREFIX', 'gnome-'), ('GNOME_TERMINAL_SCREEN', '/org/gnome/Terminal/screen/d8af12fa_0ba9_4dab_bb5a_fb890aa18bad'), ('XDG_RUNTIME_DIR', '/run/user/1000'), ('DISPLAY', ':0'), ('LANG', 'en_US.UTF-8'), ('XDG_CURRENT_DESKTOP', 'ubuntu:GNOME'), ('XMODIFIERS', '@im=ibus'), ('ROSLISP_PACKAGE_DIRECTORIES', ''), ('XDG_SESSION_DESKTOP', 'ubuntu'), ('XAUTHORITY', '/run/user/1000/gdm/Xauthority'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('GNOME_TERMINAL_SERVICE', ':1.73'), ('ROS_ROOT', '/opt/ros/noetic/share/ros'), ('SSH_AUTH_SOCK', '/run/user/1000/keyring/ssh'), ('AMENT_PREFIX_PATH', '/opt/ros/foxy'), ('SHELL', '/bin/bash'), ('QT_ACCESSIBILITY', '1'), ('ROS_MASTER_URI', 'http://localhost:11311'), ('GDMSESSION', 'ubuntu'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('GPG_AGENT_INFO', '/run/user/1000/gnupg/S.gpg-agent:0:1'), ('GJS_DEBUG_OUTPUT', 'stderr'), ('QT_IM_MODULE', 'ibus'), ('PWD', '/home/<USER>/bridge_ws/build/ros1_bridge'), ('XDG_CONFIG_DIRS', '/etc/xdg/xdg-ubuntu:/etc/xdg'), ('XDG_DATA_DIRS', '/usr/share/ubuntu:/usr/local/share/:/usr/share/:/var/lib/snapd/desktop'), ('PYTHONPATH', '/opt/ros/foxy/lib/python3.8/site-packages:/opt/ros/noetic/lib/python3/dist-packages'), ('COLCON', '1'), ('VTE_VERSION', '6003'), ('CMAKE_PREFIX_PATH', '/opt/ros/foxy:/opt/ros/noetic')]), 'shell': False}
[1076.213926] (-) TimerEvent: {}
[1076.223244] (ros1_bridge) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[1076.231349] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.sh\n'}
[1076.233410] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.dsv\n'}
[1076.234399] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.sh\n'}
[1076.235044] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.dsv\n'}
[1076.235666] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge\n'}
[1076.236425] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge/__init__.py\n'}
[1076.297829] (ros1_bridge) StdoutLine: {'line': b"Listing '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge'...\n"}
[1076.298198] (ros1_bridge) StdoutLine: {'line': b"Compiling '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge/__init__.py'...\n"}
[1076.304448] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/package_run_dependencies/ros1_bridge\n'}
[1076.306447] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/parent_prefix_path/ros1_bridge\n'}
[1076.311077] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.sh\n'}
[1076.312348] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.dsv\n'}
[1076.312888] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.sh\n'}
[1076.314111] (-) TimerEvent: {}
[1076.314451] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.dsv\n'}
[1076.314964] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.bash\n'}
[1076.315488] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.sh\n'}
[1076.316238] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.zsh\n'}
[1076.316775] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.dsv\n'}
[1076.317124] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.dsv\n'}
[1076.318246] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/packages/ros1_bridge\n'}
[1076.319397] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake\n'}
[1076.319903] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake\n'}
[1076.320763] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_include_directories-extras.cmake\n'}
[1076.321503] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_libraries-extras.cmake\n'}
[1076.322633] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig.cmake\n'}
[1076.323100] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig-version.cmake\n'}
[1076.323593] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.xml\n'}
[1076.324801] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_1_to_2\n'}
[1076.328822] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_1_to_2" to ""\n'}
[1076.329003] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_2_to_1\n'}
[1076.335461] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_2_to_1" to ""\n'}
[1076.335625] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge\n'}
[1076.343597] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge" to ""\n'}
[1076.344150] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/libros1_bridge.so\n'}
[1076.414430] (-) TimerEvent: {}
[1076.515478] (-) TimerEvent: {}
[1076.615878] (-) TimerEvent: {}
[1076.661198] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/libros1_bridge.so" to ""\n'}
[1076.662497] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/static_bridge\n'}
[1076.664940] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/static_bridge" to ""\n'}
[1076.666412] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/parameter_bridge\n'}
[1076.668071] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/parameter_bridge" to ""\n'}
[1076.668189] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/dynamic_bridge\n'}
[1076.671723] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/dynamic_bridge" to ""\n'}
[1076.673150] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_client_cpp\n'}
[1076.684552] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_client_cpp" to ""\n'}
[1076.684718] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_server_cpp\n'}
[1076.687485] (ros1_bridge) StdoutLine: {'line': b'-- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_server_cpp" to ""\n'}
[1076.688706] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/generate_factories/ros1_bridge_generate_factories\n'}
[1076.690399] (ros1_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake\n'}
[1076.691059] (ros1_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake\n'}
[1076.691665] (ros1_bridge) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake\n'}
[1076.691862] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include\n'}
[1076.692031] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge\n'}
[1076.692215] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory.hpp\n'}
[1076.692712] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_builtin_interfaces.hpp\n'}
[1076.693535] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory_interface.hpp\n'}
[1076.694652] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/builtin_interfaces_factories.hpp\n'}
[1076.695358] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_decl.hpp\n'}
[1076.696150] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/bridge.hpp\n'}
[1076.696514] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource\n'}
[1076.697265] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.cpp.em\n'}
[1076.698454] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_mappings.cpp.em\n'}
[1076.699098] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.hpp.em\n'}
[1076.699822] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_factory.cpp.em\n'}
[1076.700570] (ros1_bridge) StdoutLine: {'line': b'-- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/interface_factories.cpp.em\n'}
[1076.710275] (ros1_bridge) CommandEnded: {'returncode': 0}
[1076.716986] (-) TimerEvent: {}
[1076.817764] (-) TimerEvent: {}
[1076.920203] (-) TimerEvent: {}
[1077.020913] (-) TimerEvent: {}
[1077.121191] (-) TimerEvent: {}
[1077.222451] (-) TimerEvent: {}
[1077.322690] (-) TimerEvent: {}
[1077.422962] (-) TimerEvent: {}
[1077.523224] (-) TimerEvent: {}
[1077.623510] (-) TimerEvent: {}
[1077.723833] (-) TimerEvent: {}
[1077.824109] (-) TimerEvent: {}
[1077.924544] (-) TimerEvent: {}
[1078.025280] (-) TimerEvent: {}
[1078.086578] (ros1_bridge) JobEnded: {'identifier': 'ros1_bridge', 'rc': 0}
[1078.089404] (-) EventReactorShutdown: {}
