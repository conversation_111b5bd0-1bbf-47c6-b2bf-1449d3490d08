[0.911s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'ros1_bridge', '--cmake-force-configure']
[0.912s] DEBUG:colcon:Parsed command line arguments: Namespace(allow_overriding=[], ament_cmake_args=None, base_paths=['.'], build_base='build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=None, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=True, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='parallel', ignore_user_meta=False, install_base='install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fde97a24310>>, merge_install=False, metas=['./colcon.meta'], mixin=None, mixin_files=None, mixin_verb=('build',), packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=['ros1_bridge'], packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=None, packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=2, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fde97a24310>, verb_name='build', verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7fde978a31f0>)
[1.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[1.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[1.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[1.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[1.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[1.144s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[1.144s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/bridge_ws'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extensions ['ignore', 'ignore_ament_install']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extension 'ignore'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extension 'ignore_ament_install'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extensions ['colcon_pkg']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extension 'colcon_pkg'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extensions ['colcon_meta']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extension 'colcon_meta'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extensions ['ros']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros1_bridge) by extension 'ros'
[1.159s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros1_bridge' with type 'ros.ament_cmake' and name 'ros1_bridge'
[1.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[1.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[1.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[1.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[1.159s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[1.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[1.204s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[1.212s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 256 installed packages in /opt/ros/foxy
[1.213s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_args' from command line to 'None'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_target' from command line to 'None'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_clean_cache' from command line to 'False'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_clean_first' from command line to 'False'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'cmake_force_configure' from command line to 'True'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'ament_cmake_args' from command line to 'None'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'catkin_cmake_args' from command line to 'None'
[1.306s] Level 5:colcon.colcon_core.verb:set package 'ros1_bridge' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.306s] DEBUG:colcon.colcon_core.verb:Building package 'ros1_bridge' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/bridge_ws/build/ros1_bridge', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': True, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/bridge_ws/install/ros1_bridge', 'merge_install': False, 'path': '/home/<USER>/bridge_ws/src/ros1_bridge', 'symlink_install': False, 'test_result_base': None}
[1.306s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[1.307s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[1.307s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/bridge_ws/src/ros1_bridge' with build type 'ament_cmake'
[1.307s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/bridge_ws/src/ros1_bridge'
[1.322s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[1.322s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.322s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.323s] DEBUG:colcon.colcon_core.shell:Ignoring prefix path '/opt/ros/noetic'
[1.342s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake /home/<USER>/bridge_ws/src/ros1_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/bridge_ws/install/ros1_bridge
[10.991s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake /home/<USER>/bridge_ws/src/ros1_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/bridge_ws/install/ros1_bridge
[11.000s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/bridge_ws/build/ros1_bridge -- -j2 -l2
[1076.227s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/bridge_ws/build/ros1_bridge -- -j2 -l2
[1077.521s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/bridge_ws/build/ros1_bridge
[1078.013s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ros1_bridge)
[1078.018s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/bridge_ws/build/ros1_bridge
[1078.054s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge' for CMake module files
[1078.056s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge' for CMake config files
[1078.058s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'cmake_prefix_path')
[1078.059s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.ps1'
[1078.077s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.dsv'
[1078.079s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.sh'
[1078.082s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib'
[1078.082s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'ld_library_path_lib')
[1078.083s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.ps1'
[1078.083s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.dsv'
[1078.084s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.sh'
[1078.084s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/bin'
[1078.084s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib/pkgconfig/ros1_bridge.pc'
[1078.085s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages'
[1078.085s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'pythonpath')
[1078.085s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.ps1'
[1078.086s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.dsv'
[1078.086s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.sh'
[1078.086s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/bin'
[1078.087s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.ps1'
[1078.090s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.dsv'
[1078.092s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.sh'
[1078.095s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.bash'
[1078.098s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.zsh'
[1078.101s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/bridge_ws/install/ros1_bridge/share/colcon-core/packages/ros1_bridge)
[1079.369s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ros1_bridge)
[1079.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge' for CMake module files
[1079.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge' for CMake config files
[1079.370s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'cmake_prefix_path')
[1079.371s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.ps1'
[1079.372s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.dsv'
[1079.373s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/cmake_prefix_path.sh'
[1079.374s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib'
[1079.374s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'ld_library_path_lib')
[1079.375s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.ps1'
[1079.378s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.dsv'
[1079.379s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/ld_library_path_lib.sh'
[1079.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/bin'
[1079.379s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib/pkgconfig/ros1_bridge.pc'
[1079.380s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages'
[1079.380s] Level 1:colcon.colcon_core.shell:create_environment_hook('ros1_bridge', 'pythonpath')
[1079.381s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.ps1'
[1079.382s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.dsv'
[1079.384s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/hook/pythonpath.sh'
[1079.386s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/bridge_ws/install/ros1_bridge/bin'
[1079.388s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.ps1'
[1079.391s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.dsv'
[1079.391s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.sh'
[1079.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.bash'
[1079.392s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.zsh'
[1079.393s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/bridge_ws/install/ros1_bridge/share/colcon-core/packages/ros1_bridge)
[1079.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[1079.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[1079.396s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[1079.397s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[1079.455s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1079.455s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[1079.455s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[1079.857s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[1079.859s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/bridge_ws/install/local_setup.ps1'
[1079.862s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/bridge_ws/install/_local_setup_util_ps1.py'
[1079.864s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/bridge_ws/install/setup.ps1'
[1079.866s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/bridge_ws/install/local_setup.sh'
[1079.867s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/bridge_ws/install/_local_setup_util_sh.py'
[1079.868s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/bridge_ws/install/setup.sh'
[1079.870s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/bridge_ws/install/local_setup.bash'
[1079.871s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/bridge_ws/install/setup.bash'
[1079.874s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/bridge_ws/install/local_setup.zsh'
[1079.875s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/bridge_ws/install/setup.zsh'
