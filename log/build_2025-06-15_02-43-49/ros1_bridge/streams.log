[0.034s] Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake /home/<USER>/bridge_ws/src/ros1_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/bridge_ws/install/ros1_bridge
[0.358s] -- The C compiler identification is GNU 9.4.0
[0.543s] -- The CXX compiler identification is GNU 9.4.0
[0.575s] -- Check for working C compiler: /usr/bin/cc
[0.655s] -- Check for working C compiler: /usr/bin/cc -- works
[0.660s] -- Detecting C compiler ABI info
[0.770s] -- Detecting C compiler ABI info - done
[0.778s] -- Detecting C compile features
[0.779s] -- Detecting C compile features - done
[0.787s] -- Check for working CXX compiler: /usr/bin/c++
[0.898s] -- Check for working CXX compiler: /usr/bin/c++ -- works
[0.899s] -- Detecting CXX compiler ABI info
[1.048s] -- Detecting CXX compiler ABI info - done
[1.064s] -- Detecting CXX compile features
[1.064s] -- Detecting CXX compile features - done
[1.071s] -- Found rmw: 1.0.4 (/opt/ros/foxy/share/rmw/cmake)
[1.112s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3") 
[1.112s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[1.506s] -- Found rclcpp: 2.4.3 (/opt/ros/foxy/share/rclcpp/cmake)
[1.612s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[1.630s] -- Found rosidl_adapter: 1.3.1 (/opt/ros/foxy/share/rosidl_adapter/cmake)
[1.806s] -- Found OpenSSL: /usr/lib/x86_64-linux-gnu/libcrypto.so (found version "1.1.1f")  
[1.837s] -- Found FastRTPS: /opt/ros/foxy/include  
[1.953s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[2.060s] -- Found rmw_implementation_cmake: 1.0.4 (/opt/ros/foxy/share/rmw_implementation_cmake/cmake)
[2.064s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[2.068s] -- Looking for pthread.h
[2.197s] -- Looking for pthread.h - found
[2.198s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[2.290s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
[2.290s] -- Looking for pthread_create in pthreads
[2.396s] -- Looking for pthread_create in pthreads - not found
[2.398s] -- Looking for pthread_create in pthread
[2.547s] -- Looking for pthread_create in pthread - found
[2.548s] -- Found Threads: TRUE  
[2.781s] -- Found PkgConfig: /usr/bin/pkg-config (found version "0.29.1") 
[2.788s] -- Checking for module 'roscpp'
[2.820s] --   Found roscpp, version 1.17.4
[2.914s] -- Checking for module 'std_msgs'
[2.959s] --   Found std_msgs, version 0.5.14
[3.598s] -- Checking for module 'actionlib'
[3.623s] --   Found actionlib, version 1.14.3
[3.727s] -- Checking for module 'actionlib_msgs'
[3.753s] --   Found actionlib_msgs, version 1.13.2
[3.834s] -- Checking for module 'actionlib_tutorials'
[3.854s] --   Found actionlib_tutorials, version 0.2.0
[3.922s] -- Checking for module 'bond'
[3.945s] --   Found bond, version 1.8.7
[4.010s] -- Checking for module 'control_msgs'
[4.029s] --   Found control_msgs, version 1.5.2
[4.090s] -- Checking for module 'controller_manager_msgs'
[4.113s] --   Found controller_manager_msgs, version 0.20.0
[4.212s] -- Checking for module 'diagnostic_msgs'
[4.233s] --   Found diagnostic_msgs, version 1.13.2
[4.294s] -- Checking for module 'dynamic_reconfigure'
[4.336s] --   Found dynamic_reconfigure, version 1.7.6
[4.399s] -- Checking for module 'gazebo_msgs'
[4.418s] --   Found gazebo_msgs, version 2.9.3
[4.493s] -- Checking for module 'geometry_msgs'
[4.525s] --   Found geometry_msgs, version 1.13.2
[4.599s] -- Checking for module 'map_msgs'
[4.621s] --   Found map_msgs, version 1.14.2
[4.705s] -- Checking for module 'nav_msgs'
[4.731s] --   Found nav_msgs, version 1.13.2
[4.840s] -- Checking for module 'pcl_msgs'
[4.868s] --   Found pcl_msgs, version 0.3.0
[4.934s] -- Checking for module 'rosgraph_msgs'
[4.960s] --   Found rosgraph_msgs, version 1.11.4
[5.017s] -- Checking for module 'rospy_tutorials'
[5.035s] --   Found rospy_tutorials, version 0.10.3
[5.098s] -- Checking for module 'sensor_msgs'
[5.119s] --   Found sensor_msgs, version 1.13.2
[5.190s] -- Checking for module 'shape_msgs'
[5.210s] --   Found shape_msgs, version 1.13.2
[5.286s] -- Checking for module 'smach_msgs'
[5.312s] --   Found smach_msgs, version 2.5.3
[5.397s] -- Checking for module 'stereo_msgs'
[5.428s] --   Found stereo_msgs, version 1.13.2
[5.503s] -- Checking for module 'tf'
[5.538s] --   Found tf, version 1.13.4
[5.636s] -- Checking for module 'tf2_msgs'
[5.660s] --   Found tf2_msgs, version 0.7.10
[5.744s] -- Checking for module 'theora_image_transport'
[5.768s] --   Found theora_image_transport, version 1.15.0
[5.828s] -- Checking for module 'trajectory_msgs'
[5.848s] --   Found trajectory_msgs, version 1.13.2
[5.929s] -- Checking for module 'turtle_actionlib'
[5.956s] --   Found turtle_actionlib, version 0.2.0
[6.009s] -- Checking for module 'turtlesim'
[6.034s] --   Found turtlesim, version 0.10.3
[6.114s] -- Checking for module 'visualization_msgs'
[6.139s] --   Found visualization_msgs, version 1.13.2
[6.219s] -- Checking for module 'control_toolbox'
[6.243s] --   Found control_toolbox, version 1.19.0
[6.317s] -- Checking for module 'laser_assembler'
[6.353s] --   Found laser_assembler, version 1.7.8
[6.455s] -- Checking for module 'polled_camera'
[6.480s] --   Found polled_camera, version 1.12.1
[6.598s] -- Checking for module 'roscpp_tutorials'
[6.622s] --   Found roscpp_tutorials, version 0.10.3
[6.705s] -- Checking for module 'rviz'
[6.736s] --   Found rviz, version 1.14.26
[6.843s] -- Checking for module 'std_srvs'
[6.874s] --   Found std_srvs, version 1.11.4
[6.976s] -- Checking for module 'topic_tools'
[6.997s] --   Found topic_tools, version 1.17.4
[7.070s] -- Found ament_lint_auto: 0.9.8 (/opt/ros/foxy/share/ament_lint_auto/cmake)
[7.075s] -- Found diagnostic_msgs: 2.0.5 (/opt/ros/foxy/share/diagnostic_msgs/cmake)
[7.138s] -- Checking for module 'roslaunch'
[7.175s] --   Found roslaunch, version 1.17.4
[7.388s] -- Found python_cmake_module: 0.8.1 (/opt/ros/foxy/share/python_cmake_module/cmake)
[7.407s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3.5") 
[7.433s] -- Found PythonLibs: /usr/lib/x86_64-linux-gnu/libpython3.8.so (found suitable version "3.8.10", minimum required is "3.5") 
[7.433s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[7.433s] -- Using PYTHON_INCLUDE_DIRS: /usr/include/python3.8
[7.433s] -- Using PYTHON_LIBRARIES: /usr/lib/x86_64-linux-gnu/libpython3.8.so
[7.472s] -- Found PythonExtra: .so  
[7.523s] -- Added test 'copyright' to check source files copyright and LICENSE
[7.528s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[7.528s] -- Configured cppcheck include dirs: 
[7.528s] -- Configured cppcheck exclude dirs and/or files: 
[7.532s] -- Added test 'cpplint' to check C / C++ code against the Google style
[7.533s] -- Configured cpplint exclude dirs and/or files: 
[7.535s] -- Added test 'flake8' to check Python code syntax and style conventions
[7.537s] -- Added test 'lint_cmake' to check CMake code style
[7.539s] -- Added test 'pep257' to check Python code against some of the style conventions in PEP 257
[7.543s] -- Added test 'uncrustify' to check C / C++ code style
[7.544s] -- Configured uncrustify additional arguments: 
[7.545s] -- Added test 'xmllint' to check XML markup files
[7.637s] -- Found action_msgs: 1.0.0 (/opt/ros/foxy/share/action_msgs/cmake)
[7.685s] -- Found action_tutorials_interfaces: 0.9.4 (/opt/ros/foxy/share/action_tutorials_interfaces/cmake)
[7.752s] -- Found actionlib_msgs: 2.0.5 (/opt/ros/foxy/share/actionlib_msgs/cmake)
[7.752s] -- Found builtin_interfaces: 1.0.0 (/opt/ros/foxy/share/builtin_interfaces/cmake)
[7.797s] -- Found composition_interfaces: 1.0.0 (/opt/ros/foxy/share/composition_interfaces/cmake)
[7.797s] -- Found diagnostic_msgs: 2.0.5 (/opt/ros/foxy/share/diagnostic_msgs/cmake)
[7.855s] -- Found example_interfaces: 0.9.1 (/opt/ros/foxy/share/example_interfaces/cmake)
[7.901s] -- Found geometry_msgs: 2.0.5 (/opt/ros/foxy/share/geometry_msgs/cmake)
[7.902s] -- Found libstatistics_collector: 1.0.2 (/opt/ros/foxy/share/libstatistics_collector/cmake)
[7.932s] -- Found lifecycle_msgs: 1.0.0 (/opt/ros/foxy/share/lifecycle_msgs/cmake)
[7.941s] -- Found logging_demo: 0.9.4 (/opt/ros/foxy/share/logging_demo/cmake)
[8.257s] -- Found map_msgs: 2.0.2 (/opt/ros/foxy/share/map_msgs/cmake)
[8.257s] -- Found nav_msgs: 2.0.5 (/opt/ros/foxy/share/nav_msgs/cmake)
[8.355s] -- Found pcl_msgs: 1.0.0 (/opt/ros/foxy/share/pcl_msgs/cmake)
[8.381s] -- Found pendulum_msgs: 0.9.4 (/opt/ros/foxy/share/pendulum_msgs/cmake)
[8.381s] -- Found rcl_interfaces: 1.0.0 (/opt/ros/foxy/share/rcl_interfaces/cmake)
[8.415s] -- Found rmw_dds_common: 1.0.3 (/opt/ros/foxy/share/rmw_dds_common/cmake)
[8.415s] -- Found rosgraph_msgs: 1.0.0 (/opt/ros/foxy/share/rosgraph_msgs/cmake)
[8.416s] -- Found sensor_msgs: 2.0.5 (/opt/ros/foxy/share/sensor_msgs/cmake)
[8.497s] -- Found shape_msgs: 2.0.5 (/opt/ros/foxy/share/shape_msgs/cmake)
[8.498s] -- Found statistics_msgs: 1.0.0 (/opt/ros/foxy/share/statistics_msgs/cmake)
[8.499s] -- Found std_msgs: 2.0.5 (/opt/ros/foxy/share/std_msgs/cmake)
[8.531s] -- Found std_srvs: 2.0.5 (/opt/ros/foxy/share/std_srvs/cmake)
[8.668s] -- Found stereo_msgs: 2.0.5 (/opt/ros/foxy/share/stereo_msgs/cmake)
[8.834s] -- Found tf2_msgs: 0.13.14 (/opt/ros/foxy/share/tf2_msgs/cmake)
[8.935s] -- Found trajectory_msgs: 2.0.5 (/opt/ros/foxy/share/trajectory_msgs/cmake)
[8.979s] -- Found turtlesim: 1.2.6 (/opt/ros/foxy/share/turtlesim/cmake)
[8.979s] -- Found unique_identifier_msgs: 2.1.3 (/opt/ros/foxy/share/unique_identifier_msgs/cmake)
[9.060s] -- Found visualization_msgs: 2.0.5 (/opt/ros/foxy/share/visualization_msgs/cmake)
[9.326s] -- Configuring done
[9.657s] -- Generating done
[9.661s] -- Build files have been written to: /home/<USER>/bridge_ws/build/ros1_bridge
[9.683s] Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake /home/<USER>/bridge_ws/src/ros1_bridge -DCMAKE_INSTALL_PREFIX=/home/<USER>/bridge_ws/install/ros1_bridge
[9.691s] Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/bridge_ws/build/ros1_bridge -- -j2 -l2
[9.752s] [35m[1mScanning dependencies of target test_ros1_client[0m
[9.753s] [35m[1mScanning dependencies of target test_ros1_server[0m
[9.794s] [  0%] [32mBuilding CXX object CMakeFiles/test_ros1_client.dir/test/test_ros1_client.cpp.o[0m
[9.805s] [  1%] [32mBuilding CXX object CMakeFiles/test_ros1_server.dir/test/test_ros1_server.cpp.o[0m
[12.713s] [  1%] [32m[1mLinking CXX executable test_ros1_client[0m
[12.913s] [  1%] Built target test_ros1_client
[12.939s] [35m[1mScanning dependencies of target simple_bridge_1_to_2[0m
[12.975s] [  1%] [32m[1mLinking CXX executable test_ros1_server[0m
[13.168s] [  1%] Built target test_ros1_server
[13.183s] [35m[1mScanning dependencies of target simple_bridge_2_to_1[0m
[13.280s] [  2%] [32mBuilding CXX object CMakeFiles/simple_bridge_2_to_1.dir/src/simple_bridge_2_to_1.cpp.o[0m
[13.280s] [  2%] [32mBuilding CXX object CMakeFiles/simple_bridge_1_to_2.dir/src/simple_bridge_1_to_2.cpp.o[0m
[18.560s] [  2%] [32m[1mLinking CXX executable simple_bridge_1_to_2[0m
[18.943s] [  2%] Built target simple_bridge_1_to_2
[18.966s] [  2%] [34m[1mGenerating factories for interface types[0m
[20.466s] [  2%] [32m[1mLinking CXX executable simple_bridge_2_to_1[0m
[20.826s] [  2%] Built target simple_bridge_2_to_1
[20.840s] [35m[1mScanning dependencies of target test_ros2_server_cpp[0m
[20.909s] [  2%] [32mBuilding CXX object CMakeFiles/test_ros2_server_cpp.dir/test/test_ros2_server.cpp.o[0m
[23.188s] [  3%] [32m[1mLinking CXX executable test_ros2_server_cpp[0m
[23.385s] [  3%] Built target test_ros2_server_cpp
[23.399s] [35m[1mScanning dependencies of target simple_bridge[0m
[23.465s] [  3%] [32mBuilding CXX object CMakeFiles/simple_bridge.dir/src/simple_bridge.cpp.o[0m
[31.011s] [  4%] [32m[1mLinking CXX executable simple_bridge[0m
[31.344s] [  4%] Built target simple_bridge
[31.354s] [35m[1mScanning dependencies of target test_ros2_client_cpp[0m
[31.399s] [  4%] [32mBuilding CXX object CMakeFiles/test_ros2_client_cpp.dir/test/test_ros2_client.cpp.o[0m
[33.938s] [35m[1mScanning dependencies of target ros1_bridge[0m
[34.209s] [  5%] [32m[1mLinking CXX executable test_ros2_client_cpp[0m
[34.394s] [  5%] Built target test_ros2_client_cpp
[36.391s] [  6%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/builtin_interfaces_factories.cpp.o[0m
[36.391s] [  6%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/convert_builtin_interfaces.cpp.o[0m
[37.131s] [  6%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/src/bridge.cpp.o[0m
[40.178s] [  7%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/get_factory.cpp.o[0m
[46.638s] [  7%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/get_mappings.cpp.o[0m
[47.218s] [  7%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs_factories.cpp.o[0m
[47.491s] [  8%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalInfo__factories.cpp.o[0m
[51.955s] [  8%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalStatus__factories.cpp.o[0m
[52.236s] [  8%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__msg__GoalStatusArray__factories.cpp.o[0m
[56.379s] [  9%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_msgs__srv__CancelGoal__factories.cpp.o[0m
[56.868s] [  9%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/action_tutorials_interfaces_factories.cpp.o[0m
[60.566s] [ 10%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs_factories.cpp.o[0m
[61.244s] [ 10%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalID__factories.cpp.o[0m
[65.392s] [ 10%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalStatus__factories.cpp.o[0m
[71.923s] [ 11%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/actionlib_msgs__msg__GoalStatusArray__factories.cpp.o[0m
[75.488s] [ 11%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces_factories.cpp.o[0m
[80.946s] [ 11%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__LoadNode__factories.cpp.o[0m
[82.967s] [ 12%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__ListNodes__factories.cpp.o[0m
[85.591s] [ 12%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/composition_interfaces__srv__UnloadNode__factories.cpp.o[0m
[87.265s] [ 12%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs_factories.cpp.o[0m
[89.323s] [ 13%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__DiagnosticArray__factories.cpp.o[0m
[91.009s] [ 13%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__DiagnosticStatus__factories.cpp.o[0m
[97.576s] [ 13%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__msg__KeyValue__factories.cpp.o[0m
[99.490s] [ 14%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__srv__AddDiagnostics__factories.cpp.o[0m
[104.650s] [ 14%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/diagnostic_msgs__srv__SelfTest__factories.cpp.o[0m
[106.095s] [ 14%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces_factories.cpp.o[0m
[109.819s] [ 15%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Bool__factories.cpp.o[0m
[110.683s] [ 15%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Byte__factories.cpp.o[0m
[113.573s] [ 16%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__ByteMultiArray__factories.cpp.o[0m
[115.205s] [ 16%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Char__factories.cpp.o[0m
[119.231s] [ 16%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Empty__factories.cpp.o[0m
[119.964s] [ 17%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float32__factories.cpp.o[0m
[122.954s] [ 17%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float32MultiArray__factories.cpp.o[0m
[123.632s] [ 17%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float64__factories.cpp.o[0m
[126.928s] [ 18%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Float64MultiArray__factories.cpp.o[0m
[127.408s] [ 18%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int16__factories.cpp.o[0m
[131.507s] [ 18%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int16MultiArray__factories.cpp.o[0m
[131.865s] [ 19%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int32__factories.cpp.o[0m
[136.177s] [ 19%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int32MultiArray__factories.cpp.o[0m
[136.657s] [ 19%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int64__factories.cpp.o[0m
[140.700s] [ 20%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int64MultiArray__factories.cpp.o[0m
[141.144s] [ 20%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int8__factories.cpp.o[0m
[144.638s] [ 20%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__Int8MultiArray__factories.cpp.o[0m
[144.907s] [ 21%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__MultiArrayDimension__factories.cpp.o[0m
[148.495s] [ 21%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__MultiArrayLayout__factories.cpp.o[0m
[148.585s] [ 22%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__String__factories.cpp.o[0m
[152.265s] [ 22%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt16__factories.cpp.o[0m
[152.267s] [ 22%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt16MultiArray__factories.cpp.o[0m
[155.984s] [ 23%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt32__factories.cpp.o[0m
[156.131s] [ 23%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt32MultiArray__factories.cpp.o[0m
[159.704s] [ 23%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt64__factories.cpp.o[0m
[159.935s] [ 24%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt64MultiArray__factories.cpp.o[0m
[163.454s] [ 24%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt8__factories.cpp.o[0m
[163.704s] [ 24%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__UInt8MultiArray__factories.cpp.o[0m
[166.968s] [ 25%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__msg__WString__factories.cpp.o[0m
[167.351s] [ 25%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__AddTwoInts__factories.cpp.o[0m
[172.840s] [ 25%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__SetBool__factories.cpp.o[0m
[174.861s] [ 26%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/example_interfaces__srv__Trigger__factories.cpp.o[0m
[176.072s] [ 26%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs_factories.cpp.o[0m
[178.490s] [ 26%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Accel__factories.cpp.o[0m
[179.750s] [ 27%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelStamped__factories.cpp.o[0m
[186.535s] [ 27%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelWithCovariance__factories.cpp.o[0m
[187.893s] [ 28%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__AccelWithCovarianceStamped__factories.cpp.o[0m
[194.627s] [ 28%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Inertia__factories.cpp.o[0m
[196.223s] [ 28%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__InertiaStamped__factories.cpp.o[0m
[203.021s] [ 29%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Point__factories.cpp.o[0m
[204.615s] [ 29%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Point32__factories.cpp.o[0m
[212.894s] [ 29%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PointStamped__factories.cpp.o[0m
[219.685s] [ 30%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Polygon__factories.cpp.o[0m
[225.707s] [ 30%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PolygonStamped__factories.cpp.o[0m
[226.774s] [ 30%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Pose__factories.cpp.o[0m
[233.825s] [ 31%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Pose2D__factories.cpp.o[0m
[234.695s] [ 31%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseArray__factories.cpp.o[0m
[242.045s] [ 31%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseStamped__factories.cpp.o[0m
[242.830s] [ 32%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseWithCovariance__factories.cpp.o[0m
[250.000s] [ 32%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__PoseWithCovarianceStamped__factories.cpp.o[0m
[250.824s] [ 32%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Quaternion__factories.cpp.o[0m
[258.414s] [ 33%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__QuaternionStamped__factories.cpp.o[0m
[259.188s] [ 33%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Transform__factories.cpp.o[0m
[266.861s] [ 34%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TransformStamped__factories.cpp.o[0m
[267.216s] [ 34%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Twist__factories.cpp.o[0m
[275.011s] [ 34%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistStamped__factories.cpp.o[0m
[275.403s] [ 35%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistWithCovariance__factories.cpp.o[0m
[283.439s] [ 35%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__TwistWithCovarianceStamped__factories.cpp.o[0m
[283.449s] [ 35%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Vector3__factories.cpp.o[0m
[291.557s] [ 36%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Vector3Stamped__factories.cpp.o[0m
[291.569s] [ 36%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__Wrench__factories.cpp.o[0m
[299.654s] [ 36%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/geometry_msgs__msg__WrenchStamped__factories.cpp.o[0m
[299.813s] [ 37%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/libstatistics_collector_factories.cpp.o[0m
[303.451s] [ 37%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/libstatistics_collector__msg__DummyMessage__factories.cpp.o[0m
[307.106s] [ 37%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs_factories.cpp.o[0m
[308.049s] [ 38%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__State__factories.cpp.o[0m
[310.767s] [ 38%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__Transition__factories.cpp.o[0m
[311.778s] [ 38%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__TransitionDescription__factories.cpp.o[0m
[314.293s] [ 39%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__msg__TransitionEvent__factories.cpp.o[0m
[315.444s] [ 39%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__ChangeState__factories.cpp.o[0m
[318.078s] [ 40%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetAvailableStates__factories.cpp.o[0m
[319.181s] [ 40%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetAvailableTransitions__factories.cpp.o[0m
[321.955s] [ 40%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/lifecycle_msgs__srv__GetState__factories.cpp.o[0m
[322.937s] [ 41%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/logging_demo_factories.cpp.o[0m
[326.500s] [ 41%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/logging_demo__srv__ConfigLogger__factories.cpp.o[0m
[329.447s] [ 41%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs_factories.cpp.o[0m
[331.465s] [ 42%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__OccupancyGridUpdate__factories.cpp.o[0m
[332.866s] [ 42%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__PointCloud2Update__factories.cpp.o[0m
[340.403s] [ 42%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__ProjectedMapInfo__factories.cpp.o[0m
[341.038s] [ 43%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__msg__ProjectedMap__factories.cpp.o[0m
[348.318s] [ 43%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetMapROI__factories.cpp.o[0m
[349.088s] [ 43%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetPointMapROI__factories.cpp.o[0m
[353.752s] [ 44%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__GetPointMap__factories.cpp.o[0m
[354.796s] [ 44%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__ProjectedMapsInfo__factories.cpp.o[0m
[359.415s] [ 44%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__SaveMap__factories.cpp.o[0m
[360.436s] [ 45%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/map_msgs__srv__SetMapProjections__factories.cpp.o[0m
[364.704s] [ 45%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs_factories.cpp.o[0m
[366.299s] [ 46%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__GridCells__factories.cpp.o[0m
[368.637s] [ 46%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__MapMetaData__factories.cpp.o[0m
[374.809s] [ 46%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__OccupancyGrid__factories.cpp.o[0m
[376.899s] [ 47%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__Odometry__factories.cpp.o[0m
[383.164s] [ 47%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__msg__Path__factories.cpp.o[0m
[390.648s] [ 47%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__GetMap__factories.cpp.o[0m
[399.131s] [ 48%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__GetPlan__factories.cpp.o[0m
[400.714s] [ 48%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/nav_msgs__srv__SetMap__factories.cpp.o[0m
[409.186s] [ 48%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs_factories.cpp.o[0m
[410.720s] [ 49%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__ModelCoefficients__factories.cpp.o[0m
[416.110s] [ 49%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__PointIndices__factories.cpp.o[0m
[423.761s] [ 49%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__PolygonMesh__factories.cpp.o[0m
[428.163s] [ 50%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__msg__Vertices__factories.cpp.o[0m
[438.369s] [ 50%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pcl_msgs__srv__UpdateFilename__factories.cpp.o[0m
[440.386s] [ 50%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs_factories.cpp.o[0m
[445.619s] [ 51%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__JointState__factories.cpp.o[0m
[446.589s] [ 51%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__JointCommand__factories.cpp.o[0m
[450.666s] [ 52%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/pendulum_msgs__msg__RttestResults__factories.cpp.o[0m
[451.342s] [ 52%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces_factories.cpp.o[0m
[454.688s] [ 52%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__FloatingPointRange__factories.cpp.o[0m
[455.338s] [ 53%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__IntegerRange__factories.cpp.o[0m
[458.616s] [ 53%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ListParametersResult__factories.cpp.o[0m
[459.389s] [ 53%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__Log__factories.cpp.o[0m
[462.558s] [ 54%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterDescriptor__factories.cpp.o[0m
[466.246s] [ 54%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterEventDescriptors__factories.cpp.o[0m
[467.816s] [ 54%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterEvent__factories.cpp.o[0m
[469.927s] [ 55%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__Parameter__factories.cpp.o[0m
[471.638s] [ 55%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterType__factories.cpp.o[0m
[474.145s] [ 55%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__ParameterValue__factories.cpp.o[0m
[477.678s] [ 56%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__msg__SetParametersResult__factories.cpp.o[0m
[480.705s] [ 56%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__DescribeParameters__factories.cpp.o[0m
[480.887s] [ 57%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__GetParameters__factories.cpp.o[0m
[484.404s] [ 57%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__GetParameterTypes__factories.cpp.o[0m
[484.405s] [ 57%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__ListParameters__factories.cpp.o[0m
[488.058s] [ 58%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__SetParametersAtomically__factories.cpp.o[0m
[488.204s] [ 58%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rcl_interfaces__srv__SetParameters__factories.cpp.o[0m
[491.595s] [ 58%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common_factories.cpp.o[0m
[491.860s] [ 59%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__Gid__factories.cpp.o[0m
[495.199s] [ 59%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__NodeEntitiesInfo__factories.cpp.o[0m
[495.560s] [ 59%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rmw_dds_common__msg__ParticipantEntitiesInfo__factories.cpp.o[0m
[498.758s] [ 60%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rosgraph_msgs_factories.cpp.o[0m
[499.289s] [ 60%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/rosgraph_msgs__msg__Clock__factories.cpp.o[0m
[502.652s] [ 60%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs_factories.cpp.o[0m
[506.844s] [ 61%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__BatteryState__factories.cpp.o[0m
[507.806s] [ 61%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__CameraInfo__factories.cpp.o[0m
[515.351s] [ 61%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__ChannelFloat32__factories.cpp.o[0m
[516.406s] [ 62%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__CompressedImage__factories.cpp.o[0m
[524.073s] [ 62%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__FluidPressure__factories.cpp.o[0m
[525.394s] [ 63%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Illuminance__factories.cpp.o[0m
[532.455s] [ 63%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Image__factories.cpp.o[0m
[533.852s] [ 63%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Imu__factories.cpp.o[0m
[541.286s] [ 64%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JointState__factories.cpp.o[0m
[542.180s] [ 64%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Joy__factories.cpp.o[0m
[550.196s] [ 64%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JoyFeedback__factories.cpp.o[0m
[550.940s] [ 65%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__JoyFeedbackArray__factories.cpp.o[0m
[558.832s] [ 65%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__LaserEcho__factories.cpp.o[0m
[559.418s] [ 65%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__LaserScan__factories.cpp.o[0m
[567.461s] [ 66%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MagneticField__factories.cpp.o[0m
[567.692s] [ 66%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MultiDOFJointState__factories.cpp.o[0m
[576.229s] [ 66%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__MultiEchoLaserScan__factories.cpp.o[0m
[576.545s] [ 67%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__NavSatFix__factories.cpp.o[0m
[585.046s] [ 67%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__NavSatStatus__factories.cpp.o[0m
[585.152s] [ 67%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointCloud__factories.cpp.o[0m
[593.791s] [ 68%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointCloud2__factories.cpp.o[0m
[595.809s] [ 68%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__PointField__factories.cpp.o[0m
[601.914s] [ 69%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Range__factories.cpp.o[0m
[604.154s] [ 69%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__RegionOfInterest__factories.cpp.o[0m
[610.706s] [ 69%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__RelativeHumidity__factories.cpp.o[0m
[612.759s] [ 70%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__Temperature__factories.cpp.o[0m
[619.220s] [ 70%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__msg__TimeReference__factories.cpp.o[0m
[626.062s] [ 70%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/sensor_msgs__srv__SetCameraInfo__factories.cpp.o[0m
[627.141s] [ 71%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs_factories.cpp.o[0m
[630.877s] [ 71%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__Mesh__factories.cpp.o[0m
[632.030s] [ 71%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__MeshTriangle__factories.cpp.o[0m
[641.076s] [ 72%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__Plane__factories.cpp.o[0m
[647.836s] [ 72%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/shape_msgs__msg__SolidPrimitive__factories.cpp.o[0m
[650.852s] [ 72%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs_factories.cpp.o[0m
[654.460s] [ 73%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__MetricsMessage__factories.cpp.o[0m
[655.386s] [ 73%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__StatisticDataPoint__factories.cpp.o[0m
[658.093s] [ 73%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/statistics_msgs__msg__StatisticDataType__factories.cpp.o[0m
[658.877s] [ 74%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs_factories.cpp.o[0m
[661.809s] [ 74%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Bool__factories.cpp.o[0m
[662.898s] [ 75%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Byte__factories.cpp.o[0m
[670.198s] [ 75%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__ByteMultiArray__factories.cpp.o[0m
[677.276s] [ 75%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Char__factories.cpp.o[0m
[680.305s] [ 76%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__ColorRGBA__factories.cpp.o[0m
[684.719s] [ 76%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Empty__factories.cpp.o[0m
[688.420s] [ 76%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float32__factories.cpp.o[0m
[692.912s] [ 77%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float32MultiArray__factories.cpp.o[0m
[696.878s] [ 77%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float64__factories.cpp.o[0m
[701.073s] [ 77%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Float64MultiArray__factories.cpp.o[0m
[705.294s] [ 78%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Header__factories.cpp.o[0m
[710.019s] [ 78%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int16__factories.cpp.o[0m
[713.821s] [ 78%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int16MultiArray__factories.cpp.o[0m
[718.817s] [ 79%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int32__factories.cpp.o[0m
[722.737s] [ 79%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int32MultiArray__factories.cpp.o[0m
[730.605s] [ 79%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int64__factories.cpp.o[0m
[730.906s] [ 80%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int64MultiArray__factories.cpp.o[0m
[738.604s] [ 80%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int8__factories.cpp.o[0m
[739.417s] [ 81%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__Int8MultiArray__factories.cpp.o[0m
[747.392s] [ 81%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__MultiArrayDimension__factories.cpp.o[0m
[748.411s] [ 81%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__MultiArrayLayout__factories.cpp.o[0m
[756.941s] [ 82%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__String__factories.cpp.o[0m
[763.981s] [ 82%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt16__factories.cpp.o[0m
[771.022s] [ 82%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt16MultiArray__factories.cpp.o[0m
[776.040s] [ 83%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt32__factories.cpp.o[0m
[778.624s] [ 83%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt32MultiArray__factories.cpp.o[0m
[784.279s] [ 83%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt64__factories.cpp.o[0m
[786.904s] [ 84%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt64MultiArray__factories.cpp.o[0m
[792.473s] [ 84%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt8__factories.cpp.o[0m
[795.198s] [ 84%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_msgs__msg__UInt8MultiArray__factories.cpp.o[0m
[800.667s] [ 85%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs_factories.cpp.o[0m
[803.438s] [ 85%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__Empty__factories.cpp.o[0m
[804.296s] [ 85%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__SetBool__factories.cpp.o[0m
[809.062s] [ 86%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/std_srvs__srv__Trigger__factories.cpp.o[0m
[809.715s] [ 86%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/stereo_msgs_factories.cpp.o[0m
[813.617s] [ 87%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/stereo_msgs__msg__DisparityImage__factories.cpp.o[0m
[814.738s] [ 87%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs_factories.cpp.o[0m
[818.875s] [ 87%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__msg__TF2Error__factories.cpp.o[0m
[821.973s] [ 88%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__msg__TFMessage__factories.cpp.o[0m
[827.211s] [ 88%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/tf2_msgs__srv__FrameGraph__factories.cpp.o[0m
[830.729s] [ 88%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs_factories.cpp.o[0m
[832.864s] [ 89%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__JointTrajectory__factories.cpp.o[0m
[834.575s] [ 89%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__JointTrajectoryPoint__factories.cpp.o[0m
[841.377s] [ 89%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__MultiDOFJointTrajectory__factories.cpp.o[0m
[843.149s] [ 90%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/trajectory_msgs__msg__MultiDOFJointTrajectoryPoint__factories.cpp.o[0m
[851.261s] [ 90%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim_factories.cpp.o[0m
[852.294s] [ 90%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__msg__Color__factories.cpp.o[0m
[855.656s] [ 91%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__msg__Pose__factories.cpp.o[0m
[856.430s] [ 91%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__Kill__factories.cpp.o[0m
[859.905s] [ 91%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__SetPen__factories.cpp.o[0m
[861.740s] [ 92%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__Spawn__factories.cpp.o[0m
[865.508s] [ 92%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__TeleportAbsolute__factories.cpp.o[0m
[867.308s] [ 93%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/turtlesim__srv__TeleportRelative__factories.cpp.o[0m
[871.050s] [ 93%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/unique_identifier_msgs_factories.cpp.o[0m
[872.572s] [ 93%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/unique_identifier_msgs__msg__UUID__factories.cpp.o[0m
[875.031s] [ 94%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs_factories.cpp.o[0m
[876.289s] [ 94%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__ImageMarker__factories.cpp.o[0m
[879.372s] [ 94%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarker__factories.cpp.o[0m
[885.171s] [ 95%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerControl__factories.cpp.o[0m
[888.929s] [ 95%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerFeedback__factories.cpp.o[0m
[894.558s] [ 95%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerInit__factories.cpp.o[0m
[900.714s] [ 96%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerPose__factories.cpp.o[0m
[907.025s] [ 96%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__InteractiveMarkerUpdate__factories.cpp.o[0m
[911.814s] [ 96%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__Marker__factories.cpp.o[0m
[923.688s] [ 97%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__MarkerArray__factories.cpp.o[0m
[925.700s] [ 97%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__msg__MenuEntry__factories.cpp.o[0m
[934.252s] [ 97%] [32mBuilding CXX object CMakeFiles/ros1_bridge.dir/generated/visualization_msgs__srv__GetInteractiveMarkers__factories.cpp.o[0m
[937.942s] [ 98%] [32m[1mLinking CXX shared library libros1_bridge.so[0m
[1001.837s] [ 98%] Built target ros1_bridge
[1001.869s] [35m[1mScanning dependencies of target static_bridge[0m
[1001.869s] [35m[1mScanning dependencies of target parameter_bridge[0m
[1002.223s] [ 99%] [32mBuilding CXX object CMakeFiles/parameter_bridge.dir/src/parameter_bridge.cpp.o[0m
[1002.224s] [ 99%] [32mBuilding CXX object CMakeFiles/static_bridge.dir/src/static_bridge.cpp.o[0m
[1007.519s] [100%] [32m[1mLinking CXX executable static_bridge[0m
[1007.698s] [100%] [32m[1mLinking CXX executable parameter_bridge[0m
[1042.417s] [100%] Built target static_bridge
[1042.451s] [35m[1mScanning dependencies of target dynamic_bridge[0m
[1042.535s] [100%] [32mBuilding CXX object CMakeFiles/dynamic_bridge.dir/src/dynamic_bridge.cpp.o[0m
[1042.807s] [100%] Built target parameter_bridge
[1047.240s] [100%] [32m[1mLinking CXX executable dynamic_bridge[0m
[1074.839s] [100%] Built target dynamic_bridge
[1074.918s] Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --build /home/<USER>/bridge_ws/build/ros1_bridge -- -j2 -l2
[1076.213s] Invoking command in '/home/<USER>/bridge_ws/build/ros1_bridge': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/bridge_ws/build/ros1_bridge
[1076.223s] -- Install configuration: ""
[1076.231s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.sh
[1076.233s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.dsv
[1076.234s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.sh
[1076.235s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.dsv
[1076.235s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge
[1076.236s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge/__init__.py
[1076.298s] Listing '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge'...
[1076.298s] Compiling '/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge/__init__.py'...
[1076.304s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/package_run_dependencies/ros1_bridge
[1076.306s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/parent_prefix_path/ros1_bridge
[1076.311s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.sh
[1076.312s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.dsv
[1076.313s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.sh
[1076.314s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.dsv
[1076.315s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.bash
[1076.315s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.sh
[1076.316s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.zsh
[1076.317s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.dsv
[1076.317s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.dsv
[1076.318s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/packages/ros1_bridge
[1076.319s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake
[1076.320s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake
[1076.321s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_include_directories-extras.cmake
[1076.322s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_libraries-extras.cmake
[1076.322s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig.cmake
[1076.323s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig-version.cmake
[1076.323s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.xml
[1076.325s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_1_to_2
[1076.329s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_1_to_2" to ""
[1076.329s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_2_to_1
[1076.335s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_2_to_1" to ""
[1076.335s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge
[1076.343s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge" to ""
[1076.344s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/libros1_bridge.so
[1076.662s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/libros1_bridge.so" to ""
[1076.662s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/static_bridge
[1076.665s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/static_bridge" to ""
[1076.666s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/parameter_bridge
[1076.668s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/parameter_bridge" to ""
[1076.668s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/dynamic_bridge
[1076.672s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/dynamic_bridge" to ""
[1076.673s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_client_cpp
[1076.684s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_client_cpp" to ""
[1076.684s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_server_cpp
[1076.687s] -- Set runtime path of "/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_server_cpp" to ""
[1076.689s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/generate_factories/ros1_bridge_generate_factories
[1076.690s] -- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake
[1076.691s] -- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake
[1076.691s] -- Up-to-date: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake
[1076.692s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include
[1076.692s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge
[1076.692s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory.hpp
[1076.693s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_builtin_interfaces.hpp
[1076.694s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory_interface.hpp
[1076.694s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/builtin_interfaces_factories.hpp
[1076.695s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_decl.hpp
[1076.696s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/bridge.hpp
[1076.696s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource
[1076.697s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.cpp.em
[1076.698s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_mappings.cpp.em
[1076.699s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.hpp.em
[1076.700s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_factory.cpp.em
[1076.700s] -- Installing: /home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/interface_factories.cpp.em
[1076.710s] Invoked command in '/home/<USER>/bridge_ws/build/ros1_bridge' returned '0': CMAKE_PREFIX_PATH=/opt/ros/foxy:${CMAKE_PREFIX_PATH} /usr/bin/cmake --install /home/<USER>/bridge_ws/build/ros1_bridge
