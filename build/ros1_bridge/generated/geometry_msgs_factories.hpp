// generated from ros1_bridge/resource/pkg_factories.hpp.em

#include <memory>
#include <string>

#include <ros1_bridge/factory.hpp>

// include ROS 1 messages
#include <geometry_msgs/Point.h>
#include <geometry_msgs/Point32.h>
#include <geometry_msgs/Polygon.h>
#include <geometry_msgs/Pose2D.h>
#include <geometry_msgs/Quaternion.h>
#include <geometry_msgs/Pose.h>
#include <geometry_msgs/PoseWithCovariance.h>
#include <geometry_msgs/Vector3.h>
#include <geometry_msgs/Accel.h>
#include <geometry_msgs/AccelWithCovariance.h>
#include <geometry_msgs/Inertia.h>
#include <geometry_msgs/Transform.h>
#include <geometry_msgs/Twist.h>
#include <geometry_msgs/TwistWithCovariance.h>
#include <geometry_msgs/Wrench.h>
#include <geometry_msgs/AccelStamped.h>
#include <geometry_msgs/AccelWithCovarianceStamped.h>
#include <geometry_msgs/InertiaStamped.h>
#include <geometry_msgs/PointStamped.h>
#include <geometry_msgs/PolygonStamped.h>
#include <geometry_msgs/PoseArray.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/PoseWithCovarianceStamped.h>
#include <geometry_msgs/QuaternionStamped.h>
#include <geometry_msgs/TransformStamped.h>
#include <geometry_msgs/TwistStamped.h>
#include <geometry_msgs/TwistWithCovarianceStamped.h>
#include <geometry_msgs/Vector3Stamped.h>
#include <geometry_msgs/WrenchStamped.h>

// include ROS 2 messages
#include <geometry_msgs/msg/point.hpp>
#include <geometry_msgs/msg/point32.hpp>
#include <geometry_msgs/msg/polygon.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <geometry_msgs/msg/pose.hpp>
#include <geometry_msgs/msg/pose_with_covariance.hpp>
#include <geometry_msgs/msg/vector3.hpp>
#include <geometry_msgs/msg/accel.hpp>
#include <geometry_msgs/msg/accel_with_covariance.hpp>
#include <geometry_msgs/msg/inertia.hpp>
#include <geometry_msgs/msg/transform.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_with_covariance.hpp>
#include <geometry_msgs/msg/wrench.hpp>
#include <geometry_msgs/msg/accel_stamped.hpp>
#include <geometry_msgs/msg/accel_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/inertia_stamped.hpp>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <geometry_msgs/msg/polygon_stamped.hpp>
#include <geometry_msgs/msg/pose_array.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/quaternion_stamped.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/twist_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/vector3_stamped.hpp>
#include <geometry_msgs/msg/wrench_stamped.hpp>

namespace ros1_bridge
{

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Accel(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__AccelStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__AccelWithCovariance(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__AccelWithCovarianceStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Inertia(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__InertiaStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Point(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Point32(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PointStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Polygon(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PolygonStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Pose(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Pose2D(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PoseArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PoseStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PoseWithCovariance(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__PoseWithCovarianceStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Quaternion(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__QuaternionStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Transform(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__TransformStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Twist(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__TwistStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__TwistWithCovariance(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__TwistWithCovarianceStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Vector3(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Vector3Stamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__Wrench(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_geometry_msgs__msg__WrenchStamped(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::unique_ptr<ServiceFactoryInterface>
get_service_factory_geometry_msgs(const std::string & ros_id, const std::string & package_name, const std::string & service_name);

// conversion functions for available interfaces

template<>
void
Factory<
  geometry_msgs::Point,
  geometry_msgs::msg::Point
>::convert_1_to_2(
  const geometry_msgs::Point & ros1_msg,
  geometry_msgs::msg::Point & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Point,
  geometry_msgs::msg::Point
>::convert_2_to_1(
  const geometry_msgs::msg::Point & ros2_msg,
  geometry_msgs::Point & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Point32,
  geometry_msgs::msg::Point32
>::convert_1_to_2(
  const geometry_msgs::Point32 & ros1_msg,
  geometry_msgs::msg::Point32 & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Point32,
  geometry_msgs::msg::Point32
>::convert_2_to_1(
  const geometry_msgs::msg::Point32 & ros2_msg,
  geometry_msgs::Point32 & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Polygon,
  geometry_msgs::msg::Polygon
>::convert_1_to_2(
  const geometry_msgs::Polygon & ros1_msg,
  geometry_msgs::msg::Polygon & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Polygon,
  geometry_msgs::msg::Polygon
>::convert_2_to_1(
  const geometry_msgs::msg::Polygon & ros2_msg,
  geometry_msgs::Polygon & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Pose2D,
  geometry_msgs::msg::Pose2D
>::convert_1_to_2(
  const geometry_msgs::Pose2D & ros1_msg,
  geometry_msgs::msg::Pose2D & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Pose2D,
  geometry_msgs::msg::Pose2D
>::convert_2_to_1(
  const geometry_msgs::msg::Pose2D & ros2_msg,
  geometry_msgs::Pose2D & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Quaternion,
  geometry_msgs::msg::Quaternion
>::convert_1_to_2(
  const geometry_msgs::Quaternion & ros1_msg,
  geometry_msgs::msg::Quaternion & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Quaternion,
  geometry_msgs::msg::Quaternion
>::convert_2_to_1(
  const geometry_msgs::msg::Quaternion & ros2_msg,
  geometry_msgs::Quaternion & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Pose,
  geometry_msgs::msg::Pose
>::convert_1_to_2(
  const geometry_msgs::Pose & ros1_msg,
  geometry_msgs::msg::Pose & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Pose,
  geometry_msgs::msg::Pose
>::convert_2_to_1(
  const geometry_msgs::msg::Pose & ros2_msg,
  geometry_msgs::Pose & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PoseWithCovariance,
  geometry_msgs::msg::PoseWithCovariance
>::convert_1_to_2(
  const geometry_msgs::PoseWithCovariance & ros1_msg,
  geometry_msgs::msg::PoseWithCovariance & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PoseWithCovariance,
  geometry_msgs::msg::PoseWithCovariance
>::convert_2_to_1(
  const geometry_msgs::msg::PoseWithCovariance & ros2_msg,
  geometry_msgs::PoseWithCovariance & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Vector3,
  geometry_msgs::msg::Vector3
>::convert_1_to_2(
  const geometry_msgs::Vector3 & ros1_msg,
  geometry_msgs::msg::Vector3 & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Vector3,
  geometry_msgs::msg::Vector3
>::convert_2_to_1(
  const geometry_msgs::msg::Vector3 & ros2_msg,
  geometry_msgs::Vector3 & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Accel,
  geometry_msgs::msg::Accel
>::convert_1_to_2(
  const geometry_msgs::Accel & ros1_msg,
  geometry_msgs::msg::Accel & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Accel,
  geometry_msgs::msg::Accel
>::convert_2_to_1(
  const geometry_msgs::msg::Accel & ros2_msg,
  geometry_msgs::Accel & ros1_msg);


template<>
void
Factory<
  geometry_msgs::AccelWithCovariance,
  geometry_msgs::msg::AccelWithCovariance
>::convert_1_to_2(
  const geometry_msgs::AccelWithCovariance & ros1_msg,
  geometry_msgs::msg::AccelWithCovariance & ros2_msg);

template<>
void
Factory<
  geometry_msgs::AccelWithCovariance,
  geometry_msgs::msg::AccelWithCovariance
>::convert_2_to_1(
  const geometry_msgs::msg::AccelWithCovariance & ros2_msg,
  geometry_msgs::AccelWithCovariance & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Inertia,
  geometry_msgs::msg::Inertia
>::convert_1_to_2(
  const geometry_msgs::Inertia & ros1_msg,
  geometry_msgs::msg::Inertia & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Inertia,
  geometry_msgs::msg::Inertia
>::convert_2_to_1(
  const geometry_msgs::msg::Inertia & ros2_msg,
  geometry_msgs::Inertia & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Transform,
  geometry_msgs::msg::Transform
>::convert_1_to_2(
  const geometry_msgs::Transform & ros1_msg,
  geometry_msgs::msg::Transform & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Transform,
  geometry_msgs::msg::Transform
>::convert_2_to_1(
  const geometry_msgs::msg::Transform & ros2_msg,
  geometry_msgs::Transform & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Twist,
  geometry_msgs::msg::Twist
>::convert_1_to_2(
  const geometry_msgs::Twist & ros1_msg,
  geometry_msgs::msg::Twist & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Twist,
  geometry_msgs::msg::Twist
>::convert_2_to_1(
  const geometry_msgs::msg::Twist & ros2_msg,
  geometry_msgs::Twist & ros1_msg);


template<>
void
Factory<
  geometry_msgs::TwistWithCovariance,
  geometry_msgs::msg::TwistWithCovariance
>::convert_1_to_2(
  const geometry_msgs::TwistWithCovariance & ros1_msg,
  geometry_msgs::msg::TwistWithCovariance & ros2_msg);

template<>
void
Factory<
  geometry_msgs::TwistWithCovariance,
  geometry_msgs::msg::TwistWithCovariance
>::convert_2_to_1(
  const geometry_msgs::msg::TwistWithCovariance & ros2_msg,
  geometry_msgs::TwistWithCovariance & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Wrench,
  geometry_msgs::msg::Wrench
>::convert_1_to_2(
  const geometry_msgs::Wrench & ros1_msg,
  geometry_msgs::msg::Wrench & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Wrench,
  geometry_msgs::msg::Wrench
>::convert_2_to_1(
  const geometry_msgs::msg::Wrench & ros2_msg,
  geometry_msgs::Wrench & ros1_msg);


template<>
void
Factory<
  geometry_msgs::AccelStamped,
  geometry_msgs::msg::AccelStamped
>::convert_1_to_2(
  const geometry_msgs::AccelStamped & ros1_msg,
  geometry_msgs::msg::AccelStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::AccelStamped,
  geometry_msgs::msg::AccelStamped
>::convert_2_to_1(
  const geometry_msgs::msg::AccelStamped & ros2_msg,
  geometry_msgs::AccelStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::AccelWithCovarianceStamped,
  geometry_msgs::msg::AccelWithCovarianceStamped
>::convert_1_to_2(
  const geometry_msgs::AccelWithCovarianceStamped & ros1_msg,
  geometry_msgs::msg::AccelWithCovarianceStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::AccelWithCovarianceStamped,
  geometry_msgs::msg::AccelWithCovarianceStamped
>::convert_2_to_1(
  const geometry_msgs::msg::AccelWithCovarianceStamped & ros2_msg,
  geometry_msgs::AccelWithCovarianceStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::InertiaStamped,
  geometry_msgs::msg::InertiaStamped
>::convert_1_to_2(
  const geometry_msgs::InertiaStamped & ros1_msg,
  geometry_msgs::msg::InertiaStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::InertiaStamped,
  geometry_msgs::msg::InertiaStamped
>::convert_2_to_1(
  const geometry_msgs::msg::InertiaStamped & ros2_msg,
  geometry_msgs::InertiaStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PointStamped,
  geometry_msgs::msg::PointStamped
>::convert_1_to_2(
  const geometry_msgs::PointStamped & ros1_msg,
  geometry_msgs::msg::PointStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PointStamped,
  geometry_msgs::msg::PointStamped
>::convert_2_to_1(
  const geometry_msgs::msg::PointStamped & ros2_msg,
  geometry_msgs::PointStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PolygonStamped,
  geometry_msgs::msg::PolygonStamped
>::convert_1_to_2(
  const geometry_msgs::PolygonStamped & ros1_msg,
  geometry_msgs::msg::PolygonStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PolygonStamped,
  geometry_msgs::msg::PolygonStamped
>::convert_2_to_1(
  const geometry_msgs::msg::PolygonStamped & ros2_msg,
  geometry_msgs::PolygonStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PoseArray,
  geometry_msgs::msg::PoseArray
>::convert_1_to_2(
  const geometry_msgs::PoseArray & ros1_msg,
  geometry_msgs::msg::PoseArray & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PoseArray,
  geometry_msgs::msg::PoseArray
>::convert_2_to_1(
  const geometry_msgs::msg::PoseArray & ros2_msg,
  geometry_msgs::PoseArray & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PoseStamped,
  geometry_msgs::msg::PoseStamped
>::convert_1_to_2(
  const geometry_msgs::PoseStamped & ros1_msg,
  geometry_msgs::msg::PoseStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PoseStamped,
  geometry_msgs::msg::PoseStamped
>::convert_2_to_1(
  const geometry_msgs::msg::PoseStamped & ros2_msg,
  geometry_msgs::PoseStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::PoseWithCovarianceStamped,
  geometry_msgs::msg::PoseWithCovarianceStamped
>::convert_1_to_2(
  const geometry_msgs::PoseWithCovarianceStamped & ros1_msg,
  geometry_msgs::msg::PoseWithCovarianceStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::PoseWithCovarianceStamped,
  geometry_msgs::msg::PoseWithCovarianceStamped
>::convert_2_to_1(
  const geometry_msgs::msg::PoseWithCovarianceStamped & ros2_msg,
  geometry_msgs::PoseWithCovarianceStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::QuaternionStamped,
  geometry_msgs::msg::QuaternionStamped
>::convert_1_to_2(
  const geometry_msgs::QuaternionStamped & ros1_msg,
  geometry_msgs::msg::QuaternionStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::QuaternionStamped,
  geometry_msgs::msg::QuaternionStamped
>::convert_2_to_1(
  const geometry_msgs::msg::QuaternionStamped & ros2_msg,
  geometry_msgs::QuaternionStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::TransformStamped,
  geometry_msgs::msg::TransformStamped
>::convert_1_to_2(
  const geometry_msgs::TransformStamped & ros1_msg,
  geometry_msgs::msg::TransformStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::TransformStamped,
  geometry_msgs::msg::TransformStamped
>::convert_2_to_1(
  const geometry_msgs::msg::TransformStamped & ros2_msg,
  geometry_msgs::TransformStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::TwistStamped,
  geometry_msgs::msg::TwistStamped
>::convert_1_to_2(
  const geometry_msgs::TwistStamped & ros1_msg,
  geometry_msgs::msg::TwistStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::TwistStamped,
  geometry_msgs::msg::TwistStamped
>::convert_2_to_1(
  const geometry_msgs::msg::TwistStamped & ros2_msg,
  geometry_msgs::TwistStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::TwistWithCovarianceStamped,
  geometry_msgs::msg::TwistWithCovarianceStamped
>::convert_1_to_2(
  const geometry_msgs::TwistWithCovarianceStamped & ros1_msg,
  geometry_msgs::msg::TwistWithCovarianceStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::TwistWithCovarianceStamped,
  geometry_msgs::msg::TwistWithCovarianceStamped
>::convert_2_to_1(
  const geometry_msgs::msg::TwistWithCovarianceStamped & ros2_msg,
  geometry_msgs::TwistWithCovarianceStamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::Vector3Stamped,
  geometry_msgs::msg::Vector3Stamped
>::convert_1_to_2(
  const geometry_msgs::Vector3Stamped & ros1_msg,
  geometry_msgs::msg::Vector3Stamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::Vector3Stamped,
  geometry_msgs::msg::Vector3Stamped
>::convert_2_to_1(
  const geometry_msgs::msg::Vector3Stamped & ros2_msg,
  geometry_msgs::Vector3Stamped & ros1_msg);


template<>
void
Factory<
  geometry_msgs::WrenchStamped,
  geometry_msgs::msg::WrenchStamped
>::convert_1_to_2(
  const geometry_msgs::WrenchStamped & ros1_msg,
  geometry_msgs::msg::WrenchStamped & ros2_msg);

template<>
void
Factory<
  geometry_msgs::WrenchStamped,
  geometry_msgs::msg::WrenchStamped
>::convert_2_to_1(
  const geometry_msgs::msg::WrenchStamped & ros2_msg,
  geometry_msgs::WrenchStamped & ros1_msg);

}  // namespace ros1_bridge
