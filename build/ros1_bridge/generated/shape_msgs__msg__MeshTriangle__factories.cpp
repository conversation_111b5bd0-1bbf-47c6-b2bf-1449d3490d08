// generated from ros1_bridge/resource/interface_factories.cpp.em

#include "shape_msgs_factories.hpp"

#include <algorithm>

#include "rclcpp/rclcpp.hpp"

// include builtin interfaces
#include <ros1_bridge/convert_builtin_interfaces.hpp>

// include ROS 1 services

// include ROS 2 services

namespace ros1_bridge
{

std::shared_ptr<FactoryInterface>
get_factory_shape_msgs__msg__MeshTriangle(const std::string & ros1_type_name, const std::string & ros2_type_name)
{
  // mapping from string to specialized template
  if (
    (ros1_type_name == "shape_msgs/MeshTriangle" ||
     ros1_type_name == "") &&
    ros2_type_name == "shape_msgs/msg/MeshTriangle")
  {
    return std::make_shared<
      Factory<
        shape_msgs::MeshTriangle,
        shape_msgs::msg::MeshTriangle
      >
    >("shape_msgs/MeshTriangle", ros2_type_name);
  }
  return std::shared_ptr<FactoryInterface>();
}

std::unique_ptr<ServiceFactoryInterface>
get_service_factory_shape_msgs__msg__MeshTriangle(const std::string & ros_id, const std::string & package_name, const std::string & service_name)
{
  (void)ros_id;
  (void)package_name;
  (void)service_name;
  return nullptr;
}
// conversion functions for available interfaces

template<>
void
Factory<
  shape_msgs::MeshTriangle,
  shape_msgs::msg::MeshTriangle
>::convert_1_to_2(
  const shape_msgs::MeshTriangle & ros1_msg,
  shape_msgs::msg::MeshTriangle & ros2_msg)
{

  // convert array or sequence field
  // statically sized array
  static_assert(
    (ros2_msg.vertex_indices.size()) >= (ros1_msg.vertex_indices.size()),
    "destination array not large enough for source array"
  );
  // convert primitive array elements
  std::copy(
    ros1_msg.vertex_indices.begin(),
    ros1_msg.vertex_indices.end(),
    ros2_msg.vertex_indices.begin());
}

template<>
void
Factory<
  shape_msgs::MeshTriangle,
  shape_msgs::msg::MeshTriangle
>::convert_2_to_1(
  const shape_msgs::msg::MeshTriangle & ros2_msg,
  shape_msgs::MeshTriangle & ros1_msg)
{

  // convert array or sequence field
  // statically sized array
  static_assert(
    (ros1_msg.vertex_indices.size()) >= (ros2_msg.vertex_indices.size()),
    "destination array not large enough for source array"
  );
  // convert primitive array elements
  std::copy(
    ros2_msg.vertex_indices.begin(),
    ros2_msg.vertex_indices.end(),
    ros1_msg.vertex_indices.begin());
}
}  // namespace ros1_bridge
