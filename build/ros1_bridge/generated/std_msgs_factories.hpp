// generated from ros1_bridge/resource/pkg_factories.hpp.em

#include <memory>
#include <string>

#include <ros1_bridge/factory.hpp>

// include ROS 1 messages
#include <std_msgs/Bool.h>
#include <std_msgs/Byte.h>
#include <std_msgs/Char.h>
#include <std_msgs/ColorRGBA.h>
#include <std_msgs/Empty.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Float64.h>
#include <std_msgs/Header.h>
#include <std_msgs/Int16.h>
#include <std_msgs/Int32.h>
#include <std_msgs/Int64.h>
#include <std_msgs/Int8.h>
#include <std_msgs/MultiArrayDimension.h>
#include <std_msgs/MultiArrayLayout.h>
#include <std_msgs/ByteMultiArray.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int16MultiArray.h>
#include <std_msgs/Int32MultiArray.h>
#include <std_msgs/Int64MultiArray.h>
#include <std_msgs/Int8MultiArray.h>
#include <std_msgs/String.h>
#include <std_msgs/UInt16.h>
#include <std_msgs/UInt16MultiArray.h>
#include <std_msgs/UInt32.h>
#include <std_msgs/UInt32MultiArray.h>
#include <std_msgs/UInt64.h>
#include <std_msgs/UInt64MultiArray.h>
#include <std_msgs/UInt8.h>
#include <std_msgs/UInt8MultiArray.h>

// include ROS 2 messages
#include <std_msgs/msg/bool.hpp>
#include <std_msgs/msg/byte.hpp>
#include <std_msgs/msg/char.hpp>
#include <std_msgs/msg/color_rgba.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/float32.hpp>
#include <std_msgs/msg/float64.hpp>
#include <std_msgs/msg/header.hpp>
#include <std_msgs/msg/int16.hpp>
#include <std_msgs/msg/int32.hpp>
#include <std_msgs/msg/int64.hpp>
#include <std_msgs/msg/int8.hpp>
#include <std_msgs/msg/multi_array_dimension.hpp>
#include <std_msgs/msg/multi_array_layout.hpp>
#include <std_msgs/msg/byte_multi_array.hpp>
#include <std_msgs/msg/float32_multi_array.hpp>
#include <std_msgs/msg/float64_multi_array.hpp>
#include <std_msgs/msg/int16_multi_array.hpp>
#include <std_msgs/msg/int32_multi_array.hpp>
#include <std_msgs/msg/int64_multi_array.hpp>
#include <std_msgs/msg/int8_multi_array.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/u_int16.hpp>
#include <std_msgs/msg/u_int16_multi_array.hpp>
#include <std_msgs/msg/u_int32.hpp>
#include <std_msgs/msg/u_int32_multi_array.hpp>
#include <std_msgs/msg/u_int64.hpp>
#include <std_msgs/msg/u_int64_multi_array.hpp>
#include <std_msgs/msg/u_int8.hpp>
#include <std_msgs/msg/u_int8_multi_array.hpp>

namespace ros1_bridge
{

std::shared_ptr<FactoryInterface>
get_factory_std_msgs(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Bool(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Byte(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__ByteMultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Char(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__ColorRGBA(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Empty(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Float32(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Float32MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Float64(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Float64MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Header(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int16(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int16MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int32(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int32MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int64(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int64MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int8(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__Int8MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__MultiArrayDimension(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__MultiArrayLayout(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__String(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt16(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt16MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt32(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt32MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt64(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt64MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt8(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::shared_ptr<FactoryInterface>
get_factory_std_msgs__msg__UInt8MultiArray(const std::string & ros1_type_name, const std::string & ros2_type_name);

std::unique_ptr<ServiceFactoryInterface>
get_service_factory_std_msgs(const std::string & ros_id, const std::string & package_name, const std::string & service_name);

// conversion functions for available interfaces

template<>
void
Factory<
  std_msgs::Bool,
  std_msgs::msg::Bool
>::convert_1_to_2(
  const std_msgs::Bool & ros1_msg,
  std_msgs::msg::Bool & ros2_msg);

template<>
void
Factory<
  std_msgs::Bool,
  std_msgs::msg::Bool
>::convert_2_to_1(
  const std_msgs::msg::Bool & ros2_msg,
  std_msgs::Bool & ros1_msg);


template<>
void
Factory<
  std_msgs::Byte,
  std_msgs::msg::Byte
>::convert_1_to_2(
  const std_msgs::Byte & ros1_msg,
  std_msgs::msg::Byte & ros2_msg);

template<>
void
Factory<
  std_msgs::Byte,
  std_msgs::msg::Byte
>::convert_2_to_1(
  const std_msgs::msg::Byte & ros2_msg,
  std_msgs::Byte & ros1_msg);


template<>
void
Factory<
  std_msgs::Char,
  std_msgs::msg::Char
>::convert_1_to_2(
  const std_msgs::Char & ros1_msg,
  std_msgs::msg::Char & ros2_msg);

template<>
void
Factory<
  std_msgs::Char,
  std_msgs::msg::Char
>::convert_2_to_1(
  const std_msgs::msg::Char & ros2_msg,
  std_msgs::Char & ros1_msg);


template<>
void
Factory<
  std_msgs::ColorRGBA,
  std_msgs::msg::ColorRGBA
>::convert_1_to_2(
  const std_msgs::ColorRGBA & ros1_msg,
  std_msgs::msg::ColorRGBA & ros2_msg);

template<>
void
Factory<
  std_msgs::ColorRGBA,
  std_msgs::msg::ColorRGBA
>::convert_2_to_1(
  const std_msgs::msg::ColorRGBA & ros2_msg,
  std_msgs::ColorRGBA & ros1_msg);


template<>
void
Factory<
  std_msgs::Empty,
  std_msgs::msg::Empty
>::convert_1_to_2(
  const std_msgs::Empty & ros1_msg,
  std_msgs::msg::Empty & ros2_msg);

template<>
void
Factory<
  std_msgs::Empty,
  std_msgs::msg::Empty
>::convert_2_to_1(
  const std_msgs::msg::Empty & ros2_msg,
  std_msgs::Empty & ros1_msg);


template<>
void
Factory<
  std_msgs::Float32,
  std_msgs::msg::Float32
>::convert_1_to_2(
  const std_msgs::Float32 & ros1_msg,
  std_msgs::msg::Float32 & ros2_msg);

template<>
void
Factory<
  std_msgs::Float32,
  std_msgs::msg::Float32
>::convert_2_to_1(
  const std_msgs::msg::Float32 & ros2_msg,
  std_msgs::Float32 & ros1_msg);


template<>
void
Factory<
  std_msgs::Float64,
  std_msgs::msg::Float64
>::convert_1_to_2(
  const std_msgs::Float64 & ros1_msg,
  std_msgs::msg::Float64 & ros2_msg);

template<>
void
Factory<
  std_msgs::Float64,
  std_msgs::msg::Float64
>::convert_2_to_1(
  const std_msgs::msg::Float64 & ros2_msg,
  std_msgs::Float64 & ros1_msg);


template<>
void
Factory<
  std_msgs::Header,
  std_msgs::msg::Header
>::convert_1_to_2(
  const std_msgs::Header & ros1_msg,
  std_msgs::msg::Header & ros2_msg);

template<>
void
Factory<
  std_msgs::Header,
  std_msgs::msg::Header
>::convert_2_to_1(
  const std_msgs::msg::Header & ros2_msg,
  std_msgs::Header & ros1_msg);


template<>
void
Factory<
  std_msgs::Int16,
  std_msgs::msg::Int16
>::convert_1_to_2(
  const std_msgs::Int16 & ros1_msg,
  std_msgs::msg::Int16 & ros2_msg);

template<>
void
Factory<
  std_msgs::Int16,
  std_msgs::msg::Int16
>::convert_2_to_1(
  const std_msgs::msg::Int16 & ros2_msg,
  std_msgs::Int16 & ros1_msg);


template<>
void
Factory<
  std_msgs::Int32,
  std_msgs::msg::Int32
>::convert_1_to_2(
  const std_msgs::Int32 & ros1_msg,
  std_msgs::msg::Int32 & ros2_msg);

template<>
void
Factory<
  std_msgs::Int32,
  std_msgs::msg::Int32
>::convert_2_to_1(
  const std_msgs::msg::Int32 & ros2_msg,
  std_msgs::Int32 & ros1_msg);


template<>
void
Factory<
  std_msgs::Int64,
  std_msgs::msg::Int64
>::convert_1_to_2(
  const std_msgs::Int64 & ros1_msg,
  std_msgs::msg::Int64 & ros2_msg);

template<>
void
Factory<
  std_msgs::Int64,
  std_msgs::msg::Int64
>::convert_2_to_1(
  const std_msgs::msg::Int64 & ros2_msg,
  std_msgs::Int64 & ros1_msg);


template<>
void
Factory<
  std_msgs::Int8,
  std_msgs::msg::Int8
>::convert_1_to_2(
  const std_msgs::Int8 & ros1_msg,
  std_msgs::msg::Int8 & ros2_msg);

template<>
void
Factory<
  std_msgs::Int8,
  std_msgs::msg::Int8
>::convert_2_to_1(
  const std_msgs::msg::Int8 & ros2_msg,
  std_msgs::Int8 & ros1_msg);


template<>
void
Factory<
  std_msgs::MultiArrayDimension,
  std_msgs::msg::MultiArrayDimension
>::convert_1_to_2(
  const std_msgs::MultiArrayDimension & ros1_msg,
  std_msgs::msg::MultiArrayDimension & ros2_msg);

template<>
void
Factory<
  std_msgs::MultiArrayDimension,
  std_msgs::msg::MultiArrayDimension
>::convert_2_to_1(
  const std_msgs::msg::MultiArrayDimension & ros2_msg,
  std_msgs::MultiArrayDimension & ros1_msg);


template<>
void
Factory<
  std_msgs::MultiArrayLayout,
  std_msgs::msg::MultiArrayLayout
>::convert_1_to_2(
  const std_msgs::MultiArrayLayout & ros1_msg,
  std_msgs::msg::MultiArrayLayout & ros2_msg);

template<>
void
Factory<
  std_msgs::MultiArrayLayout,
  std_msgs::msg::MultiArrayLayout
>::convert_2_to_1(
  const std_msgs::msg::MultiArrayLayout & ros2_msg,
  std_msgs::MultiArrayLayout & ros1_msg);


template<>
void
Factory<
  std_msgs::ByteMultiArray,
  std_msgs::msg::ByteMultiArray
>::convert_1_to_2(
  const std_msgs::ByteMultiArray & ros1_msg,
  std_msgs::msg::ByteMultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::ByteMultiArray,
  std_msgs::msg::ByteMultiArray
>::convert_2_to_1(
  const std_msgs::msg::ByteMultiArray & ros2_msg,
  std_msgs::ByteMultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Float32MultiArray,
  std_msgs::msg::Float32MultiArray
>::convert_1_to_2(
  const std_msgs::Float32MultiArray & ros1_msg,
  std_msgs::msg::Float32MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Float32MultiArray,
  std_msgs::msg::Float32MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Float32MultiArray & ros2_msg,
  std_msgs::Float32MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Float64MultiArray,
  std_msgs::msg::Float64MultiArray
>::convert_1_to_2(
  const std_msgs::Float64MultiArray & ros1_msg,
  std_msgs::msg::Float64MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Float64MultiArray,
  std_msgs::msg::Float64MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Float64MultiArray & ros2_msg,
  std_msgs::Float64MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Int16MultiArray,
  std_msgs::msg::Int16MultiArray
>::convert_1_to_2(
  const std_msgs::Int16MultiArray & ros1_msg,
  std_msgs::msg::Int16MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Int16MultiArray,
  std_msgs::msg::Int16MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Int16MultiArray & ros2_msg,
  std_msgs::Int16MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Int32MultiArray,
  std_msgs::msg::Int32MultiArray
>::convert_1_to_2(
  const std_msgs::Int32MultiArray & ros1_msg,
  std_msgs::msg::Int32MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Int32MultiArray,
  std_msgs::msg::Int32MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Int32MultiArray & ros2_msg,
  std_msgs::Int32MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Int64MultiArray,
  std_msgs::msg::Int64MultiArray
>::convert_1_to_2(
  const std_msgs::Int64MultiArray & ros1_msg,
  std_msgs::msg::Int64MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Int64MultiArray,
  std_msgs::msg::Int64MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Int64MultiArray & ros2_msg,
  std_msgs::Int64MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::Int8MultiArray,
  std_msgs::msg::Int8MultiArray
>::convert_1_to_2(
  const std_msgs::Int8MultiArray & ros1_msg,
  std_msgs::msg::Int8MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::Int8MultiArray,
  std_msgs::msg::Int8MultiArray
>::convert_2_to_1(
  const std_msgs::msg::Int8MultiArray & ros2_msg,
  std_msgs::Int8MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::String,
  std_msgs::msg::String
>::convert_1_to_2(
  const std_msgs::String & ros1_msg,
  std_msgs::msg::String & ros2_msg);

template<>
void
Factory<
  std_msgs::String,
  std_msgs::msg::String
>::convert_2_to_1(
  const std_msgs::msg::String & ros2_msg,
  std_msgs::String & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt16,
  std_msgs::msg::UInt16
>::convert_1_to_2(
  const std_msgs::UInt16 & ros1_msg,
  std_msgs::msg::UInt16 & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt16,
  std_msgs::msg::UInt16
>::convert_2_to_1(
  const std_msgs::msg::UInt16 & ros2_msg,
  std_msgs::UInt16 & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt16MultiArray,
  std_msgs::msg::UInt16MultiArray
>::convert_1_to_2(
  const std_msgs::UInt16MultiArray & ros1_msg,
  std_msgs::msg::UInt16MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt16MultiArray,
  std_msgs::msg::UInt16MultiArray
>::convert_2_to_1(
  const std_msgs::msg::UInt16MultiArray & ros2_msg,
  std_msgs::UInt16MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt32,
  std_msgs::msg::UInt32
>::convert_1_to_2(
  const std_msgs::UInt32 & ros1_msg,
  std_msgs::msg::UInt32 & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt32,
  std_msgs::msg::UInt32
>::convert_2_to_1(
  const std_msgs::msg::UInt32 & ros2_msg,
  std_msgs::UInt32 & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt32MultiArray,
  std_msgs::msg::UInt32MultiArray
>::convert_1_to_2(
  const std_msgs::UInt32MultiArray & ros1_msg,
  std_msgs::msg::UInt32MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt32MultiArray,
  std_msgs::msg::UInt32MultiArray
>::convert_2_to_1(
  const std_msgs::msg::UInt32MultiArray & ros2_msg,
  std_msgs::UInt32MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt64,
  std_msgs::msg::UInt64
>::convert_1_to_2(
  const std_msgs::UInt64 & ros1_msg,
  std_msgs::msg::UInt64 & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt64,
  std_msgs::msg::UInt64
>::convert_2_to_1(
  const std_msgs::msg::UInt64 & ros2_msg,
  std_msgs::UInt64 & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt64MultiArray,
  std_msgs::msg::UInt64MultiArray
>::convert_1_to_2(
  const std_msgs::UInt64MultiArray & ros1_msg,
  std_msgs::msg::UInt64MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt64MultiArray,
  std_msgs::msg::UInt64MultiArray
>::convert_2_to_1(
  const std_msgs::msg::UInt64MultiArray & ros2_msg,
  std_msgs::UInt64MultiArray & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt8,
  std_msgs::msg::UInt8
>::convert_1_to_2(
  const std_msgs::UInt8 & ros1_msg,
  std_msgs::msg::UInt8 & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt8,
  std_msgs::msg::UInt8
>::convert_2_to_1(
  const std_msgs::msg::UInt8 & ros2_msg,
  std_msgs::UInt8 & ros1_msg);


template<>
void
Factory<
  std_msgs::UInt8MultiArray,
  std_msgs::msg::UInt8MultiArray
>::convert_1_to_2(
  const std_msgs::UInt8MultiArray & ros1_msg,
  std_msgs::msg::UInt8MultiArray & ros2_msg);

template<>
void
Factory<
  std_msgs::UInt8MultiArray,
  std_msgs::msg::UInt8MultiArray
>::convert_2_to_1(
  const std_msgs::msg::UInt8MultiArray & ros2_msg,
  std_msgs::UInt8MultiArray & ros1_msg);

}  // namespace ros1_bridge
