// generated from ros1_bridge/resource/interface_factories.cpp.em

#include "visualization_msgs_factories.hpp"

#include <algorithm>

#include "rclcpp/rclcpp.hpp"

// include builtin interfaces
#include <ros1_bridge/convert_builtin_interfaces.hpp>

// include ROS 1 services

// include ROS 2 services

namespace ros1_bridge
{

std::shared_ptr<FactoryInterface>
get_factory_visualization_msgs__msg__InteractiveMarkerUpdate(const std::string & ros1_type_name, const std::string & ros2_type_name)
{
  // mapping from string to specialized template
  if (
    (ros1_type_name == "visualization_msgs/InteractiveMarkerUpdate" ||
     ros1_type_name == "") &&
    ros2_type_name == "visualization_msgs/msg/InteractiveMarkerUpdate")
  {
    return std::make_shared<
      Factory<
        visualization_msgs::InteractiveMarkerUpdate,
        visualization_msgs::msg::InteractiveMarkerUpdate
      >
    >("visualization_msgs/InteractiveMarkerUpdate", ros2_type_name);
  }
  return std::shared_ptr<FactoryInterface>();
}

std::unique_ptr<ServiceFactoryInterface>
get_service_factory_visualization_msgs__msg__InteractiveMarkerUpdate(const std::string & ros_id, const std::string & package_name, const std::string & service_name)
{
  (void)ros_id;
  (void)package_name;
  (void)service_name;
  return nullptr;
}
// conversion functions for available interfaces

template<>
void
Factory<
  visualization_msgs::InteractiveMarkerUpdate,
  visualization_msgs::msg::InteractiveMarkerUpdate
>::convert_1_to_2(
  const visualization_msgs::InteractiveMarkerUpdate & ros1_msg,
  visualization_msgs::msg::InteractiveMarkerUpdate & ros2_msg)
{

  // convert non-array field
  // convert primitive field
  ros2_msg.server_id = ros1_msg.server_id;

  // convert non-array field
  // convert primitive field
  ros2_msg.seq_num = ros1_msg.seq_num;

  // convert non-array field
  // convert primitive field
  ros2_msg.type = ros1_msg.type;

  // convert array or sequence field
  // dynamically sized sequence, ensure destination sequence/vector size is large enough
  // resize ros2 field to match the ros1 field
  ros2_msg.markers.resize(ros1_msg.markers.size());
  // copy element wise since the type is different
  {
    auto ros1_it = ros1_msg.markers.cbegin();
    auto ros2_it = ros2_msg.markers.begin();
    for (
      ;
      ros1_it != ros1_msg.markers.cend() &&
      ros2_it != ros2_msg.markers.end();
      ++ros1_it, ++ros2_it
    )
    {
      // convert sub message element
      Factory<
        visualization_msgs::InteractiveMarker,
        visualization_msgs::msg::InteractiveMarker
      >::convert_1_to_2(
        *ros1_it, *ros2_it);
    }
  }

  // convert array or sequence field
  // dynamically sized sequence, ensure destination sequence/vector size is large enough
  // resize ros2 field to match the ros1 field
  ros2_msg.poses.resize(ros1_msg.poses.size());
  // copy element wise since the type is different
  {
    auto ros1_it = ros1_msg.poses.cbegin();
    auto ros2_it = ros2_msg.poses.begin();
    for (
      ;
      ros1_it != ros1_msg.poses.cend() &&
      ros2_it != ros2_msg.poses.end();
      ++ros1_it, ++ros2_it
    )
    {
      // convert sub message element
      Factory<
        visualization_msgs::InteractiveMarkerPose,
        visualization_msgs::msg::InteractiveMarkerPose
      >::convert_1_to_2(
        *ros1_it, *ros2_it);
    }
  }

  // convert array or sequence field
  // dynamically sized sequence, ensure destination sequence/vector size is large enough
  // resize ros2 field to match the ros1 field
  ros2_msg.erases.resize(ros1_msg.erases.size());
  // convert primitive array elements
  std::copy(
    ros1_msg.erases.begin(),
    ros1_msg.erases.end(),
    ros2_msg.erases.begin());
}

template<>
void
Factory<
  visualization_msgs::InteractiveMarkerUpdate,
  visualization_msgs::msg::InteractiveMarkerUpdate
>::convert_2_to_1(
  const visualization_msgs::msg::InteractiveMarkerUpdate & ros2_msg,
  visualization_msgs::InteractiveMarkerUpdate & ros1_msg)
{

  // convert non-array field
  // convert primitive field
  ros1_msg.server_id = ros2_msg.server_id;

  // convert non-array field
  // convert primitive field
  ros1_msg.seq_num = ros2_msg.seq_num;

  // convert non-array field
  // convert primitive field
  ros1_msg.type = ros2_msg.type;

  // convert array or sequence field
  // dynamically sized sequence, ensure destination vector size is large enough
  // resize ros1 field to match the ros2 field
  ros1_msg.markers.resize(ros2_msg.markers.size());
  // copy element wise since the type is different
  {
    auto ros2_it = ros2_msg.markers.cbegin();
    auto ros1_it = ros1_msg.markers.begin();
    for (
      ;
      ros2_it != ros2_msg.markers.cend() &&
      ros1_it != ros1_msg.markers.end();
      ++ros2_it, ++ros1_it
    )
    {
      // convert sub message element
      Factory<
        visualization_msgs::InteractiveMarker,
        visualization_msgs::msg::InteractiveMarker
      >::convert_2_to_1(
        *ros2_it, *ros1_it);
    }
  }

  // convert array or sequence field
  // dynamically sized sequence, ensure destination vector size is large enough
  // resize ros1 field to match the ros2 field
  ros1_msg.poses.resize(ros2_msg.poses.size());
  // copy element wise since the type is different
  {
    auto ros2_it = ros2_msg.poses.cbegin();
    auto ros1_it = ros1_msg.poses.begin();
    for (
      ;
      ros2_it != ros2_msg.poses.cend() &&
      ros1_it != ros1_msg.poses.end();
      ++ros2_it, ++ros1_it
    )
    {
      // convert sub message element
      Factory<
        visualization_msgs::InteractiveMarkerPose,
        visualization_msgs::msg::InteractiveMarkerPose
      >::convert_2_to_1(
        *ros2_it, *ros1_it);
    }
  }

  // convert array or sequence field
  // dynamically sized sequence, ensure destination vector size is large enough
  // resize ros1 field to match the ros2 field
  ros1_msg.erases.resize(ros2_msg.erases.size());
  // convert primitive array elements
  std::copy(
    ros2_msg.erases.begin(),
    ros2_msg.erases.end(),
    ros1_msg.erases.begin());
}
}  // namespace ros1_bridge
