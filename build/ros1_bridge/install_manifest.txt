/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.sh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/library_path.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.sh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/pythonpath.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/lib/python3.8/site-packages/ros1_bridge/__init__.py
/home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/package_run_dependencies/ros1_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/parent_prefix_path/ros1_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.sh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/ament_prefix_path.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.sh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/environment/path.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.bash
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.sh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.zsh
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/local_setup.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.dsv
/home/<USER>/bridge_ws/install/ros1_bridge/share/ament_index/resource_index/packages/ros1_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_include_directories-extras.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ament_cmake_export_libraries-extras.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/ros1_bridgeConfig-version.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/package.xml
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_1_to_2
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge_2_to_1
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/simple_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/lib/libros1_bridge.so
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/static_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/parameter_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/dynamic_bridge
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_client_cpp
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/test_ros2_server_cpp
/home/<USER>/bridge_ws/install/ros1_bridge/lib/ros1_bridge/generate_factories/ros1_bridge_generate_factories
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_interface_packages.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/cmake/find_ros1_package.cmake
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_builtin_interfaces.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/factory_interface.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/builtin_interfaces_factories.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/convert_decl.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/include/ros1_bridge/bridge.hpp
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.cpp.em
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_mappings.cpp.em
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/pkg_factories.hpp.em
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/get_factory.cpp.em
/home/<USER>/bridge_ws/install/ros1_bridge/share/ros1_bridge/resource/interface_factories.cpp.em