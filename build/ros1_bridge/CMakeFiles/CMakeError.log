Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_4c397/fast && /usr/bin/make -f CMakeFiles/cmTC_4c397.dir/build.make CMakeFiles/cmTC_4c397.dir/build
make[1]: Entering directory '/home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_4c397.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_4c397.dir/src.c.o   -c /home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_4c397
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_4c397.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_4c397.dir/src.c.o  -o cmTC_4c397 
/usr/bin/ld: CMakeFiles/cmTC_4c397.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_4c397.dir/build.make:87: cmTC_4c397] Error 1
make[1]: Leaving directory '/home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_4c397/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_2688e/fast && /usr/bin/make -f CMakeFiles/cmTC_2688e.dir/build.make CMakeFiles/cmTC_2688e.dir/build
make[1]: Entering directory '/home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_2688e.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_2688e.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_2688e
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_2688e.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_2688e.dir/CheckFunctionExists.c.o  -o cmTC_2688e  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_2688e.dir/build.make:87: cmTC_2688e] Error 1
make[1]: Leaving directory '/home/<USER>/bridge_ws/build/ros1_bridge/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_2688e/fast] Error 2



