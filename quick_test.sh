#!/bin/bash

# Quick test script to verify ROS bridge setup
echo "=== ROS Bridge Quick Test ==="

# Check if we're on ROS 1 or ROS 2 system
if [ -f "/opt/ros/noetic/setup.bash" ] && [ -f "/opt/ros/humble/setup.bash" ]; then
    echo "Detected: Ubuntu 22.04 with both ROS 1 and ROS 2"
    ROS_SETUP="both"
elif [ -f "/opt/ros/noetic/setup.bash" ]; then
    echo "Detected: Ubuntu 20.04 with ROS 1 Noetic"
    ROS_SETUP="ros1"
elif [ -f "/opt/ros/humble/setup.bash" ]; then
    echo "Detected: Ubuntu 22.04 with ROS 2 Humble"
    ROS_SETUP="ros2"
else
    echo "Error: No ROS installation detected!"
    exit 1
fi

echo ""

# Test network connectivity
echo "Testing network connectivity..."
if ping -c 1 ************* > /dev/null 2>&1; then
    echo "✅ Can reach Ubuntu 20.04 VM (*************)"
else
    echo "❌ Cannot reach Ubuntu 20.04 VM (*************)"
fi

if ping -c 1 ************* > /dev/null 2>&1; then
    echo "✅ Can reach Ubuntu 22.04 VM (*************)"
else
    echo "❌ Cannot reach Ubuntu 22.04 VM (*************)"
fi

echo ""

# Test ROS bridge server
echo "Testing ROS bridge server..."
if curl -s --max-time 3 http://*************:9090 > /dev/null 2>&1; then
    echo "✅ ROSBridge server is running on *************:9090"
else
    echo "❌ ROSBridge server not accessible"
fi

echo ""

# Test ROS functionality based on system
if [ "$ROS_SETUP" = "ros1" ]; then
    echo "Testing ROS 1 functionality..."
    export ROS_MASTER_URI=http://*************:11311
    source /opt/ros/noetic/setup.bash
    
    if rostopic list > /dev/null 2>&1; then
        echo "✅ ROS 1 Master is running"
        echo "Available topics:"
        rostopic list | head -10
    else
        echo "❌ ROS 1 Master not accessible"
    fi

elif [ "$ROS_SETUP" = "ros2" ]; then
    echo "Testing ROS 2 functionality..."
    source /opt/ros/humble/setup.bash
    
    if ros2 topic list > /dev/null 2>&1; then
        echo "✅ ROS 2 is working"
        echo "Available topics:"
        ros2 topic list | head -10
    else
        echo "❌ ROS 2 not working properly"
    fi

elif [ "$ROS_SETUP" = "both" ]; then
    echo "Testing ros1_bridge setup..."
    if [ -d "~/ros1_bridge_ws" ]; then
        echo "✅ ros1_bridge workspace exists"
    else
        echo "❌ ros1_bridge workspace not found"
    fi
fi

echo ""
echo "=== Test Complete ==="
