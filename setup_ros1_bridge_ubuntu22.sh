#!/bin/bash

# Setup script for ros1_bridge on Ubuntu 22.04 with ROS 2 Humble
# This script installs ROS 1 Noetic alongside ROS 2 Humble and builds ros1_bridge

set -e

echo "=== ROS1 Bridge Setup for Ubuntu 22.04 ==="
echo "This script will install ROS 1 Noetic alongside ROS 2 Humble"
echo "and build the ros1_bridge package."
echo ""

# Update system
echo "Updating system packages..."
sudo apt update

# Install ROS 1 Noetic
echo "Installing ROS 1 Noetic..."

# Add ROS 1 repository
sudo sh -c 'echo "deb http://packages.ros.org/ros/ubuntu $(lsb_release -sc) main" > /etc/apt/sources.list.d/ros-latest.list'
curl -s https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc | sudo apt-key add -

# Update package list
sudo apt update

# Install ROS 1 Noetic desktop full
sudo apt install -y ros-noetic-desktop-full

# Install additional ROS 1 dependencies
sudo apt install -y python3-rosdep python3-rosinstall python3-rosinstall-generator python3-wstool build-essential

# Initialize rosdep if not already done
if [ ! -f /etc/ros/rosdep/sources.list.d/20-default.list ]; then
    sudo rosdep init
fi
rosdep update

# Install ROS 2 development tools (if not already installed)
echo "Installing ROS 2 development tools..."
sudo apt install -y ros-humble-desktop-full
sudo apt install -y python3-colcon-common-extensions
sudo apt install -y ros-dev-tools

# Create workspace for ros1_bridge
echo "Creating ros1_bridge workspace..."
mkdir -p ~/ros1_bridge_ws/src
cd ~/ros1_bridge_ws

# Clone ros1_bridge
echo "Cloning ros1_bridge..."
cd src
git clone https://github.com/ros2/ros1_bridge.git -b humble

# Install dependencies
echo "Installing dependencies..."
cd ~/ros1_bridge_ws
rosdep install --from-paths src --ignore-src -r -y

# Build ros1_bridge
echo "Building ros1_bridge..."
echo "This may take 10-15 minutes..."

# Source both ROS distributions
source /opt/ros/noetic/setup.bash
source /opt/ros/humble/setup.bash

# Build with colcon
colcon build --packages-select ros1_bridge --cmake-force-configure

echo ""
echo "=== Setup Complete! ==="
echo ""
echo "To use the bridge:"
echo "1. Terminal 1 (ROS 1): source /opt/ros/noetic/setup.bash && roscore"
echo "2. Terminal 2 (Bridge): source /opt/ros/noetic/setup.bash && source /opt/ros/humble/setup.bash && source ~/ros1_bridge_ws/install/setup.bash && ros2 run ros1_bridge dynamic_bridge"
echo "3. Terminal 3 (ROS 2): source /opt/ros/humble/setup.bash && ros2 topic list"
echo ""
echo "Network setup for multi-VM:"
echo "- Set ROS_MASTER_URI=http://*************:11311 (Ubuntu 20.04 VM IP)"
echo "- Set ROS_IP=************* (Ubuntu 22.04 VM IP)"
echo ""
